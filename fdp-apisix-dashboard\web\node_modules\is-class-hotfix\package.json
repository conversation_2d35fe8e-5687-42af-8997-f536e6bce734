{"name": "is-class-hotfix", "version": "0.0.6", "description": "Check if function is an ES6 class.", "main": "is-class.js", "directories": {"test": "test"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/miguelmota/is-class"}, "keywords": ["predicate", "function", "class", "es6"], "author": "<PERSON> <<EMAIL>> (http://www.miguelmota.com/)", "license": "MIT", "bugs": {"url": "https://github.com/miguelmota/is-class/issues"}, "homepage": "https://github.com/miguelmota/is-class", "devDependencies": {"tape": "^3.0.3"}}