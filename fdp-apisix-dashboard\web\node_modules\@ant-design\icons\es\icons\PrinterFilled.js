// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PrinterFilledSvg from "@ant-design/icons-svg/es/asn/PrinterFilled";
import AntdIcon from '../components/AntdIcon';

var PrinterFilled = function PrinterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PrinterFilledSvg
  }));
};

PrinterFilled.displayName = 'PrinterFilled';
export default /*#__PURE__*/React.forwardRef(PrinterFilled);