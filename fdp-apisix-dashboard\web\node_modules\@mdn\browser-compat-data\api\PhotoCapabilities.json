{"api": {"PhotoCapabilities": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PhotoCapabilities", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "fillLightMode": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PhotoCapabilities/fillLightMode", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "imageHeight": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PhotoCapabilities/imageHeight", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "imageWidth": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PhotoCapabilities/imageWidth", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "redEyeReduction": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PhotoCapabilities/redEyeReduction", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}