{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["../../../../src/registry/router/manhattan/router.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,sCAA2C;AAC3C,8CAAoD;AAGpD,2CAAwC;AACxC,+CAA4C;AAC5C,2CAA8B;AAC9B,qCAIkB;AAElB;;GAEG;AACH,SAAS,SAAS,CAChB,QAAkB,EAClB,IAAuB,EACvB,EAAqB,EACrB,GAAgB,EAChB,OAAwB;IAExB,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;IAEnC,IAAI,cAAc,CAAA;IAClB,IAAI,cAAc,CAAA;IAElB,IAAI,oBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC/B,cAAc,GAAG,IAAI,CAAC,KAAK,CACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,EACjD,SAAS,CACV,CAAA;KACF;SAAM;QACL,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAA;KACrD;IAED,IAAI,oBAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;QAC7B,cAAc,GAAG,IAAI,CAAC,KAAK,CACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,EACjD,SAAS,CACV,CAAA;KACF;SAAM;QACL,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAA;KACnD;IAED,2BAA2B;IAC3B,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,CAAA;IAEvE,0BAA0B;IAC1B,0BAA0B;IAE1B,IAAM,UAAU,GAAG,cAAc,CAAA;IACjC,IAAM,QAAQ,GAAG,cAAc,CAAA;IAC/B,IAAI,WAAW,CAAA;IACf,IAAI,SAAS,CAAA;IAEb,IAAI,oBAAS,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC/B,WAAW,GAAG,IAAI,CAAC,aAAa,CAC9B,UAAU,EACV,IAAI,EACJ,OAAO,CAAC,eAAe,EACvB,IAAI,EACJ,OAAO,CACR,CAAA;KACF;SAAM;QACL,WAAW,GAAG,CAAC,UAAU,CAAC,CAAA;KAC3B;IAED,IAAI,oBAAS,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;QAC7B,SAAS,GAAG,IAAI,CAAC,aAAa,CAC5B,cAAc,EACd,EAAE,EACF,OAAO,CAAC,aAAa,EACrB,IAAI,EACJ,OAAO,CACR,CAAA;KACF;SAAM;QACL,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAA;KACvB;IAED,4EAA4E;IAC5E,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAnB,CAAmB,CAAC,CAAA;IAC5D,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAnB,CAAmB,CAAC,CAAA;IAExD,oDAAoD;IACpD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;QAClD,IAAM,OAAO,GAAG,IAAI,sBAAS,EAAE,CAAA;QAC/B,2DAA2D;QAC3D,IAAM,MAAM,GAAoB,EAAE,CAAA;QAClC,kEAAkE;QAClE,IAAM,OAAO,GAAoB,EAAE,CAAA;QACnC,oDAAoD;QACpD,IAAM,KAAK,GAAqB,EAAE,CAAA;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YACrD,8CAA8C;YAC9C,IAAM,YAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;YACjC,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAU,CAAC,CAAA;YACnC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,YAAU,EAAE,SAAS,CAAC,CAAC,CAAA;YACrD,MAAM,CAAC,GAAG,CAAC,GAAG,YAAU,CAAA;YACxB,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACf;QAED,IAAM,2BAA2B,GAAG,OAAO,CAAC,sBAAsB,CAAA;QAClE,4BAA4B;QAC5B,IAAM,eAAe,GAAG,2BAA2B,KAAK,SAAS,CAAA;QAEjE,aAAa;QACb,IAAI,SAAS,SAAA,CAAA;QACb,IAAI,eAAe,SAAA,CAAA;QACnB,IAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACrD,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAA;QACvC,IAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAW,UAAC,GAAG,EAAE,QAAQ;YAC7D,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YACjC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAAE,CAAC,CAAA;QAEN,0BAA0B;QAC1B,IAAM,kBAAkB,GAAG,gBAAK,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAAA;QACpE,IAAI,cAAc,GAAG,OAAO,CAAC,YAAY,CAAA;QACzC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,cAAc,GAAG,CAAC,EAAE;YAC/C,0CAA0C;YAC1C,IAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAG,CAAA;YACjC,IAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;YACvC,IAAM,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;YACzC,IAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAA;YAErC,IAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;YACpD,IAAM,gBAAgB,GAAG,aAAa,IAAI,IAAI,CAAA;YAE9C,IAAI,sBAAsB,SAA2B,CAAA;YACrD,IAAI,CAAC,gBAAgB,EAAE;gBACrB,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAC7C,aAAa,EACb,YAAY,EACZ,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;aACF;iBAAM,IAAI,CAAC,eAAe,EAAE;gBAC3B,wBAAwB;gBACxB,sBAAsB,GAAG,2BAA2B,CAAA;aACrD;iBAAM,IAAI,CAAC,YAAY,EAAE;gBACxB,iCAAiC;gBACjC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAC7C,UAAU,EACV,YAAY,EACZ,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;aACF;iBAAM;gBACL,sBAAsB,GAAG,IAAI,CAAA;aAC9B;YAED,mCAAmC;YACnC,IAAM,YAAY,GAAG,gBAAgB,IAAI,kBAAkB,CAAA;YAC3D,IAAI,CAAC,YAAY,IAAI,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC3D,OAAO,CAAC,sBAAsB,GAAG,sBAAsB,CAAA;gBACvD,OAAO,IAAI,CAAC,gBAAgB,CAC1B,OAAO,EACP,MAAM,EACN,YAAY,EACZ,UAAU,EACV,QAAQ,CACT,CAAA;aACF;YAED,qDAAqD;YACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE;gBACzC,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;gBAEzB,IAAM,cAAc,GAAG,SAAS,CAAC,KAAM,CAAA;gBACvC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CACvC,sBAAuB,EACvB,cAAc,CACf,CAAA;gBAED,uCAAuC;gBACvC,IACE,CAAC,CAAC,eAAe,IAAI,YAAY,CAAC;oBAClC,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAC5C;oBACA,SAAQ;iBACT;gBAED,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAC9B,YAAY;qBACT,KAAK,EAAE;qBACP,SAAS,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,EAAE,SAAS,CAAC,WAAW,IAAI,CAAC,CAAC,EACpE,IAAI,EACJ,SAAS,CACV,CAAA;gBACD,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;gBAE9C,wCAAwC;gBACxC,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE;oBACpE,SAAQ;iBACT;gBAED,4BAA4B;gBAC5B,IAAI,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC3C,IAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;oBACjD,IAAI,CAAC,UAAU,EAAE;wBACf,IAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAC9C,aAAa,EACb,QAAQ,EACR,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAA;wBAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAChD,cAAc,EACd,iBAAiB,CAClB,CAAA;wBAED,IAAI,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,EAAE;4BACnD,SAAQ;yBACT;qBACF;iBACF;gBAED,+BAA+B;gBAC/B,+BAA+B;gBAE/B,IAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAA;gBACnC,IAAM,eAAe,GAAG,YAAY;oBAClC,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,CAAA;gBACtC,IAAM,aAAa,GAAG,WAAW,GAAG,YAAY,GAAG,eAAe,CAAA;gBAElE,2DAA2D;gBAC3D,2DAA2D;gBAC3D,IACE,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC5B,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,EAClC;oBACA,MAAM,CAAC,WAAW,CAAC,GAAG,aAAa,CAAA;oBACnC,OAAO,CAAC,WAAW,CAAC,GAAG,YAAY,CAAA;oBACnC,KAAK,CAAC,WAAW,CAAC,GAAG,aAAa,CAAA;oBAClC,OAAO,CAAC,GAAG,CACT,WAAW,EACX,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CACvD,CAAA;iBACF;aACF;YAED,cAAc,IAAI,CAAC,CAAA;SACpB;KACF;IAED,IAAI,OAAO,CAAC,aAAa,EAAE;QACzB,OAAO,kBAAW,CAAC,IAAI,CACrB,OAAO,CAAC,aAAa,EACrB,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,OAAO,CACR,CAAA;KACF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAEM,IAAM,MAAM,GAA8C,UAC/D,QAAQ,EACR,UAAU,EACV,QAAQ;IAER,IAAM,OAAO,GAAG,wBAAc,CAAC,UAAU,CAAC,CAAA;IAC1C,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACxD,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACxD,IAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAEhE,cAAc;IACd,IAAM,GAAG,GAAG,IAAI,0BAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CACxC,QAAQ,CAAC,KAAK,CAAC,KAAK,EACpB,QAAQ,CAAC,IAAI,CACd,CAAA;IAED,IAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,gBAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;IACxD,IAAM,WAAW,GAAY,EAAE,CAAA;IAE/B,2DAA2D;IAC3D,IAAI,SAAS,GAAG,cAAc,CAAA;IAE9B,IAAI,IAAI,CAAA;IACR,IAAI,EAAE,CAAA;IAEN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QAC1D,IAAI,YAAY,GAAmB,IAAI,CAAA;QAEvC,IAAI,GAAG,EAAE,IAAI,UAAU,CAAA;QACvB,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAEnB,6BAA6B;QAC7B,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,EAAE,GAAG,UAAU,CAAA;YAEf,yDAAyD;YACzD,0DAA0D;YAC1D,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;YAC1B,IAAM,eAAe,GACnB,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,IAAI,CAAA;YAElE,IAAI,eAAe,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;gBACnE,IAAM,QAAQ,GAAG,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAA;gBAC5D,IAAM,MAAM,GAAG,EAAE,CAAC,SAAS,EAAE,CAAA;gBAC7B,YAAY,GAAG,kBAAW,CAAC,IAAI,CAC7B,OAAO,CAAC,cAAc,EACtB,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,CACR,CAAA;aACF;SACF;QAED,yBAAyB;QACzB,IAAI,YAAY,IAAI,IAAI,EAAE;YACxB,YAAY,GAAG,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;SAC3D;QAED,kCAAkC;QAClC,IAAI,YAAY,KAAK,IAAI,EAAE;YACzB,OAAO,kBAAW,CAAC,IAAI,CACrB,OAAO,CAAC,cAAc,EACtB,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,CACT,CAAA;SACF;QAED,2DAA2D;QAC3D,0BAA0B;QAC1B,IAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QACjC,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAC5C,YAAY,CAAC,KAAK,EAAE,CAAA;SACrB;QAED,oCAAoC;QACpC,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,SAAS,CAAA;QAC9D,WAAW,CAAC,IAAI,OAAhB,WAAW,EAAS,YAAY,EAAC;KAClC;IAED,OAAO,WAAW,CAAA;AACpB,CAAC,CAAA;AAnFY,QAAA,MAAM,UAmFlB"}