#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# If you want to set the specified configuration value, you can set the new
# in this file. For example if you want to specify the etcd address:
#
components: {}
info:
  title: RoutesExport
  version: 3.0.0
openapi: 3.0.0
paths:
  /get:
    delete:
      operationId: api1DELETE
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
    get:
      operationId: api1GET
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
    head:
      operationId: api1HEAD
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
    patch:
      operationId: api1PATCH
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
    post:
      operationId: api1POST
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
    put:
      operationId: api1PUT
      requestBody: {}
      responses:
        default:
          description: ''
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v2
        dev: test
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
  /post:
    post:
      operationId: test_postPOST
      requestBody: {}
      responses:
        default:
          description: ''
      security: []
      x-apisix-enableWebsocket: false
      x-apisix-labels:
        API_VERSION: v1
        version: v1
      x-apisix-plugins:
        proxy-rewrite:
          disable: false
          scheme: http
      x-apisix-priority: 0
      x-apisix-status: 1
      x-apisix-upstream:
        nodes:
          - host: *************
            port: 80
            weight: 1
            priority: 10
        timeout:
          connect: 6000
          read: 6000
          send: 6000
        type: roundrobin
        pass_host: node
      x-apisix-vars: []
