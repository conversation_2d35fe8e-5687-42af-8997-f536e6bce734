{"name": "opn", "version": "5.4.0", "description": "A better node-open. Opens stuff like websites, files, executables. Cross-platform.", "license": "MIT", "repository": "sindresorhus/opn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo"}, "files": ["index.js", "xdg-open"], "keywords": ["app", "open", "opn", "opener", "opens", "launch", "start", "xdg-open", "xdg", "default", "cmd", "browser", "editor", "executable", "exe", "url", "urls", "arguments", "args", "spawn", "exec", "child", "process", "website", "file"], "dependencies": {"is-wsl": "^1.1.0"}, "devDependencies": {"ava": "^0.25.0", "xo": "^0.20.0"}}