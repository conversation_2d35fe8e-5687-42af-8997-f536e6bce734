#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

pcre_jit on;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] $request "$status" $body_bytes_sent "$http_referer" "$http_user_agent" "$http_x_forwarded_for" "$request_time" "$upstream_response_time" ';
    lua_package_path "/usr/local/openresty/nginx/conf/lua/?.lua;;";

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;

        access_log  off;

        location / {
            default_type text/html;
            content_by_lua_block {
                ngx.say(ngx.var.uri)
            }
        }
    }

    server {
        listen 1980;
        listen 1981;
        listen 1982;
        listen 5044;

        server_tokens off;

        location / {
            content_by_lua_block {
                require("server").go()
            }

            more_clear_headers Date;
        }
    }

    server {
        listen 1983 ssl;
        ssl_certificate             cert/apisix.crt;
        ssl_certificate_key         cert/apisix.key;
        lua_ssl_trusted_certificate cert/apisix.crt;

        server_tokens off;

        location / {
            content_by_lua_block {
                require("server").go()
            }

            more_clear_headers Date;
        }
    }
}

stream {
    server {
        listen 1991;

        content_by_lua_block {
            local sock = ngx.req.socket(true)
            while true do
                local data = sock:receive(5)
                if data then
                    sock:send("hello " .. data)
                    break
                end
            end
        }
    }

    server {
        listen 1992 udp;

        content_by_lua_block {
            local sock = ngx.req.socket()
            while true do
                local data = sock:receive(5)
                if data then
                    sock:send("hello " .. data)
                    break
                end
            end
        }
    }
}
