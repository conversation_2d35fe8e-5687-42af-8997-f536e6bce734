{"name": "hexo-util", "version": "2.7.0", "description": "Utilities for Hexo.", "main": "lib/index", "scripts": {"eslint": "eslint lib test", "test": "mocha", "test-cov": "nyc --reporter=lcovonly npm run test", "build:highlight": "node scripts/build_highlight_alias.js", "prepare": "npm run build:highlight"}, "directories": {"lib": "./lib", "scripts": "./scripts"}, "files": ["lib/", "scripts/", "highlight_alias.json"], "repository": "hexojs/hexo-util", "homepage": "https://hexo.io/", "keywords": ["hexo", "util", "utilities"], "author": "<PERSON> <<EMAIL>> (http://zespia.tw)", "maintainers": ["<PERSON><PERSON> <<EMAIL>> (http://abnerchou.me)"], "license": "MIT", "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.0.1", "eslint-config-hexo": "^5.0.0", "html-entities": "^2.1.1", "html-tag-validator": "^1.5.0", "mocha": "^10.0.0", "nyc": "^15.0.0", "rewire": "^6.0.0"}, "dependencies": {"bluebird": "^3.5.2", "camel-case": "^4.0.0", "cross-spawn": "^7.0.0", "deepmerge": "^4.2.2", "highlight.js": "^11.0.1", "htmlparser2": "^7.0.0", "prismjs": "^1.17.1", "strip-indent": "^3.0.0"}, "engines": {"node": ">=12.4.0"}}