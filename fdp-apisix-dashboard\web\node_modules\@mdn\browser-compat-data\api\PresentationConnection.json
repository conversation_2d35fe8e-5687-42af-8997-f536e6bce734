{"api": {"PresentationConnection": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "binaryType": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/binaryType", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "close": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/close", "support": {"chrome": {"version_added": "49"}, "chrome_android": {"version_added": "49"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "id": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/id", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "onclose": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/onclose", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "onconnect": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/onconnect", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "onmessage": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/onmessage", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "onterminate": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/onterminate", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "send": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/send", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "state": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/state", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "terminate": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/terminate", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "url": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnection/url", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}