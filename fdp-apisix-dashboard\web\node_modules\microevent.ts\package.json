{"name": "microevent.ts", "version": "0.1.1", "description": "Zero cost events", "scripts": {"prepublish": "tsc", "pretest": "typings install && tsc -p tsconfig.test.json", "test": "mocha -R spec -u tdd test"}, "author": "<PERSON> <<EMAIL>> (https://github.com/DirtyHairy/)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/DirtyHairy/microevent.git"}, "keywords": ["events", "performance"], "devDependencies": {"mocha": "~6.1.4", "typescript": "~3.4.5", "typings": "^2.1.1"}, "main": "lib/index.js", "types": "lib/index.d.ts"}