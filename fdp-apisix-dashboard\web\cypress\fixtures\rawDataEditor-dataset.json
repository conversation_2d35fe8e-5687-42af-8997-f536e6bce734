{"Route": {"uris": ["/*"], "name": "root", "methods": ["GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], "upstream": {"nodes": [{"host": "***********", "port": 80, "weight": 1}], "timeout": {"connect": 6000, "read": 6000, "send": 6000}, "type": "roundrobin", "pass_host": "pass"}, "status": 1}, "Upstream": {"nodes": [{"host": "***********", "port": 80, "weight": 1}], "timeout": {"connect": 6, "read": 6, "send": 6}, "type": "roundrobin", "pass_host": "pass", "name": "test"}, "Service": {"name": "test", "upstream": {"nodes": [{"host": "***********", "port": 80, "weight": 1}], "timeout": {"connect": 6, "read": 6, "send": 6}, "type": "roundrobin", "pass_host": "pass"}, "plugins": {"jwt-auth": {"disable": false}}}, "Consumer": {"username": "test", "desc": "desc", "plugins": {"basic-auth": {"disable": false, "password": "test", "username": "test"}}}}