{"api": {"PerformanceNavigationTiming": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58", "notes": "You can disable this feature using the <code>dom.enable_performance_navigation_timing</code> preference (see <a href='https://bugzil.la/1403926'>bug 1403926</a>)."}, "firefox_android": {"version_added": "58", "notes": "You can disable this feature using the <code>dom.enable_performance_navigation_timing</code> preference (see <a href='https://bugzil.la/1403926'>bug 1403926</a>)."}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "domComplete": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/domComplete", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "domContentLoadedEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/domContentLoadedEventEnd", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "domContentLoadedEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/domContentLoadedEventStart", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "domInteractive": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/domInteractive", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "loadEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/loadEventEnd", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "loadEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/loadEventStart", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "redirectCount": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/redirectCount", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "toJSON": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/toJSON", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "≤18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "type": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/type", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "unloadEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/unloadEventEnd", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "unloadEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigationTiming/unloadEventStart", "support": {"chrome": {"version_added": "57"}, "chrome_android": {"version_added": "57"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "44"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "57"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}