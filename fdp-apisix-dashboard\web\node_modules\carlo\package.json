{"name": "carlo", "version": "0.9.46", "description": "Carlo is a framework for rendering Node data structures using Chrome browser.", "repository": "github:GoogleChromeLabs/carlo", "engines": {"node": ">=7.6.0"}, "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"lint": "([ \"$CI\" = true ] && eslint --quiet -f codeframe . || eslint .)", "test": "node rpc/test.js && node test/test.js", "headful-test": "node test/headful.js"}, "keywords": [], "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"debug": "^4.1.0", "puppeteer-core": "~1.12.0"}, "devDependencies": {"eslint": "^5.8.0", "@pptr/testrunner": "^0.5.0", "@pptr/testserver": "^0.5.0"}}