import type { Node, Element } from "domhandler";
/**
 * Remove an element from the dom
 *
 * @param elem The element to be removed
 */
export declare function removeElement(elem: Node): void;
/**
 * Replace an element in the dom
 *
 * @param elem The element to be replaced
 * @param replacement The element to be added
 */
export declare function replaceElement(elem: Node, replacement: Node): void;
/**
 * Append a child to an element.
 *
 * @param elem The element to append to.
 * @param child The element to be added as a child.
 */
export declare function appendChild(elem: Element, child: Node): void;
/**
 * Append an element after another.
 *
 * @param elem The element to append after.
 * @param next The element be added.
 */
export declare function append(elem: Node, next: Node): void;
/**
 * Prepend a child to an element.
 *
 * @param elem The element to prepend before.
 * @param child The element to be added as a child.
 */
export declare function prependChild(elem: Element, child: Node): void;
/**
 * Prepend an element before another.
 *
 * @param elem The element to prepend before.
 * @param prev The element be added.
 */
export declare function prepend(elem: Node, prev: Node): void;
//# sourceMappingURL=manipulation.d.ts.map