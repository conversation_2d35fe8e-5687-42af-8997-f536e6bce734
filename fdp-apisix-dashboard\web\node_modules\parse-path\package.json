{"name": "parse-path", "version": "4.0.3", "description": "Parse paths (local paths, urls: ssh/git/etc)", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/IonicaBizau/parse-path.git"}, "keywords": ["parse", "path", "url", "node", "git", "advanced"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/parse-path/issues"}, "homepage": "https://github.com/IonicaBizau/parse-path", "devDependencies": {"tester": "^1.3.1"}, "dependencies": {"is-ssh": "^1.3.0", "protocols": "^1.4.0", "qs": "^6.9.4", "query-string": "^6.13.8"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "bloggify.js", "bloggify.json", "bloggify/"]}