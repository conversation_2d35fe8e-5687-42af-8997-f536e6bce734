{"api": {"Presentation": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Presentation", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "defaultRequest": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Presentation/defaultRequest", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "receiver": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Presentation/receiver", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.receiver.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.receiver.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}