{"name": "browser-resolve", "version": "1.11.3", "description": "resolve which handles browser field support in package.json", "main": "index.js", "files": ["index.js", "empty.js"], "scripts": {"test": "mocha --reporter list test/*.js"}, "repository": {"type": "git", "url": "git://github.com/shtylman/node-browser-resolve.git"}, "keywords": ["resolve", "browser"], "author": "<PERSON>ylman <<EMAIL>>", "license": "MIT", "dependencies": {"resolve": "1.1.7"}, "devDependencies": {"mocha": "1.14.0"}}