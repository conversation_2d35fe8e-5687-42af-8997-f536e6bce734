{"name": "pirates", "description": "Properly hijack require", "main": "lib/index.js", "types": "index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> lib", "build": "babel src -d lib", "test": "yarn run lint && cross-env BABEL_ENV=test yarn run build && nyc ava", "lint": "eslint --report-unused-disable-directives .", "prepublish": "yarn run clean && yarn run build"}, "files": ["lib", "index.d.ts"], "repository": {"type": "git", "url": "https://github.com/ariporad/pirates.git"}, "engines": {"node": ">= 6"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ariporad.com"}, "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "ava": "^1.2.1", "babel-core": "^7.0.0-0", "babel-eslint": "^10.0.1", "babel-plugin-istanbul": "^5.1.0", "cross-env": "^5.0.5", "cz-conventional-changelog": "^2.0.0", "decache": "^4.1.0", "eslint": "^5.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-prettier": "^3.0.1", "mock-require": "^3.0.2", "nyc": "^13.2.0", "prettier": "^1.16.4", "rewire": "^4.0.1", "rimraf": "^2.6.1", "semantic-release": "^15.7.0"}, "license": "MIT", "bugs": {"url": "https://github.com/ariporad/pirates/issues"}, "homepage": "https://github.com/ariporad/pirates#readme", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "ava": {"files": ["test/*.js"], "sources": ["lib/**/*.js"]}, "nyc": {"include": ["src/*.js"], "reporter": ["json", "text"], "sourceMap": false, "instrument": false}, "version": "4.0.1"}