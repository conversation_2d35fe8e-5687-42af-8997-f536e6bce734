{"webextensions": {"manifest": {"permissions": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/manifest.json/permissions", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}, "activeTab": {"__compat": {"description": "<code>activeTab</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "alarms": {"__compat": {"description": "<code>alarms</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "background": {"__compat": {"description": "<code>background</code>", "support": {"chrome": {"version_added": "10"}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "bookmarks": {"__compat": {"description": "<code>bookmarks</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "15"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "browserSettings": {"__compat": {"description": "<code>browserSettings</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "56"}, "firefox_android": {"version_added": "56"}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "browsingData": {"__compat": {"description": "<code>browsingData</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": "56"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "captivePortal": {"__compat": {"description": "<code>captivePortal</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "68"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "clipboardRead": {"__compat": {"description": "<code>clipboardRead</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "54"}, "firefox_android": {"version_added": "54"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "clipboardWrite": {"__compat": {"description": "<code>clipboardWrite</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "51"}, "firefox_android": {"version_added": "51"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "contentSettings": {"__compat": {"description": "<code>contentSettings</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "contextMenus": {"__compat": {"description": "<code>contextMenus</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"notes": "Available as an alias to the <code>menus</code> permission.", "version_added": "55"}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"notes": "Available as an alias to the <code>menus</code> permission.", "version_added": "14"}}}}, "contextualIdentities": {"__compat": {"description": "<code>contextualIdentities</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": "53"}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "cookies": {"__compat": {"description": "<code>cookies</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "debugger": {"__compat": {"description": "<code>debugger</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "dns": {"__compat": {"description": "<code>dns</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "60"}, "firefox_android": {"version_added": "60"}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "downloads": {"__compat": {"description": "<code>downloads</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "downloads_open": {"__compat": {"description": "<code>downloads.open</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "find": {"__compat": {"description": "<code>find</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "geolocation": {"__compat": {"description": "<code>geolocation</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "54"}, "firefox_android": {"version_added": "54"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "history": {"__compat": {"description": "<code>history</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "49"}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "identity": {"__compat": {"description": "<code>identity</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "idle": {"__compat": {"description": "<code>idle</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "15"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "management": {"__compat": {"description": "<code>management</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "51"}, "firefox_android": {"version_added": "51"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "menus": {"__compat": {"description": "<code>menus</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": "14"}}}}, "nativeMessaging": {"__compat": {"description": "<code>nativeMessaging</code>", "support": {"chrome": {"version_added": "29"}, "edge": {"version_added": "15"}, "firefox": {"version_added": "50"}, "firefox_android": {"version_added": false}, "opera": {"version_added": "16"}, "safari": {"version_added": "14"}}}}, "notifications": {"__compat": {"description": "<code>notifications</code>", "support": {"chrome": {"version_added": "5"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": "25"}, "safari": {"version_added": false}}}}, "pageCapture": {"__compat": {"description": "<code>pageCapture</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "pkcs11": {"__compat": {"description": "<code>pkcs11</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "privacy": {"__compat": {"description": "<code>privacy</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "54"}, "firefox_android": {"version_added": "54"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "proxy": {"__compat": {"description": "<code>proxy</code>", "support": {"chrome": {"version_added": "33"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "search": {"__compat": {"description": "<code>search</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "63"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "sessions": {"__compat": {"description": "<code>sessions</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "52"}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "storage": {"__compat": {"description": "<code>storage</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "tabHide": {"__compat": {"description": "<code>tabHide</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "61"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "tabs": {"__compat": {"description": "<code>tabs</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "54"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "theme": {"__compat": {"description": "<code>theme</code>", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}}}, "topSites": {"__compat": {"description": "<code>topSites</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "52"}, "firefox_android": {"version_added": "52"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}, "unlimitedStorage": {"__compat": {"description": "<code>unlimitedStorage</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "56"}, "firefox_android": {"version_added": "56"}, "opera": {"version_added": false}, "safari": {"notes": "Does not grant an unlimited storage quota. Grants a 10 MB storage quota, instead of the standard 5 MB.", "version_added": "14"}}}}, "webNavigation": {"__compat": {"description": "<code>webNavigation</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": "17"}, "safari": {"version_added": "14"}}}}, "webRequest": {"__compat": {"description": "<code>webRequest</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "webRequestBlocking": {"__compat": {"description": "<code>webRequestBlocking</code>", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "14"}, "firefox": {"version_added": "48"}, "firefox_android": {"version_added": "48"}, "opera": {"version_added": true}, "safari": {"version_added": false}}}}}}}}