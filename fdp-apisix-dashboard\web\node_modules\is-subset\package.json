{"name": "is-subset", "version": "0.1.1", "description": "Check if an object is contained within another one", "dependencies": {}, "scripts": {"build": "cd module && babel --out-dir ../ *.js", "clean": "git reset && echo '/node_modules/' > .gitignore && git add .gitignore && git stash save --include-untracked --keep-index '`npm run clean` trash can' && git clean --force -d && git reset --hard && echo '\nclean: Uncommitted and ignored files have been moved to git’s stash. To restore them run `git stash pop --quiet; git checkout .gitignore`.'", "coveralls": "npm run coverage && cat ./coverage/lcov.info | coveralls", "coverage": "isparta cover test.js", "develop": "nodangel --ignore node_modules --exec 'npm run test:lite'", "prepublish": "npm run clean && npm run build", "test": "jshint . && npm run test:lite", "test:lite": "babel-node test.js | tap-spec"}, "devDependencies": {"babel": "5.1.13", "coveralls": "2.11.2", "isparta": "3.0.3", "jshint": "2.7.0", "nodangel": "1.3.8", "tap-spec": "2.2.2", "tape-catch": "1.0.4"}, "files": ["/*.js", "/module/", "/test/", "/Readme.md", "/License.md"], "license": "MIT", "keywords": ["contains", "contain", "object", "superset", "deep-equal", "equal"], "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:studio-b12/is-subset.git"}}