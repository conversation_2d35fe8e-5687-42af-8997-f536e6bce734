{"name": "p-cancelable", "version": "1.1.0", "description": "Create a promise that can be canceled", "license": "MIT", "repository": "sindresorhus/p-cancelable", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "cancelable", "cancel", "canceled", "canceling", "cancellable", "cancellation", "abort", "abortable", "aborting", "cleanup", "task", "token", "async", "function", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.3.1", "delay": "^4.1.0", "promise.prototype.finally": "^3.1.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}}