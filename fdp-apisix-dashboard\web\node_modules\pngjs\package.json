{"name": "pngjs", "version": "2.3.1", "description": "PNG encoder/decoder in pure JS, supporting any bit size & interlace, async & sync with full test suite.", "contributors": ["<PERSON>", "Gaurav Mali", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "lian<PERSON><PERSON>g", "<PERSON>"], "homepage": "https://github.com/lukeapage/pngjs", "keywords": ["PNG", "decoder", "encoder", "js-png", "node-png", "parser", "png", "png-js", "png-parse", "pngjs"], "engines": {"node": ">=0.10.0", "iojs": ">= 1.0.0"}, "main": "./lib/png.js", "directories": {"example": "examples"}, "scripts": {"coverage": "istanbul -- cover node_modules/tape/bin/tape test/*-spec.js nolarge", "coverage-report": "npm run coverage && istanbul report html", "coveralls": "cat ./coverage/lcov.info | coveralls", "test": "npm run lint && tape test/*-spec.js | tap-dot && node test/run-compare", "lint": "eslint lib"}, "repository": {"type": "git", "url": "git://github.com/lukeapage/pngjs2.git"}, "license": "MIT", "bugs": {"url": "https://github.com/lukeapage/pngjs2/issues"}, "devDependencies": {"buffer-equal": "1.0.0", "connect": "^3.4.0", "eslint": "^2.5.0", "istanbul": "^0.4.1", "phantomjs-prebuilt": "^2.1.7", "serve-static": "^1.10.0", "tap-dot": "^1.0.0", "tape": "^4.0.2"}}