{"javascript": {"builtins": {"Intl": {"PluralRules": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules", "spec_url": "https://tc39.es/ecma402/#pluralrules-objects", "support": {"chrome": {"version_added": "63"}, "chrome_android": {"version_added": "63"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "nodejs": {"version_added": "10.0.0", "notes": "Before version 13.0.0, only the locale data for <code>en-US</code> is available by default. See <a href='https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/PluralRules'>the <code>PluralRules()</code> constructor</a> for more details."}, "opera": {"version_added": "50"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "63"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "PluralRules": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/PluralRules", "spec_url": "https://tc39.es/ecma402/#sec-intl-pluralrules-constructor", "description": "<code>PluralRules()</code> constructor", "support": {"chrome": {"version_added": "63"}, "chrome_android": {"version_added": "63"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "nodejs": [{"version_added": "13.0.0"}, {"version_added": "10.0.0", "partial_implementation": true, "notes": "Before version 13.0.0, only the locale data for <code>en-US</code> is available by default. When other locales are specified, the <code>PluralRules</code> instance silently falls back to <code>en-US</code>. To make full ICU (locale) data available for versions prior to 13, see <a href='https://nodejs.org/docs/latest/api/intl.html#intl_options_for_building_node_js'>Node.js documentation on the <code>--with-intl</code> option</a> and how to provide the data."}], "opera": {"version_added": "50"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "63"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "resolvedOptions": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/resolvedOptions", "support": {"chrome": {"version_added": "63"}, "chrome_android": {"version_added": "63"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "nodejs": {"version_added": "10.0.0", "notes": "Before version 13.0.0, only the locale data for <code>en-US</code> is available by default. See <a href='https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/PluralRules'>the <code>PluralRules()</code> constructor</a> for more details."}, "opera": {"version_added": "50"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "63"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "select": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/select", "spec_url": "https://tc39.es/ecma402/#sec-intl.pluralrules.prototype.select", "support": {"chrome": {"version_added": "63"}, "chrome_android": {"version_added": "63"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "nodejs": {"version_added": "10.0.0", "notes": "Before version 13.0.0, only the locale data for <code>en-US</code> is available by default. See <a href='https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/PluralRules'>the <code>PluralRules()</code> constructor</a> for more details."}, "opera": {"version_added": "50"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "63"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "supportedLocalesOf": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/supportedLocalesOf", "support": {"chrome": {"version_added": "63"}, "chrome_android": {"version_added": "63"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "nodejs": {"version_added": "10.0.0", "notes": "Before version 13.0.0, only the locale data for <code>en-US</code> is available by default. See <a href='https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Intl/PluralRules/PluralRules'>the <code>PluralRules()</code> constructor</a> for more details."}, "opera": {"version_added": "50"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "63"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}}}