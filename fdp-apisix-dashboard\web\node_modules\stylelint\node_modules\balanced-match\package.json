{"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "prettier-standard && standard && tape test/test.js", "bench": "matcha test/bench.js", "release": "np"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^7.4.0", "prettier-standard": "^16.4.1", "standard": "^16.0.3", "tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}