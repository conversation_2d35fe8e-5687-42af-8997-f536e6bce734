{"api": {"PerformanceTiming": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "8"}, "safari_ios": {"version_added": "8"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}, "connectEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/connectEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "connectStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/connectStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domainLookupEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domainLookupEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domainLookupStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domainLookupStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domComplete": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domComplete", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domContentLoadedEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domContentLoadedEventEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "8"}, "safari_ios": {"version_added": "8"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domContentLoadedEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domContentLoadedEventStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domInteractive": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domInteractive", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "domLoading": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/domLoading", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "fetchStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/fetchStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "loadEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/loadEventEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "loadEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/loadEventStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "navigationStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/navigationStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "redirectEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/redirectEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "redirectStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/redirectStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "requestStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/requestStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "responseEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/responseEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "responseStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/responseStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "secureConnectionStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/secureConnectionStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "56"}, "firefox_android": {"version_added": "56"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "toJSON": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/toJSON", "support": {"chrome": {"version_added": "44"}, "chrome_android": {"version_added": "44"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "25"}, "firefox_android": {"version_added": "25"}, "ie": {"version_added": "9"}, "opera": {"version_added": "32"}, "opera_android": {"version_added": "32"}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "44"}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "unloadEventEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/unloadEventEnd", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "unloadEventStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceTiming/unloadEventStart", "support": {"chrome": {"version_added": "6"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}}}}