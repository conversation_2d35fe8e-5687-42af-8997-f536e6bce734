{"name": "@formatjs/intl-unified-numberformat", "version": "3.3.7", "description": "Ponyfill for intl unified numberformat proposal", "keywords": ["polyfill", "i18n", "numberformat", "unified"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/formatjs/formatjs", "license": "MIT", "main": "dist/index.js", "module": "lib/index.js", "types": "lib/intl-unified-numberformat.d.ts", "repository": {"type": "git", "url": "git+https://github.com/formatjs/formatjs.git"}, "files": ["dist", "lib", "polyfill.js", "src"], "devDependencies": {"@formatjs/intl-pluralrules": "^1.5.9", "@types/jest": "^25.2.1", "chalk": "^4.0.0", "formatjs-extract-cldr-data": "^10.1.8", "jest": "^25.4.0", "ts-jest": "^25.4.0"}, "dependencies": {"@formatjs/intl-utils": "^2.3.0"}, "scripts": {"benchmark": "ts-node tests/benchmark", "build": "yarn run cldr && yarn run compile && yarn run rollup ", "cldr": "NODE_OPTIONS=--max-old-space-size=8192 ts-node scripts/cldr", "clean": "rimraf dist dist-es6 lib src/locales.ts *.tsbuildinfo", "compile": "NODE_OPTIONS=--max-old-space-size=8192 tsc && tsc -p tsconfig.esm.json && tsc -p tsconfig.es6.json && api-extractor run --local", "jest": "cross-env NODE_ICU_DATA=../../node_modules/full-icu cross-env NODE_ENV=test jest", "rollup": "NODE_OPTIONS=--max-old-space-size=8192 rollup -c rollup.config.js", "test": "yarn run jest", "test262": "cross-env NODE_ICU_DATA=../../node_modules/full-icu ts-node tests/runner"}, "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "gitHead": "5b218606c3ec03e332f472706fffd04153c20dae"}