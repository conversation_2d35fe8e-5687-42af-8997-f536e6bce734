{"name": "multimatch", "version": "4.0.0", "description": "Extends `minimatch.match()` with support for multiple patterns", "license": "MIT", "repository": "sindresorhus/multimatch", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["expand", "find", "glob", "globbing", "globs", "match", "matcher", "minimatch", "pattern", "patterns", "wildcard"], "dependencies": {"@types/minimatch": "^3.0.3", "array-differ": "^3.0.0", "array-union": "^2.1.0", "arrify": "^2.0.1", "minimatch": "^3.0.4"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}