{"version": 3, "file": "store.js", "sourceRoot": "", "sources": ["../../src/model/store.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oCAAoC;AACpC,gCAAmC;AAEnC;IAA8B,yBAA4B;IAQxD,eAAY,IAAqB;QAArB,qBAAA,EAAA,SAAqB;QAAjC,YACE,iBAAO,SAIR;QATS,aAAO,GAAG,KAAK,CAAA;QACf,cAAQ,GAAG,KAAK,CAAA;QAKxB,KAAI,CAAC,IAAI,GAAG,EAAO,CAAA;QACnB,KAAI,CAAC,MAAM,CAAC,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,KAAI,CAAC,OAAO,GAAG,EAAE,CAAA;;IACnB,CAAC;IAES,sBAAM,GAAhB,UACE,IAAgB,EAChB,OAAiC;QAFnC,iBA4EC;QA1EC,wBAAA,EAAA,YAAiC;QAEjC,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,IAAI,CAAA;QACpC,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,CAAA;QACtC,IAAM,OAAO,GAAQ,EAAE,CAAA;QACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAE9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QAEpB,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,QAAQ,GAAG,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC9C,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;SAClB;QAED,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;QACzB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,CAAC;YAC1B,IAAM,GAAG,GAAG,CAAM,CAAA;YAClB,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YAC1B,IAAI,CAAC,gBAAS,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,EAAE;gBAC9C,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;aAClB;YAED,IAAI,CAAC,gBAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;aACxB;iBAAM;gBACL,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA;aACpB;YAED,IAAI,KAAK,EAAE;gBACT,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA;aACpB;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,GAAG,QAAe,CAAA;aAC/B;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACnB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAA;YAC7B,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG;gBAClB,KAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACpB,GAAG,KAAA;oBACH,OAAO,SAAA;oBACP,KAAK,EAAE,KAAI;oBACX,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC;oBACrB,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC;iBACxB,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;SACH;QAED,IAAI,QAAQ,EAAE;YACZ,OAAO,IAAI,CAAA;SACZ;QAED,IAAI,CAAC,MAAM,EAAE;YACX,8DAA8D;YAC9D,OAAO,IAAI,CAAC,OAAO,EAAE;gBACnB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;gBACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,OAAO,SAAA;oBACP,QAAQ,UAAA;oBACR,KAAK,EAAE,IAAI;oBACX,OAAO,EAAE,IAAI,CAAC,cAAe;iBAC9B,CAAC,CAAA;aACH;SACF;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAE1B,OAAO,IAAI,CAAA;IACb,CAAC;IAOD,mBAAG,GAAH,UAAuB,GAAO,EAAE,YAAmB;QACjD,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,IAAI,CAAC,IAAI,CAAA;SACjB;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC1B,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAA;IACzC,CAAC;IAED,2BAAW,GAAX,UAAe,GAAY;QACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC9B,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAG,GAAiB,CAAA;SACrD;QAED,OAAO,SAAS,CAAA;IAClB,CAAC;IASD,mBAAG,GAAH,UACE,GAAmB,EACnB,KAAyD,EACzD,OAA0B;;QAE1B,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAyB,CAAC,CAAA;aAC5C;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,CAAA,SAAE,GAAC,GAAG,IAAG,KAAK,IAAgB,CAAA,EAAE,OAAO,CAAC,CAAA;aACrD;SACF;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAID,sBAAM,GAAN,UACE,GAA+B,EAC/B,OAA0B;QAE1B,IAAM,KAAK,GAAG,SAAS,CAAA;QACvB,IAAM,MAAM,GAAe,EAAE,CAAA;QAC7B,IAAI,IAAkC,CAAA;QAEtC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YACnB,IAAI,GAAG,OAAO,CAAA;SACf;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC7B,GAAG,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAAA;YACvC,IAAI,GAAG,OAAO,CAAA;SACf;aAAM;YACL,2BAA2B;YAC3B,KAAK,IAAM,KAAG,IAAI,IAAI,CAAC,IAAI,EAAE;gBAC3B,MAAM,CAAC,KAAG,CAAC,GAAG,KAAK,CAAA;aACpB;YACD,IAAI,GAAG,GAAuB,CAAA;SAC/B;QAED,IAAI,CAAC,MAAM,CAAC,MAAM,wBAAO,IAAI,KAAE,KAAK,EAAE,IAAI,IAAG,CAAA;QAC7C,OAAO,IAAI,CAAA;IACb,CAAC;IAED,yBAAS,GAAT,UAAa,IAAuB;QAClC,OAAO,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAM,CAAA;IACvD,CAAC;IAED,yBAAS,GAAT,UACE,IAAuB,EACvB,KAAU,EACV,OAAoC;QAApC,wBAAA,EAAA,YAAoC;QAEpC,IAAM,KAAK,GAAG,GAAG,CAAA;QACjB,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACrE,IAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAEhE,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAM,CAAA;QAClC,IAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAA;QAExC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAA;QACjC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAA;QAC7B,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAA;QAErC,IAAI,eAAe,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;SACnC;aAAM;YACL,IAAM,MAAM,GAAa,EAAE,CAAA;YAC3B,IAAI,KAAK,GAAG,MAAM,CAAA;YAClB,IAAI,OAAO,GAAG,QAAkB,CAAA;YAEhC,yEAAyE;YACzE,wEAAwE;YACxE,2BAA2B;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE;gBAC3C,IAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBACxB,IAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;gBACjD,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;gBAC/C,OAAO,GAAG,GAAG,CAAA;aACd;YAED,2CAA2C;YAC3C,gBAAS,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;YAEpD,IAAM,IAAI,GAAG,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAE3C,wEAAwE;YACxE,4BAA4B;YAC5B,IAAI,OAAO,CAAC,OAAO,EAAE;gBACnB,gBAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;aACzC;YAED,IAAM,MAAM,GAAG,gBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAC5C,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAA;SAC9C;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,4BAAY,GAAZ,UACE,IAAuB,EACvB,OAA0B;QAE1B,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACzD,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAM,CAAA;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAA;SAC1B;aAAM;YACL,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAC3B,IAAM,IAAI,GAAG,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;YAC/C,IAAI,IAAI,EAAE;gBACR,gBAAS,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;aACnC;YAED,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAY,EAAE,OAAO,CAAC,CAAA;SACrC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAKD,0BAAU,GAAV,UAA8B,GAAc;QAC1C,IAAI,GAAG,IAAI,IAAI,EAAE;YACf,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;SAC5C;QAED,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,0BAAU,GAAV,UAAW,IAAiB;QAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;YAChB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;SACpE;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;QACrD,IAAM,OAAO,GAAe,EAAE,CAAA;QAC9B,IAAI,UAAU,CAAA;QACd,2BAA2B;QAC3B,KAAK,IAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACrB,IAAI,CAAC,gBAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;gBACrC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;gBAClB,UAAU,GAAG,IAAI,CAAA;aAClB;SACF;QACD,OAAO,UAAU,CAAC,CAAC,CAAC,gBAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,sBAAM,GAAN;QACE,OAAO,gBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED,qBAAK,GAAL;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAkB,CAAA;QAC3C,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAM,CAAA;IACxC,CAAC;IAGD,uBAAO,GAAP;QACE,IAAI,CAAC,GAAG,EAAE,CAAA;QACV,IAAI,CAAC,IAAI,GAAG,EAAO,CAAA;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAO,CAAA;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;IAC3C,CAAC;IATD;QADC,iBAAQ,CAAC,OAAO,EAAE;wCAUlB;IACH,YAAC;CAAA,AA7SD,CAA8B,iBAAQ,GA6SrC;AA7SY,sBAAK"}