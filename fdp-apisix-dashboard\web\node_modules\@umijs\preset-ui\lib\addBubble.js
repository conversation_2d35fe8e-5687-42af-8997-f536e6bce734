"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

function _react() {
  const data = _interopRequireDefault(require("react"));

  _react = function _react() {
    return data;
  };

  return data;
}

function _path() {
  const data = require("path");

  _path = function _path() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var _default = api => {
  const utils = api.utils;
  const winPath = utils.winPath;
  api.addEntryCode(() => `
    (() => {
      try {
        const ua = window.navigator.userAgent;
        const isIE = ua.indexOf('MSIE ') > -1 || ua.indexOf('Trident/') > -1;
        if (isIE) return;

        // Umi UI Bubble
        require('${winPath((0, _path().join)(__dirname, './bubble'))}').default({
          port: ${process.env.UMI_UI_PORT},
          path: '${winPath(api.cwd)}',
          currentProject: '${process.env.UMI_UI_CURRENT_PROJECT || ''}',
          isBigfish: ${process.env.BIGFISH_COMPAT},
        });
      } catch (e) {
        console.warn('Umi UI render error:', e);
      }
    })();
  `); // TODO: chainWebpack

  api.chainWebpack(config => {
    config.plugin('umi-ui-compile-status').use(require('./CompileStatusWebpackPlugin').default);
  });
};

exports.default = _default;