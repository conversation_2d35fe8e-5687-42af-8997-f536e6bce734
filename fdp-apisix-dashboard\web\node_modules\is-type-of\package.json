{"name": "is-type-of", "version": "1.2.1", "description": "complete type checking for node", "main": "index.js", "scripts": {"test": "nyc mocha test/*.test.js"}, "repository": {"type": "git", "url": "git://github.com/node-modules/is-type-of.git"}, "files": ["index.js"], "keywords": ["typeof", "checker", "type", "is"], "author": "dead_horse <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/node-modules/is-type-of/issues"}, "homepage": "https://github.com/node-modules/is-type-of", "dependencies": {"core-util-is": "^1.0.2", "is-class-hotfix": "~0.0.6", "isstream": "~0.1.2"}, "devDependencies": {"autod": "^2.9.0", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.4", "contributors": "*", "long": "^3.2.0", "mocha": "^3.5.0", "nyc": "^11.1.0", "semver": "^5.4.1"}}