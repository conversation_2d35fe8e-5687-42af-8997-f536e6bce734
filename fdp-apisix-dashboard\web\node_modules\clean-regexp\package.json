{"name": "clean-regexp", "version": "1.0.0", "description": "Clean up regular expressions", "license": "MIT", "repository": "SamVerschueren/clean-regexp", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "lib"], "keywords": ["regex", "regexp", "regular", "expression", "clean", "cleanup", "digit", "word"], "dependencies": {"escape-string-regexp": "^1.0.5"}, "devDependencies": {"ava": "*", "xo": "*"}}