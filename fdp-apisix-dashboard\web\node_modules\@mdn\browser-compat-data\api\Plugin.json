{"api": {"Plugin": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "description": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/description", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "filename": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/filename", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "item": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/item", "support": {"chrome": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "chrome_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true, "notes": "Starting with Samsung Internet 7.0, method parameters are required instead of optional."}, "webview_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "name": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/name", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "namedItem": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/namedItem", "support": {"chrome": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "chrome_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true, "notes": "Starting with Samsung Internet 7.0, method parameters are required instead of optional."}, "webview_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "version": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Plugin/version", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}