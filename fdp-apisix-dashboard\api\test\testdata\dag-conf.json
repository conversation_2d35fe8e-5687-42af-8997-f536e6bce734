{"name": "route-with-plugin-orchestration", "uri": "/hello*", "upstream": {"type": "roundrobin", "nodes": [{"host": "upstream", "port": 1980, "weight": 1}]}, "script": {"rule": {"root": "451106f8-560c-43a4-acf2-2a6ed0ea57b8", "451106f8-560c-43a4-acf2-2a6ed0ea57b8": [["code == 403", "b93d622c-92ef-48b4-b6bb-57e1ce893ee3"], ["", "988ef5c2-c896-4606-a666-3d4cbe24a731"]]}, "conf": {"451106f8-560c-43a4-acf2-2a6ed0ea57b8": {"name": "uri-blocker", "conf": {"block_rules": ["root.exe", "root.m+"], "rejected_code": 403}}, "988ef5c2-c896-4606-a666-3d4cbe24a731": {"name": "kafka-logger", "conf": {"batch_max_size": 1000, "broker_list": {}, "buffer_duration": 60, "inactive_timeout": 5, "include_req_body": false, "kafka_topic": "1", "key": "2", "max_retry_count": 0, "name": "kafka logger", "retry_delay": 1, "timeout": 3}}, "b93d622c-92ef-48b4-b6bb-57e1ce893ee3": {"name": "fault-injection", "conf": {"abort": {"body": "blocked", "http_status": 403}, "delay": {"duration": 2}}}}, "chart": {"hovered": {}, "links": {"3a110c30-d6f3-40b1-a8ac-b828cfaa2489": {"from": {"nodeId": "3365eca3-4bc8-4769-bab3-1485dfd6a43c", "portId": "port3"}, "id": "3a110c30-d6f3-40b1-a8ac-b828cfaa2489", "to": {"nodeId": "b93d622c-92ef-48b4-b6bb-57e1ce893ee3", "portId": "port1"}}, "c1958993-c1ef-44b1-bb32-7fc6f34870c2": {"from": {"nodeId": "3365eca3-4bc8-4769-bab3-1485dfd6a43c", "portId": "port2"}, "id": "c1958993-c1ef-44b1-bb32-7fc6f34870c2", "to": {"nodeId": "988ef5c2-c896-4606-a666-3d4cbe24a731", "portId": "port1"}}, "f9c42bf6-c8aa-4e86-8498-8dfbc5c53c23": {"from": {"nodeId": "451106f8-560c-43a4-acf2-2a6ed0ea57b8", "portId": "port2"}, "id": "f9c42bf6-c8aa-4e86-8498-8dfbc5c53c23", "to": {"nodeId": "3365eca3-4bc8-4769-bab3-1485dfd6a43c", "portId": "port1"}}}, "nodes": {"3365eca3-4bc8-4769-bab3-1485dfd6a43c": {"id": "3365eca3-4bc8-4769-bab3-1485dfd6a43c", "orientation": 0, "ports": {"port1": {"id": "port1", "position": {"x": 107, "y": 0}, "type": "input"}, "port2": {"id": "port2", "position": {"x": 92, "y": 96}, "properties": {"value": "no"}, "type": "output"}, "port3": {"id": "port3", "position": {"x": 122, "y": 96}, "properties": {"value": "yes"}, "type": "output"}}, "position": {"x": 750.2627969928922, "y": 301.0370335799397}, "properties": {"customData": {"name": "code == 403", "type": 1}}, "size": {"height": 96, "width": 214}, "type": "conditions"}, "451106f8-560c-43a4-acf2-2a6ed0ea57b8": {"id": "451106f8-560c-43a4-acf2-2a6ed0ea57b8", "orientation": 0, "ports": {"port1": {"id": "port1", "position": {"x": 100, "y": 0}, "properties": {"custom": "property"}, "type": "input"}, "port2": {"id": "port2", "position": {"x": 100, "y": 96}, "properties": {"custom": "property"}, "type": "output"}}, "position": {"x": 741.5684544145346, "y": 126.75879247285502}, "properties": {"customData": {"data": {"block_rules": ["root.exe", "root.m+"], "rejected_code": 403}, "name": "uri-blocker", "type": 0}}, "size": {"height": 96, "width": 201}, "type": "uri-blocker"}, "988ef5c2-c896-4606-a666-3d4cbe24a731": {"id": "988ef5c2-c896-4606-a666-3d4cbe24a731", "orientation": 0, "ports": {"port1": {"id": "port1", "position": {"x": 106, "y": 0}, "properties": {"custom": "property"}, "type": "input"}, "port2": {"id": "port2", "position": {"x": 106, "y": 96}, "properties": {"custom": "property"}, "type": "output"}}, "position": {"x": 607.9687500000001, "y": 471.17788461538447}, "properties": {"customData": {"data": {"batch_max_size": 1000, "broker_list": {}, "buffer_duration": 60, "inactive_timeout": 5, "include_req_body": false, "kafka_topic": "1", "key": "2", "max_retry_count": 0, "name": "kafka logger", "retry_delay": 1, "timeout": 3}, "name": "kafka-logger", "type": 0}}, "size": {"height": 96, "width": 212}, "type": "kafka-logger"}, "b93d622c-92ef-48b4-b6bb-57e1ce893ee3": {"id": "b93d622c-92ef-48b4-b6bb-57e1ce893ee3", "orientation": 0, "ports": {"port1": {"id": "port1", "position": {"x": 110, "y": 0}, "properties": {"custom": "property"}, "type": "input"}, "port2": {"id": "port2", "position": {"x": 110, "y": 96}, "properties": {"custom": "property"}, "type": "output"}}, "position": {"x": 988.9074986362261, "y": 478.62041800736495}, "properties": {"customData": {"data": {"abort": {"body": "200", "http_status": 300}, "delay": {"duration": 500}}, "name": "fault-injection", "type": 0}}, "size": {"height": 96, "width": 219}, "type": "fault-injection"}}, "offset": {"x": -376.83, "y": 87.98}, "scale": 0.832, "selected": {"id": "b93d622c-92ef-48b4-b6bb-57e1ce893ee3", "type": "node"}}}}