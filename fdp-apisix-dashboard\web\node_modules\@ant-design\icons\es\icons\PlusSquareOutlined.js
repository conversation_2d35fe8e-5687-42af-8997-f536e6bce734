// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusSquareOutlinedSvg from "@ant-design/icons-svg/es/asn/PlusSquareOutlined";
import AntdIcon from '../components/AntdIcon';

var PlusSquareOutlined = function PlusSquareOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PlusSquareOutlinedSvg
  }));
};

PlusSquareOutlined.displayName = 'PlusSquareOutlined';
export default /*#__PURE__*/React.forwardRef(PlusSquareOutlined);