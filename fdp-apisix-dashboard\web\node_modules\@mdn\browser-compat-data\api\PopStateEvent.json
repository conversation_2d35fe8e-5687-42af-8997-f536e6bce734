{"api": {"PopStateEvent": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PopStateEvent", "support": {"chrome": {"version_added": "4"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "4"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "state": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PopStateEvent/state", "support": {"chrome": {"version_added": "4"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "4"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}