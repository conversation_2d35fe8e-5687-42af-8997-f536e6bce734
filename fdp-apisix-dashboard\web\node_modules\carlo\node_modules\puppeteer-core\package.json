{"name": "puppeteer-core", "version": "1.12.2", "description": "A high-level API to control headless Chrome over the DevTools Protocol", "main": "index.js", "repository": "github:GoogleChrome/puppeteer", "engines": {"node": ">=6.4.0"}, "puppeteer": {"chromium_revision": "624492"}, "scripts": {"unit": "node test/test.js", "funit": "BROWSER=firefox node test/test.js", "debug-unit": "node --inspect-brk test/test.js", "test-doclint": "node utils/doclint/check_public_api/test/test.js && node utils/doclint/preprocessor/test.js", "test": "npm run lint --silent && npm run coverage && npm run test-doclint && npm run test-node6-transformer && npm run test-types", "install": "node install.js", "lint": "([ \"$CI\" = true ] && eslint --quiet -f codeframe . || eslint .) && npm run tsc && npm run doc", "doc": "node utils/doclint/cli.js", "coverage": "cross-env COVERAGE=true npm run unit", "test-node6-transformer": "node utils/node6-transform/test/test.js", "build": "node utils/node6-transform/index.js && node utils/doclint/generate_types", "unit-node6": "node node6/test/test.js", "tsc": "tsc -p .", "prepublishOnly": "npm run build", "apply-next-version": "node utils/apply_next_version.js", "bundle": "npx browserify -r ./index.js:puppeteer -o utils/browser/puppeteer-web.js", "test-types": "node utils/doclint/generate_types && npx -p typescript@2.1 tsc -p utils/doclint/generate_types/test/", "unit-bundle": "node utils/browser/test.js"}, "author": "The Chromium Authors", "license": "Apache-2.0", "dependencies": {"debug": "^4.1.0", "extract-zip": "^1.6.6", "https-proxy-agent": "^2.2.1", "mime": "^2.0.3", "progress": "^2.0.1", "proxy-from-env": "^1.0.0", "rimraf": "^2.6.1", "ws": "^6.1.0"}, "devDependencies": {"@types/debug": "0.0.31", "@types/extract-zip": "^1.6.2", "@types/mime": "^2.0.0", "@types/node": "^8.10.34", "@types/rimraf": "^2.0.2", "@types/ws": "^6.0.1", "commonmark": "^0.28.1", "cross-env": "^5.0.5", "eslint": "^5.9.0", "esprima": "^4.0.0", "jpeg-js": "^0.3.4", "minimist": "^1.2.0", "ncp": "^2.0.0", "pixelmatch": "^4.0.2", "pngjs": "^3.3.3", "text-diff": "^1.0.1", "typescript": "3.2.2"}, "browser": {"./lib/BrowserFetcher.js": false, "./node6/lib/Puppeteer": false, "ws": "./utils/browser/WebSocket", "fs": false, "child_process": false, "rimraf": false, "readline": false}}