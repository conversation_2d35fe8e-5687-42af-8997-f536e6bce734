{"deletePluginSuccess": "Delete Plugin Successfully", "createUpstreamSuccess": "Create Upstream Successfully", "configureUpstreamSuccess": "Configure Upstream Successfully", "createServiceSuccess": "Create Service Successfully", "editServiceSuccess": "Configure Service Successfully", "deleteServiceSuccess": "Delete Service Successfully", "deleteRouteSuccess": "Delete Route Successfully", "submitSuccess": "Submit Successfully", "deleteUpstreamSuccess": "Delete Upstream Successfully", "createConsumerSuccess": "Create Consumer Successfully", "deleteConsumerSuccess": "Delete Consumer Successfully", "upstreamName": "test_upstream", "serviceName": "test_service", "serviceName2": "test_service2", "routeName": "test_route", "consumerName": "test_consumer", "basicAuthPlugin": "basic-auth", "ip1": "127.0.0.1", "ip2": "*********", "host1": "***********", "host2": "***********", "host3": "***********", "port": "80", "weight": 1, "description": "desc_by_autotest", "description2": "description2", "grafanaAddress": "<PERSON><PERSON> Address", "grafanaExplanation1": "Grafana address should begin with HTTP or HTTPS", "grafanaExplanation2": "Address is invalid", "updateSuccessfully": "Update Configuration Successfully", "deleteSSLSuccess": "Remove target SSL successfully", "sslErrorAlert": "key and cert don't match", "pluginErrorAlert": "Invalid plugin data", "pluginTemplateName": "test_plugin_template1", "pluginTemplateName2": "test_plugin_template2", "createPluginTemplateSuccess": "Create P<PERSON><PERSON> Template Successfully", "editPluginTemplateSuccess": "Configure <PERSON><PERSON><PERSON> Template Successfully", "deletePluginTemplateSuccess": "Delete Plugin Template Successfully", "pluginTemplateErrorAlert": "Request Error Code: 10000"}