#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
version: "3.6"

services:
  etcd:
    image: bitnami/etcd:3.5
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
    ports:
      - "2379:2379"
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  upstream:
    image: johz/upstream:v2.0
    restart: always
    volumes:
      - ./upstream.conf:/usr/local/openresty/nginx/conf/nginx.conf:ro
    ports:
      - '80:80/tcp'
      - '1980:1980/tcp'
      - '1981:1981/tcp'
      - '1982:1982/tcp'
      - '1983:1983/tcp'
      - '1984:1984/tcp'
      - '1991:1991/tcp'
      - '1992:1992/udp'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  upstream_grpc:
    image: grpc_server_example
    restart: always
    ports:
      - '50051:50051'
      - '50052:50052'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  upstream_httpbin:
    image: kennethreitz/httpbin
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  apisix:
    hostname: apisix_server1
    image: apache/apisix:3.0.0-debian
    restart: always
    volumes:
      - ./apisix_config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - ./apisix_logs:/usr/local/apisix/logs
    depends_on:
      - etcd
    ports:
      - '9080:9080/tcp'
      - '9090:9090/tcp'
      - '9091:9091/tcp'
      - '9443:9443/tcp'
      - '9180:9180/tcp'
      - '10090:10090/tcp'
      - '10091:10091/tcp'
      - '10092:10092/tcp'
      - '10093:10093/tcp'
      - '10095:10095/udp'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  apisix2:
    hostname: apisix_server2
    image: apache/apisix:3.0.0-debian
    restart: always
    volumes:
      - ./apisix_config2.yaml:/usr/local/apisix/conf/config.yaml:ro
    depends_on:
      - etcd
    ports:
      - '9081:9080/tcp'
      - '9181:9180/tcp'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  managerapi:
    build:
      context: ../../
      dockerfile: test/docker/Dockerfile
    restart: always
    volumes:
      - ../../conf/conf.yaml:/go/manager-api/conf/conf.yaml:ro
      - ../testdata:/go/manager-api/testdata
    depends_on:
      - etcd
    ports:
      - '9000:9000/tcp'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

  keycloak:
    image: jboss/keycloak:9.0.2
    environment:
      - KEYCLOAK_USER=admin
      - KEYCLOAK_PASSWORD=admin
      - DB_VENDOR=h2
    ports:
      - "8080:8080"

networks:
  apisix_dashboard_e2e:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: ************/24
