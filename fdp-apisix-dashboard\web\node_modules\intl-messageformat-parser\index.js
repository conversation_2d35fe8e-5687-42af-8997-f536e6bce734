"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parse = void 0;
var tslib_1 = require("tslib");
var parser_1 = require("./src/parser");
var normalize_1 = require("./src/normalize");
tslib_1.__exportStar(require("./src/types"), exports);
tslib_1.__exportStar(require("./src/parser"), exports);
function parse(input, opts) {
    opts = tslib_1.__assign({ normalizeHashtagInPlural: true, shouldParseSkeleton: true }, (opts || {}));
    var els = parser_1.pegParse(input, opts);
    if (opts.normalizeHashtagInPlural) {
        normalize_1.normalizeHashtagInPlural(els);
    }
    return els;
}
exports.parse = parse;
