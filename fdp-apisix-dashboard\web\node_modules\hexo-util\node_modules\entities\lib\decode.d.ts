import htmlDecodeTree from "./generated/decode-data-html";
import xmlDecodeTree from "./generated/decode-data-xml";
export { htmlDecodeTree, xmlDecodeTree };
export declare enum BinTrieFlags {
    HAS_VALUE = 32768,
    BRANCH_LENGTH = 32512,
    MULTI_BYTE = 128,
    JUMP_TABLE = 127
}
export declare const JUMP_OFFSET_BASE: number;
export declare function determineBranch(decodeTree: Uint16Array, current: number, nodeIdx: number, char: number): number;
export declare function decodeHTML(str: string): string;
export declare function decodeHTMLStrict(str: string): string;
export declare function decodeXML(str: string): string;
//# sourceMappingURL=decode.d.ts.map