/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,no-duplicate-selectors,string-no-newline */
.ant-pro-core-label-tip {
  display: flex;
  align-items: center;
}
.ant-pro-core-label-tip-icon {
  display: block;
  margin-left: 4px;
  cursor: pointer;
}
.ant-pro-core-label-tip-subtitle {
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-weight: normal;
  font-size: 14px;
  white-space: nowrap;
}

.ant-pro-core-field-dropdown-label {
  cursor: pointer;
}
.ant-pro-core-field-dropdown-overlay {
  min-width: 200px;
  margin-top: 4px;
  background-color: #fff;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
}
.ant-pro-core-field-dropdown-content {
  padding: 16px;
}

.ant-pro-core-dropdown-footer {
  display: flex;
  justify-content: space-between;
  padding: 16px 16px 16px 8px;
  border-top: 1px solid #f0f0f0;
}

.ant-pro-core-field-label {
  display: inline-block;
  height: 30px;
  padding: 0 4px;
  font-size: 14px;
  line-height: 30px;
  border-radius: 2px;
  cursor: pointer;
}
.ant-pro-core-field-label-active {
  padding: 0 12px;
  background-color: rgba(0, 0, 0, 0.04);
}
.ant-pro-core-field-label-active.ant-pro-core-field-label-allow-clear:hover:not(.ant-pro-core-field-label-disabled) .ant-pro-core-field-label-arrow {
  display: none;
}
.ant-pro-core-field-label-active.ant-pro-core-field-label-allow-clear:hover:not(.ant-pro-core-field-label-disabled) .ant-pro-core-field-label-close {
  display: inline-block;
}
.ant-pro-core-field-label-icon {
  margin-top: -2px;
  margin-left: 4px;
  padding: 1px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  vertical-align: middle;
}
.ant-pro-core-field-label-icon.ant-pro-core-field-label-close {
  display: none;
  margin-top: -4px;
  padding: 3px;
  color: #fff;
  font-size: 8px;
  background-color: rgba(0, 0, 0, 0.25);
  border-radius: 50%;
}
.ant-pro-core-field-label-icon.ant-pro-core-field-label-close:hover {
  background-color: rgba(0, 0, 0, 0.45);
}
.ant-pro-core-field-label-disabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
.ant-pro-core-field-label-disabled .ant-pro-core-field-label-icon {
  color: rgba(0, 0, 0, 0.25);
}
.ant-pro-core-field-label-small {
  height: 24px;
  padding: 0 4px;
  font-size: 12px;
  line-height: 24px;
}
.ant-pro-core-field-label-small.ant-pro-core-field-label-active {
  padding: 0 8px;
}
.ant-pro-core-field-label-small .ant-pro-core-field-label-icon {
  padding: 0;
}
.ant-pro-core-field-label-small .ant-pro-core-field-label-close {
  margin-top: -2px;
  padding: 3px;
  font-size: 6px;
}
.ant-pro-core-field-label-bordered {
  height: 32px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}
.ant-pro-core-field-label-bordered.ant-pro-core-field-label-small {
  height: 24px;
  padding: 0 8px;
}
.ant-pro-core-field-label-bordered.ant-pro-core-field-label-active {
  background-color: #fff;
}

