{"name": "nzh", "version": "1.0.4", "description": "数字转中文,大写,金额", "homepage": "http://cnwhy.github.io/nzh", "main": "nzh.js", "files": ["src/*", "dist/*", "nzh.js", "cn.js", "hk.js"], "scripts": {"test": "./node_modules/.bin/_mocha test/test_mocha.js", "build": "gulp build"}, "repository": {"type": "git", "url": "https://github.com/cnwhy/nzh.git"}, "keywords": ["数字转中文", "数字转金额", "中文数字转阿拉伯数字", "中文数字"], "author": {"name": "cnwhy", "email": "<EMAIL>"}, "devDependencies": {"gulp": "^3.9.1", "gulp-browserify": "^0.5.1", "gulp-header": "^1.8.8", "gulp-mocha": "^3.0.1", "gulp-mocha-phantomjs": "^0.12.1", "gulp-rename": "^1.2.2", "gulp-uglify": "^2.0.1", "rollup": "^0.63.4", "rollup-plugin-commonjs": "^9.1.3", "rollup-plugin-node-resolve": "^3.3.0"}, "license": "BSD-2-<PERSON><PERSON>"}