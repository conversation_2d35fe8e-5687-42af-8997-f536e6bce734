{"svg": {"attributes": {"presentation": {"alignment-baseline": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/alignment-baseline", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "baseline-shift": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/baseline-shift", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "clip": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/clip", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "clip-path": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/clip-path", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "clip-rule": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/clip-rule", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "color": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/color", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "color-interpolation": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/color-interpolation", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "color-interpolation-filters": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/color-interpolation-filters", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "color-profile": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/color-profile", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "color-rendering": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/color-rendering", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": false, "deprecated": true}}}, "cursor": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/cursor", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "direction": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/direction", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "display": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/display", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "dominant-baseline": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/dominant-baseline", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "enable-background": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/enable-background", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "fill": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/fill", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "fill-opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/fill-opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "fill-rule": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/fill-rule", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "filter": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/filter", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "flood-color": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/flood-color", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "flood-opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/flood-opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-family": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-family", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-size": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-size", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-size-adjust": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-size-adjust", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-stretch": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-stretch", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-style": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-style", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-variant": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-variant", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "font-weight": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/font-weight", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "glyph-orientation-horizontal": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/glyph-orientation-horizontal", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "glyph-orientation-vertical": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/glyph-orientation-vertical", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "image-rendering": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/image-rendering", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "kerning": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/kerning", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "letter-spacing": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/letter-spacing", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "73"}, "firefox_android": {"version_added": false}, "ie": {"version_added": "9"}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": "5.1"}, "safari_ios": {"version_added": "5.1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "lighting-color": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/lighting-color", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "marker-end": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/marker-end", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "marker-mid": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/marker-mid", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "marker-start": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/marker-start", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "mask": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/mask", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "overflow": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/overflow", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "paint-order": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/paint-order", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "pointer-events": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/pointer-events", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "shape-rendering": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/shape-rendering", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "solid-color": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/solid-color", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "solid-opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/solid-opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stop-color": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stop-color", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stop-opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stop-opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-dasharray": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-dasharray", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-dashoffset": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-dashoffset", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-linecap": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linecap", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-linejoin": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-linejoin", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-miterlimit": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-miterlimit", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-opacity": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-opacity", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "stroke-width": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/stroke-width", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "text-anchor": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/text-anchor", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "text-decoration": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/text-decoration", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "text-overflow": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/text-overflow", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "text-rendering": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/text-rendering", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "transform": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/transform", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "transform-origin": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/transform-origin", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": true}, "firefox": {"version_added": "77"}, "firefox_android": {"version_added": false}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "unicode-bidi": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/unicode-bidi", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "vector-effect": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/vector-effect", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "visibility": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/visibility", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "white-space": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/white-space", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "word-spacing": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/word-spacing", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "73"}, "firefox_android": {"version_added": false}, "ie": {"version_added": "9"}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": "5.1"}, "safari_ios": {"version_added": "5.1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "writing-mode": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/SVG/Attribute/writing-mode", "support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}}