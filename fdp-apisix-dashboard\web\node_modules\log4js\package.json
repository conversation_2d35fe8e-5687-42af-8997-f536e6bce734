{"name": "log4js", "version": "1.1.1", "description": "Port of Log4js to work with node.", "keywords": ["logging", "log", "log4j", "node"], "license": "Apache-2.0", "main": "./lib/log4js", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/nomiddlename/log4js-node.git"}, "bugs": {"url": "http://github.com/nomiddlename/log4js-node/issues"}, "engines": {"node": ">=0.12"}, "scripts": {"pretest": "jshint lib/ test/", "test": "tape 'test/tape/**/*.js' && vows test/vows/*.js"}, "directories": {"test": "test", "lib": "lib"}, "dependencies": {"debug": "^2.2.0", "semver": "^5.3.0", "streamroller": "^0.4.0"}, "devDependencies": {"jshint": "^2.9.2", "sandboxed-module": "0.1.3", "tape": "^4.6.2", "vows": "0.7.0"}, "browser": {"os": false}}