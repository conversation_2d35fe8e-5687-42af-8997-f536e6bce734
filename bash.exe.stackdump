Stack trace:
Frame         Function      Args
0007FFFFB740  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x2118E
0007FFFFB740  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x69BA
0007FFFFB740  0002100469F2 (00021028DF99, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB740  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB740  00021006A545 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBA20  00021006B9A5 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8C6CA0000 ntdll.dll
7FF8C6060000 KERNEL32.DLL
7FF8C4430000 KERNELBASE.dll
7FF8C62E0000 USER32.dll
7FF8C49A0000 win32u.dll
000210040000 msys-2.0.dll
7FF8C5200000 GDI32.dll
7FF8C47C0000 gdi32full.dll
7FF8C49D0000 msvcp_win.dll
7FF8C41A0000 ucrtbase.dll
7FF8C5750000 advapi32.dll
7FF8C56A0000 msvcrt.dll
7FF8C68C0000 sechost.dll
7FF8C6690000 RPCRT4.dll
7FF8C38E0000 CRYPTBASE.DLL
7FF8C4A70000 bcryptPrimitives.dll
7FF8C6B00000 IMM32.DLL
