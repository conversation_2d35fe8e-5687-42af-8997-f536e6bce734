{"name": "caching-transform", "version": "4.0.0", "description": "Wraps a transform and provides caching", "license": "MIT", "repository": "istanbuljs/caching-transform", "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["transform", "cache", "require", "transpile", "fast", "speed", "hash"], "dependencies": {"hasha": "^5.0.0", "make-dir": "^3.0.0", "package-hash": "^4.0.0", "write-file-atomic": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "coveralls": "^3.0.3", "nyc": "^14.1.0", "proxyquire": "^2.1.0", "rimraf": "^2.6.3", "sinon": "^7.3.2", "xo": "^0.24.0"}, "nyc": {"reporter": ["lcov", "text"]}}