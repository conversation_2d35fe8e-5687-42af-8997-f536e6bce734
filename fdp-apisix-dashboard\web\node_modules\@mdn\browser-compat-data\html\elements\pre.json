{"html": {"elements": {"pre": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/HTML/Element/pre", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": "1"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": true}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "cols": {"__compat": {"support": {"chrome": {"version_added": false}, "chrome_android": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "1", "version_removed": "29"}, "firefox_android": {"version_added": "4", "version_removed": "29"}, "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": false, "deprecated": true}}}, "width": {"__compat": {"support": {"chrome": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "chrome_android": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "edge": {"version_added": "12", "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "firefox": {"version_added": "1", "notes": "Since Firefox 29, specifying the <code>width</code> attribute has no layout effect."}, "firefox_android": {"version_added": "4", "notes": "Since Firefox 29, specifying the <code>width</code> attribute has no layout effect."}, "ie": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "opera": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "opera_android": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "safari": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "safari_ios": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "samsunginternet_android": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}, "webview_android": {"version_added": true, "notes": "Specifying the <code>width</code> attribute has no layout effect."}}, "status": {"experimental": false, "standard_track": false, "deprecated": true}}}, "wrap": {"__compat": {"support": {"chrome": {"version_added": null}, "chrome_android": {"version_added": null}, "edge": {"version_added": null}, "firefox": {"version_added": "1"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": null}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": null}, "webview_android": {"version_added": null}}, "status": {"experimental": false, "standard_track": false, "deprecated": true}}}}}}}