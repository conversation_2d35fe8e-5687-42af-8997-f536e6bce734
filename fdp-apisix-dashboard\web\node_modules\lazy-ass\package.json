{"name": "lazy-ass", "description": "Lazy assertions without performance penalty", "version": "1.6.0", "author": "<PERSON><PERSON><PERSON> <gleb.bah<PERSON><PERSON>@gmail.com>", "bugs": {"url": "https://github.com/bahmutov/lazy-ass/issues"}, "config": {"pre-git": {"commit-msg": "simple", "pre-commit": ["npm test"], "pre-push": ["npm run size"], "post-commit": [], "post-merge": []}}, "contributors": [], "dependencies": {}, "devDependencies": {"@semantic-release/condition-travis": "4.1.4", "bad-line": "0.1.1", "condition-node-version": "1.2.0", "coveralls": "2.11.6", "expect.js": "0.3.1", "git-issues": "1.2.0", "grunt": "0.4.5", "grunt-banner": "0.6.0", "grunt-clean-console": "0.1.1", "grunt-cli": "1.0.0-rc1", "grunt-contrib-concat": "0.5.1", "grunt-contrib-copy": "0.8.2", "grunt-contrib-jshint": "1.0.0", "grunt-contrib-uglify": "0.11.1", "grunt-contrib-watch": "0.6.1", "grunt-deps-ok": "0.9.0", "grunt-gh-pages": "1.0.0", "grunt-karma": "0.10.1", "grunt-mocha-test": "0.12.7", "grunt-nice-package": "0.10.3", "grunt-npm2bower-sync": "0.9.1", "jshint-stylish": "2.1.0", "karma": "0.12.32", "karma-chrome-launcher": "0.2.2", "karma-coverage": "0.5.3", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "1.0.0", "matchdep": "1.0.1", "mocha": "2.4.5", "phantomjs": "2.1.3", "phantomjs-prebuilt": "2.1.12", "pkgfiles": "2.3.2", "pre-git": "3.4.0", "semantic-release": "6.3.6", "time-grunt": "1.4.0"}, "engines": {"node": "> 0.8"}, "files": ["index.js", "index.html", "bower.json", "test/demo.js", "index.d.ts"], "homepage": "https://github.com/bahmu<PERSON>/lazy-ass", "keywords": ["assertion", "assertions", "browser", "debugging", "defensive", "lazy", "node"], "license": "MIT", "main": "index.js", "release": {"verifyConditions": [{"path": "@semantic-release/condition-travis"}, {"path": "condition-node-version", "node": "4.2.2"}]}, "repository": {"type": "git", "url": "https://github.com/bahmutov/lazy-ass.git"}, "scripts": {"build": "grunt", "commit": "git-issues && commit-wizard", "coveralls": "cat coverage/PhantomJS*/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "demo": "grunt gh-pages", "dont-break": "dont-break --timeout 30", "issues": "git-issues", "mocha": "mocha test/*.spec.js", "pkgfiles": "pkgfiles", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "size": "tarball=\"$(npm pack .)\"; wc -c \"${tarball}\"; tar tvf \"${tarball}\"; rm \"${tarball}\";", "test": "grunt test", "watch": "grunt watch"}, "types": "index.d.ts"}