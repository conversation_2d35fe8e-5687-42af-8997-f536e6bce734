{"api": {"PositionOptions": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionOptions", "support": {"chrome": {"version_added": "5"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "3.5"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "9"}, "opera": [{"version_added": "16"}, {"version_added": "10", "version_removed": "15"}], "opera_android": [{"version_added": "16"}, {"version_added": "10.1", "version_removed": "14"}], "safari": {"version_added": "5"}, "safari_ios": {"version_added": "5"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "enableHighAccuracy": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionOptions/enableHighAccuracy", "support": {"chrome": {"version_added": "5"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "3.5"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "9"}, "opera": [{"version_added": "16"}, {"version_added": "10", "version_removed": "15"}], "opera_android": [{"version_added": "16"}, {"version_added": "10.1", "version_removed": "14"}], "safari": {"version_added": "5"}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "maximumAge": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionOptions/maximumAge", "support": {"chrome": {"version_added": "5"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "3.5"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "9"}, "opera": [{"version_added": "16"}, {"version_added": "10", "version_removed": "15"}], "opera_android": [{"version_added": "16"}, {"version_added": "10.1", "version_removed": "14"}], "safari": {"version_added": "5"}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "secure_context_required": {"__compat": {"description": "Secure context required", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "ie": {"version_added": false}, "opera": {"version_added": "37"}, "opera_android": {"version_added": "37"}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": "51", "notes": "Secure context is only required for applications targeting Android Nougat (7) and higher. See <a href='https://crbug.com/603574'>bug 603574</a>."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "timeout": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionOptions/timeout", "support": {"chrome": {"version_added": "5"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "3.5"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "9"}, "opera": [{"version_added": "16"}, {"version_added": "10", "version_removed": "15"}], "opera_android": [{"version_added": "16"}, {"version_added": "10.1", "version_removed": "14"}], "safari": {"version_added": "5"}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}