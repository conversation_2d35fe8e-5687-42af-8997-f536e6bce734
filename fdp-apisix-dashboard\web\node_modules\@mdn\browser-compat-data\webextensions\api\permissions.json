{"webextensions": {"api": {"permissions": {"contains": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/contains", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "getAll": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/getAll", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "onAdded": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/onAdded", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "77", "notes": "There is a <a href='https://github.com/bfred-it/chrome-permissions-events-polyfill'>polyfill</a> available for earlier versions."}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "onRemoved": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/onRemoved", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "77", "notes": "There is a <a href='https://github.com/bfred-it/chrome-permissions-events-polyfill'>polyfill</a> available for earlier versions."}, "firefox_android": {"version_added": false}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "Permissions": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/Permissions", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "opera": {"version_added": true}, "safari": {"version_added": "14"}}}}, "remove": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/remove", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": {"version_added": "55"}, "firefox_android": {"version_added": "55"}, "opera": {"version_added": true}, "safari": {"version_added": "14", "notes": "Removing <code>&lt;all_urls&gt;</code> or <code>*://*/*</code> origins will remove previously granted permission to request specific origin patterns and will stop automatically prompting the user for access to any visited website via the extension's access popover in the toolbar."}}}}, "request": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/permissions/request", "support": {"chrome": {"version_added": true}, "edge": {"version_added": "79"}, "firefox": [{"version_added": "55", "notes": ["The user will be prompted again for permissions that have been previously granted and then removed.", "It's not possible to request permissions from a popup or a sidebar document."]}, {"version_added": "56", "version_removed": "61", "notes": "It's not possible to request permissions from an options page that's embedded in about:addons. To request permissions from an options page, set the <code>open_in_tab</code> property in the <code>options_ui</code> manifest key, so the options page opens in its own tab."}], "firefox_android": [{"version_added": "55", "notes": ["The user will be prompted again for permissions that have been previously granted and then removed.", "It's not possible to request permissions from a popup or a sidebar document."]}, {"version_added": "56", "version_removed": "61", "notes": "It's not possible to request permissions from an options page that's embedded in about:addons. To request permissions from an options page, set the <code>open_in_tab</code> property in the <code>options_ui</code> manifest key, so the options page opens in its own tab."}], "opera": {"version_added": true}, "safari": {"version_added": "14", "notes": ["Requesting <code>&lt;all_urls&gt;</code> or <code>*://*/*</code> origins will grant permission to request specific origin patterns and automatically prompt the user for access to any visited website via the extension's access popover in the toolbar.", "The user will be prompted again for permissions that have been previously granted and then removed.", "Supported permissions will be granted without prompting the user. Only specific origin patterns will prompt the user."]}}}}}}}}