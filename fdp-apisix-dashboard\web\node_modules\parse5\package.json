{"name": "parse5", "description": "HTML parsing/serialization toolset for Node.js. WHATWG HTML Living Standard (aka HTML5)-compliant.", "version": "3.0.3", "author": "<PERSON> <<EMAIL>> (https://github.com/inikulin)", "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "homepage": "https://github.com/inikulin/parse5", "devDependencies": {"del": "^2.0.2", "gulp": "^3.9.0", "gulp-benchmark": "^1.1.1", "gulp-download": "0.0.1", "gulp-eslint": "^3.0.1", "gulp-install": "^0.6.0", "gulp-mocha": "^2.1.3", "gulp-rename": "^1.2.2", "gulp-typedoc": "^2.0.0", "gulp-typescript": "^3.1.2", "publish-please": "^2.2.0", "through2": "^2.0.0", "typedoc": "^0.5.1", "typescript": "^2.0.6"}, "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "sax", "simple api", "parse", "tokenize", "serialize", "tokenizer"], "license": "MIT", "main": "./lib/index.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "scripts": {"test": "gulp test", "publish-please": "publish-please", "prepublish": "publish-please guard"}, "files": ["lib"], "dependencies": {"@types/node": "*"}}