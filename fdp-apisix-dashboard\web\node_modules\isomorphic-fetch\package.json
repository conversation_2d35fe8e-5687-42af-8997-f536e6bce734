{"name": "isomorphic-fetch", "version": "2.2.1", "description": "Isomorphic WHATWG Fetch API, for Node & Browserify", "browser": "fetch-npm-browserify.js", "main": "fetch-npm-node.js", "scripts": {"files": "find . -name '*.js' ! -path './node_modules/*' ! -path './bower_components/*'", "test": "jshint `npm run -s files` && lintspaces -i js-comments -e .editorconfig `npm run -s files` && mocha"}, "repository": {"type": "git", "url": "https://github.com/matthew-andrews/isomorphic-fetch.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/matthew-andrews/isomorphic-fetch/issues"}, "homepage": "https://github.com/matthew-andrews/isomorphic-fetch/issues", "dependencies": {"node-fetch": "^1.0.1", "whatwg-fetch": ">=0.10.0"}, "devDependencies": {"chai": "^1.10.0", "es6-promise": "^2.0.1", "jshint": "^2.5.11", "lintspaces-cli": "0.0.4", "mocha": "^2.1.0", "nock": "^0.56.0", "npm-prepublish": "^1.0.2"}}