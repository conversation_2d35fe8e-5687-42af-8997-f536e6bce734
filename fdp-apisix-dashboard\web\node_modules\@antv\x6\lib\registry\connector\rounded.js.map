{"version": 3, "file": "rounded.js", "sourceRoot": "", "sources": ["../../../src/registry/connector/rounded.ts"], "names": [], "mappings": ";;;AAAA,2CAA4C;AAOrC,IAAM,OAAO,GAAkD,UACpE,WAAW,EACX,WAAW,EACX,WAAW,EACX,OAAY;IAAZ,wBAAA,EAAA,YAAY;IAEZ,IAAM,IAAI,GAAG,IAAI,eAAI,EAAE,CAAA;IAEvB,IAAI,CAAC,aAAa,CAAC,eAAI,CAAC,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAA;IAExD,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;IACjB,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;IACjB,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAA;IAEnC,IAAI,YAAY,CAAA;IAChB,IAAI,YAAY,CAAA;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;QACvD,IAAM,IAAI,GAAG,gBAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAA;QACzC,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,CAAA;QAC9C,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,CAAA;QAE9C,YAAY,GAAG,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtD,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEtC,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACjD,IAAM,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAE/C,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,KAAK,EAAE,CAAA;QAC/D,IAAM,UAAU,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,CAAA;QAE3D,IAAM,QAAQ,GAAG,IAAI,gBAAK,CACxB,GAAG,GAAG,YAAY,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,EACnC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC,CACpC,CAAA;QACD,IAAM,QAAQ,GAAG,IAAI,gBAAK,CACxB,GAAG,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,EACjC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAClC,CAAA;QAED,IAAI,CAAC,aAAa,CAAC,eAAI,CAAC,aAAa,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAA;QACzD,IAAI,CAAC,aAAa,CAAC,eAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAA;KAC5E;IAED,IAAI,CAAC,aAAa,CAAC,eAAI,CAAC,aAAa,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAA;IAExD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA;AAC9C,CAAC,CAAA;AA9CY,QAAA,OAAO,WA8CnB"}