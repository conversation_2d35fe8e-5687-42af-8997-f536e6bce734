{"css": {"properties": {"position": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/CSS/position", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "1", "notes": ["Before Firefox 57, absolute positioning did not work correctly when applied to elements inside tables that have <a href='https://developer.mozilla.org/docs/Web/CSS/border-collapse'><code>border-collapse</code></a> applied to them (<a href='https://bugzil.la/1379306'>bug 1379306</a>).", "Before Firefox 30, absolute positioning of table rows and row groups was not supported (<a href='https://bugzil.la/63895'>bug 63895</a>)."]}, "firefox_android": {"version_added": "4", "notes": ["Before Firefox 57, absolute positioning did not work correctly when applied to elements inside tables that have <a href='https://developer.mozilla.org/docs/Web/CSS/border-collapse'><code>border-collapse</code></a> applied to them (<a href='https://bugzil.la/1379306'>bug 1379306</a>).", "Before Firefox 30, absolute positioning of table rows and row groups was not supported (<a href='https://bugzil.la/63895'>bug 63895</a>)."]}, "ie": {"version_added": "4"}, "opera": {"version_added": "4"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "1"}, "safari_ios": {"version_added": "1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "absolutely_positioned_flex_children": {"__compat": {"description": "Absolutely-positioned flex children", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "52"}, "firefox_android": {"version_added": "52"}, "ie": {"version_added": "10"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "52"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "fixed": {"__compat": {"description": "<code>fixed</code>", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "1", "notes": "Before Firefox 44, <code>position: fixed</code> didn't create a stacking context in most cases. Firefox and the specification have been modified to mimic Chrome and Safari's long-time behavior."}, "firefox_android": {"version_added": "4", "notes": "Before Firefox 44, <code>position: fixed</code> didn't create a stacking context in most cases. Firefox and the specification have been modified to mimic Chrome and Safari's long-time behavior."}, "ie": {"version_added": "7", "notes": "In Internet Explorer, fixed positioning doesn't work if the document is in <a href='https://developer.mozilla.org/docs/Web/HTML/Quirks_Mode_and_Standards_Mode'>quirks mode</a>."}, "opera": {"version_added": "4"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "1"}, "safari_ios": {"version_added": "1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "position_sticky_table_elements": {"__compat": {"description": "Table elements as <code>sticky</code> positioning containers", "support": {"chrome": {"version_added": "56"}, "chrome_android": {"version_added": "56"}, "edge": {"version_added": "16"}, "firefox": {"version_added": "59"}, "firefox_android": {"version_added": "59"}, "ie": {"version_added": false}, "opera": {"version_added": "43"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": "8"}, "safari_ios": {"version_added": "8"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "56"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "sticky": {"__compat": {"description": "<code>sticky</code>", "support": {"chrome": {"version_added": "56"}, "chrome_android": {"version_added": "56"}, "edge": {"version_added": "16"}, "firefox": [{"version_added": "32"}, {"version_added": "26", "version_removed": "48", "flags": [{"type": "preference", "name": "layout.css.sticky.enabled", "value_to_set": "true"}]}], "firefox_android": [{"version_added": "32"}, {"version_added": "26", "version_removed": "48", "flags": [{"type": "preference", "name": "layout.css.sticky.enabled", "value_to_set": "true"}]}], "ie": {"version_added": false}, "opera": {"version_added": "43"}, "opera_android": {"version_added": "43"}, "safari": [{"version_added": "13"}, {"prefix": "-webkit-", "version_added": "6.1"}], "safari_ios": [{"version_added": "13"}, {"prefix": "-webkit-", "version_added": "6.1"}], "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "56"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}}