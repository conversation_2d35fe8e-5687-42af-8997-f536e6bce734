{"api": {"PerformanceNavigation": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigation", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}, "redirectCount": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigation/redirectCount", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7"}, "firefox_android": {"version_added": "7"}, "ie": {"version_added": "9"}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "toJSON": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigation/toJSON", "support": {"chrome": {"version_added": "56"}, "chrome_android": {"version_added": "56"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "25", "notes": "This property can return incorrect values. See <a href='https://bugzil.la/1459711'>bug 1459711</a>."}, "firefox_android": {"version_added": "25", "notes": "This property can return incorrect values. See <a href='https://bugzil.la/1459711'>bug 1459711</a>."}, "ie": {"version_added": "9"}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "56"}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}, "type": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceNavigation/type", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": "7", "notes": "This property can return incorrect values. See <a href='https://bugzil.la/1459711'>bug 1459711</a>."}, "firefox_android": {"version_added": "7", "notes": "This property can return incorrect values. See <a href='https://bugzil.la/1459711'>bug 1459711</a>."}, "ie": {"version_added": "9"}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}}}}