{"name": "pngjs-image", "version": "0.11.7", "description": "JavaScript-based PNG image encoder, decoder, and manipulator", "license": "MIT", "main": "index.js", "homepage": "https://github.com/yahoo/pngjs-image", "bugs": "https://github.com/yahoo/pngjs-image/issues", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.marcelerz.com"}, "repository": {"type": "git", "url": "https://github.com/yahoo/pngjs-image.git"}, "keywords": ["PNG", "image", "manipulation", "filter", "alpha"], "scripts": {"test": "istanbul cover -- _mocha --reporter spec", "docs": "yuidoc ."}, "dependencies": {"iconv-lite": "^0.4.8", "pako": "^0.2.6", "pngjs": "2.3.1", "request": "^2.55.0", "stream-buffers": "1.0.1", "underscore": "1.7.0"}, "devDependencies": {"chai": "1.9.2", "coveralls": "2.11.2", "codeclimate-test-reporter": "0.0.4", "istanbul": "0.3.2", "mocha": "1.21.4", "sinon": "1.12.2", "sinon-chai": "2.7.0", "yuidocjs": "0.3.50"}}