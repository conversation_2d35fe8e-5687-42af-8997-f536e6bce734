{"name": "caller-callsite", "version": "2.0.0", "description": "Get the callsite of the caller function", "license": "MIT", "repository": "sindresorhus/caller-callsite", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["caller", "calling", "module", "parent", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}