{"name": "ospath", "version": "1.2.2", "description": "Operating system specific paths.", "main": "index.js", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "mocha ./ospath.test.js"}, "repository": {"type": "git", "url": "git+https://github.com/jprichardson/ospath.git"}, "keywords": ["home", "data", "dir", "directory", "path", "tmp", "temp", "windows", "linux", "darwin", "mac"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jprichardson/ospath/issues"}, "homepage": "https://github.com/jprichardson/ospath#readme", "devDependencies": {"lodash.clonedeep": "^3.0.1", "mocha": "2.x", "proxyquire": "^1.5.0", "standard": "^8.0.0"}}