{"api": {"Permissions": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Permissions", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "46"}, "firefox_android": {"version_added": "46"}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "accelerometer_permission": {"__compat": {"description": "<code>accelerometer</code> permission", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "accessibility-events_permission": {"__compat": {"description": "<code>accessibility-events</code> permission", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "ambient-light-sensor_permission": {"__compat": {"description": "<code>ambient-light-sensor</code> permission", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "background-sync_permission": {"__compat": {"description": "<code>background-sync</code> permission", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "camera_permission": {"__compat": {"description": "<code>camera</code> permission", "support": {"chrome": {"version_added": "64"}, "chrome_android": {"version_added": "64"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "64"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "clipboard-read_permission": {"__compat": {"description": "<code>clipboard-read</code> permission", "support": {"chrome": {"version_added": "64"}, "chrome_android": {"version_added": "64"}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "64"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "clipboard-write_permission": {"__compat": {"description": "<code>clipboard-write</code> permission", "support": {"chrome": {"version_added": "64"}, "chrome_android": {"version_added": "64"}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "64"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "geolocation_permission": {"__compat": {"description": "<code>geolocation</code> permission", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "gyroscope_permission": {"__compat": {"description": "<code>gyroscope</code> permission", "support": {"chrome": {"version_added": "51"}, "chrome_android": {"version_added": "51"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": "51"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "magnetometer_permission": {"__compat": {"description": "<code>magnetometer</code> permission", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "microphone_permission": {"__compat": {"description": "<code>microphone</code> permission", "support": {"chrome": {"version_added": "64"}, "chrome_android": {"version_added": "64"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "64"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "midi_permission": {"__compat": {"description": "<code>midi</code> permission", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "notifications_permission": {"__compat": {"description": "<code>notifications</code> permission", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "payment-handler_permission": {"__compat": {"description": "<code>payment-handler</code> permission", "support": {"chrome": {"version_added": "66"}, "chrome_android": {"version_added": "66"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "66"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "persistent-storage_permission": {"__compat": {"description": "<code>persistent-storage</code> permission", "support": {"chrome": {"version_added": "71"}, "chrome_android": {"version_added": "71"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": "53"}, "ie": {"version_added": false}, "opera": {"version_added": "58"}, "opera_android": {"version_added": "50"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "10.0"}, "webview_android": {"version_added": "71"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "push_permission": {"__compat": {"description": "<code>push</code> permission", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "query": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Permissions/query", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "46"}, "firefox_android": {"version_added": "46"}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "request": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Permissions/request", "support": {"chrome": {"version_added": "46"}, "chrome_android": {"version_added": "46"}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": "46"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "requestAll": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Permissions/requestAll", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "79"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": "48"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "revoke": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/Permissions/revoke", "support": {"chrome": {"version_added": "46"}, "chrome_android": {"version_added": "46"}, "edge": {"version_added": "79"}, "firefox": [{"version_added": "51", "flags": [{"type": "preference", "name": "dom.permissions.revoke.enable", "value_to_set": "true"}]}, {"version_added": "47", "version_removed": "51"}], "firefox_android": [{"version_added": "51", "flags": [{"type": "preference", "name": "dom.permissions.revoke.enable", "value_to_set": "true"}]}, {"version_added": "47", "version_removed": "51"}], "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": "46"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}}}}