{"version": 3, "file": "scroller.js", "sourceRoot": "", "sources": ["../../src/graph/scroller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,gCAA6B;AAC7B,kCAAsC;AAEtC,+BAA6B;AAE7B;IAAqC,mCAAI;IAAzC;;IA6IA,CAAC;IA1IC,sBAAc,0CAAa;aAA3B;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAC9B,CAAC;;;OAAA;IAED,sBAAI,qCAAQ;aAAZ;YACE,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,IAAI,CAAA;QACnE,CAAC;;;OAAA;IAES,8BAAI,GAAd;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;QAC9C,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAES,wCAAc,GAAxB;QACE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACpE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACtE,CAAC;IAES,uCAAa,GAAvB;QACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC5D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACrE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACvE,CAAC;IAES,wCAAc,GAAxB,UAAyB,EAAmC;QAA5D,iBAWC;YAX0B,CAAC,OAAA;QAC1B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IACE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;gBAC1B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EACxE;gBACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,cAAM,OAAA,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAA3B,CAA2B,CAAC,CAAA;aAChE;SACF;IACH,CAAC;IAED,sCAAY,GAAZ,UAAa,CAAwB,EAAE,MAAgB;QACrD,OAAO,CACL,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,QAAQ;YACb,mBAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAChC,CAAA;IACH,CAAC;IAES,yCAAe,GAAzB,UAA0B,SAAmB;QAC3C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACvB,OAAM;SACP;QACD,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAU,CAAA;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAA;QACnE,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAA;QACrE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,SAAS,EAAE;gBACb,UAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBAChC,UAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aACrC;iBAAM;gBACL,UAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBACnC,UAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aAClC;SACF;aAAM;YACL,UAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;YACnC,UAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;SACrC;IACH,CAAC;IAED,uCAAa,GAAb;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;YAClC,IAAI,CAAC,eAAe,EAAE,CAAA;YAEtB,OAAO;YACP,wBAAwB;YACxB,6CAA6C;YAC7C,8CAA8C;YAC9C,MAAM;YACN,MAAM;YACN,6CAA6C;YAC7C,IAAI;SACL;IACH,CAAC;IAED,wCAAc,GAAd;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;YACnC,IAAI,CAAC,eAAe,EAAE,CAAA;SACvB;IACH,CAAC;IAED,8BAAI,GAAJ;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;SACnB;IACH,CAAC;IAED,gCAAM,GAAN;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAED,gCAAM,GAAN;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAED,0CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;SAC/B;IACH,CAAC;IAED,2CAAiB,GAAjB;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAA;SAChC;IACH,CAAC;IAED,gCAAM,GAAN,UAAO,KAAc,EAAE,MAAe;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;SAClC;IACH,CAAC;IAGD,iCAAO,GAAP;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;SACtB;QACD,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;IALD;QADC,WAAI,CAAC,OAAO,EAAE;kDAMd;IACH,sBAAC;CAAA,AA7ID,CAAqC,WAAI,GA6IxC;AA7IY,0CAAe"}