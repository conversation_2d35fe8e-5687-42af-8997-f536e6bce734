{"version": 3, "file": "scroller.js", "sourceRoot": "", "sources": ["../../src/graph/scroller.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,SAAS,CAAA;AAC7B,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAA;AAEtC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAA;AAE7B,MAAM,OAAO,eAAgB,SAAQ,IAAI;IAGvC,IAAc,aAAa;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;IAC9B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,IAAI,CAAA;IACnE,CAAC;IAES,IAAI;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,CAAA;QAC9C,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACpE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACtE,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC5D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QACrE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IACvE,CAAC;IAES,cAAc,CAAC,EAAE,CAAC,EAAgC;QAC1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IACE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;gBAC1B,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EACxE;gBACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAA;aAChE;SACF;IACH,CAAC;IAED,YAAY,CAAC,CAAwB,EAAE,MAAgB;QACrD,OAAO,CACL,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,QAAQ;YACb,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAChC,CAAA;IACH,CAAC;IAES,eAAe,CAAC,SAAmB;QAC3C,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACvB,OAAM;SACP;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAU,CAAA;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAA;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAA;QACrE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,SAAS,EAAE;gBACb,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBAChC,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aACrC;iBAAM;gBACL,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;gBACnC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;aAClC;SACF;aAAM;YACL,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;YACnC,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;SACrC;IACH,CAAC;IAED,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;YAClC,IAAI,CAAC,eAAe,EAAE,CAAA;YAEtB,OAAO;YACP,wBAAwB;YACxB,6CAA6C;YAC7C,8CAA8C;YAC9C,MAAM;YACN,MAAM;YACN,6CAA6C;YAC7C,IAAI;SACL;IACH,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;YACnC,IAAI,CAAC,eAAe,EAAE,CAAA;SACvB;IACH,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;SACnB;IACH,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;SACrB;IACH,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;SAC/B;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAA;SAChC;IACH,CAAC;IAED,MAAM,CAAC,KAAc,EAAE,MAAe;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;SAClC;IACH,CAAC;IAGD,OAAO;QACL,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;SACtB;QACD,IAAI,CAAC,aAAa,EAAE,CAAA;IACtB,CAAC;CACF;AANC;IADC,IAAI,CAAC,OAAO,EAAE;8CAMd"}