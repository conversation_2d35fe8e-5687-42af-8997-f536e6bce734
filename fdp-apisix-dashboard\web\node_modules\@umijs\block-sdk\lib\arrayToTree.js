"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = arrayToTree;

function _react() {
  const data = _interopRequireDefault(require("react"));

  _react = function _react() {
    return data;
  };

  return data;
}

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === "undefined" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

const defaultConfig = {
  id: 'id',
  parentId: 'parentId',
  dataField: 'data'
};
/**
 * Unflattens an array to a tree with runtime O(n)
 */

function arrayToTree(items, config = {}) {
  const conf = _objectSpread(_objectSpread({}, defaultConfig), config); // the resulting unflattened tree


  const rootItems = []; // stores all already processed items with ther ids as key so we can easily look them up

  const lookup = {}; // idea of this loop:
  // whenever an item has a parent, but the parent is not yet in the lookup object, we store a preliminary parent
  // in the lookup object and fill it with the data of the parent later
  // if an item has no parentId, add it as a root element to rootItems

  var _iterator = _createForOfIteratorHelper(items),
      _step;

  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      const item = _step.value;
      const itemId = item[conf.id];
      const parentId = item[conf.parentId]; // look whether item already exists in the lookup table

      if (!Object.prototype.hasOwnProperty.call(lookup, itemId)) {
        // item is not yet there, so add a preliminary item (its data will be added later)
        lookup[itemId] = {
          children: []
        };
      } // add the current item's data to the item in the lookup table


      if (conf.dataField) {
        lookup[itemId][conf.dataField] = item;
      } else {
        lookup[itemId] = _objectSpread(_objectSpread({}, item), {}, {
          children: lookup[itemId].children
        });
      }

      const TreeItem = lookup[itemId];

      if (parentId === null) {
        // is a root item
        rootItems.push(TreeItem);
      } else {
        // has a parent
        // look whether the parent already exists in the lookup table
        if (!Object.prototype.hasOwnProperty.call(lookup, parentId)) {
          // parent is not yet there, so add a preliminary parent (its data will be added later)
          lookup[parentId] = {
            children: []
          };
        } // add the current item to the parent


        lookup[parentId].children.push(TreeItem);
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }

  return rootItems;
}