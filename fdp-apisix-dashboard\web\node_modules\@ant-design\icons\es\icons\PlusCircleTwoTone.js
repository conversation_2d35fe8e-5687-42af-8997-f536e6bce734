// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PlusCircleTwoTone";
import AntdIcon from '../components/AntdIcon';

var PlusCircleTwoTone = function PlusCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PlusCircleTwoToneSvg
  }));
};

PlusCircleTwoTone.displayName = 'PlusCircleTwoTone';
export default /*#__PURE__*/React.forwardRef(PlusCircleTwoTone);