{"api": {"PerformanceResourceTiming": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": "10"}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "connectEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/connectEnd", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": "10"}, "opera": {"version_added": "32"}, "opera_android": {"version_added": "32"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "connectStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/connectStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": "10"}, "opera": {"version_added": "32"}, "opera_android": {"version_added": "32"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "decodedBodySize": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/decodedBodySize", "support": {"chrome": {"version_added": "54"}, "chrome_android": {"version_added": "54"}, "edge": {"version_added": "17"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": "41"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "54"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "domainLookupEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/domainLookupEnd", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "domainLookupStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/domainLookupStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "encodedBodySize": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/encodedBodySize", "support": {"chrome": {"version_added": "54"}, "chrome_android": {"version_added": "54"}, "edge": {"version_added": "17"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": "41"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "54"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "fetchStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/fetchStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "initiatorType": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/initiatorType", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "nextHopProtocol": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/nextHopProtocol", "support": {"chrome": {"version_added": "61"}, "chrome_android": {"version_added": "61"}, "edge": {"version_added": "17"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "61"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "redirectEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/redirectEnd", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "redirectStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/redirectStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "requestStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/requestStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "responseEnd": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/responseEnd", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "responseStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/responseStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "secureConnectionStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/secureConnectionStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "18"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "serverTiming": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/serverTiming", "support": {"chrome": {"version_added": "65"}, "chrome_android": {"version_added": "65"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "61"}, "firefox_android": {"version_added": "61"}, "ie": {"version_added": false}, "opera": {"version_added": "52"}, "opera_android": {"version_added": "47"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "65"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "toJSON": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/toJSON", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "≤18"}, "firefox": {"version_added": "40"}, "firefox_android": {"version_added": "42"}, "ie": {"version_added": false}, "opera": {"version_added": "30"}, "opera_android": {"version_added": "30"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "transferSize": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/transferSize", "support": {"chrome": {"version_added": "54"}, "chrome_android": {"version_added": "54"}, "edge": {"version_added": "17"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": "41"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "54"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "worker_support": {"__compat": {"description": "Available in workers", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "≤18"}, "firefox": {"version_added": "60"}, "firefox_android": {"version_added": "60"}, "ie": {"version_added": false}, "opera": {"version_added": "32"}, "opera_android": {"version_added": "32"}, "safari": {"version_added": "12"}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "workerStart": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceResourceTiming/workerStart", "support": {"chrome": {"version_added": "43"}, "chrome_android": {"version_added": "43"}, "edge": {"version_added": "16"}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": "58"}, "ie": {"version_added": false}, "opera": {"version_added": "32"}, "opera_android": {"version_added": "32"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "4.0"}, "webview_android": {"version_added": "43"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}