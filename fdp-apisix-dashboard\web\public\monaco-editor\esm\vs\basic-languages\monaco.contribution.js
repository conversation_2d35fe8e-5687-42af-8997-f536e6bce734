import '../editor/editor.api.js';
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.32.1(29a273516805a852aa8edc5e05059f119b13eff0)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/

// src/basic-languages/monaco.contribution.ts
import "./abap/abap.contribution.js";
import "./apex/apex.contribution.js";
import "./azcli/azcli.contribution.js";
import "./bat/bat.contribution.js";
import "./bicep/bicep.contribution.js";
import "./cameligo/cameligo.contribution.js";
import "./clojure/clojure.contribution.js";
import "./coffee/coffee.contribution.js";
import "./cpp/cpp.contribution.js";
import "./csharp/csharp.contribution.js";
import "./csp/csp.contribution.js";
import "./css/css.contribution.js";
import "./dart/dart.contribution.js";
import "./dockerfile/dockerfile.contribution.js";
import "./ecl/ecl.contribution.js";
import "./elixir/elixir.contribution.js";
import "./flow9/flow9.contribution.js";
import "./fsharp/fsharp.contribution.js";
import "./freemarker2/freemarker2.contribution.js";
import "./go/go.contribution.js";
import "./graphql/graphql.contribution.js";
import "./handlebars/handlebars.contribution.js";
import "./hcl/hcl.contribution.js";
import "./html/html.contribution.js";
import "./ini/ini.contribution.js";
import "./java/java.contribution.js";
import "./javascript/javascript.contribution.js";
import "./julia/julia.contribution.js";
import "./kotlin/kotlin.contribution.js";
import "./less/less.contribution.js";
import "./lexon/lexon.contribution.js";
import "./lua/lua.contribution.js";
import "./liquid/liquid.contribution.js";
import "./m3/m3.contribution.js";
import "./markdown/markdown.contribution.js";
import "./mips/mips.contribution.js";
import "./msdax/msdax.contribution.js";
import "./mysql/mysql.contribution.js";
import "./objective-c/objective-c.contribution.js";
import "./pascal/pascal.contribution.js";
import "./pascaligo/pascaligo.contribution.js";
import "./perl/perl.contribution.js";
import "./pgsql/pgsql.contribution.js";
import "./php/php.contribution.js";
import "./pla/pla.contribution.js";
import "./postiats/postiats.contribution.js";
import "./powerquery/powerquery.contribution.js";
import "./powershell/powershell.contribution.js";
import "./protobuf/protobuf.contribution.js";
import "./pug/pug.contribution.js";
import "./python/python.contribution.js";
import "./qsharp/qsharp.contribution.js";
import "./r/r.contribution.js";
import "./razor/razor.contribution.js";
import "./redis/redis.contribution.js";
import "./redshift/redshift.contribution.js";
import "./restructuredtext/restructuredtext.contribution.js";
import "./ruby/ruby.contribution.js";
import "./rust/rust.contribution.js";
import "./sb/sb.contribution.js";
import "./scala/scala.contribution.js";
import "./scheme/scheme.contribution.js";
import "./scss/scss.contribution.js";
import "./shell/shell.contribution.js";
import "./solidity/solidity.contribution.js";
import "./sophia/sophia.contribution.js";
import "./sparql/sparql.contribution.js";
import "./sql/sql.contribution.js";
import "./st/st.contribution.js";
import "./swift/swift.contribution.js";
import "./systemverilog/systemverilog.contribution.js";
import "./tcl/tcl.contribution.js";
import "./twig/twig.contribution.js";
import "./typescript/typescript.contribution.js";
import "./vb/vb.contribution.js";
import "./xml/xml.contribution.js";
import "./yaml/yaml.contribution.js";
