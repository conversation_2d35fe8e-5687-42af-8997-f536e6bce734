{"version": 3, "file": "selection.js", "sourceRoot": "", "sources": ["../../src/graph/selection.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,kCAAsC;AAKtC,+BAA6B;AAE7B;IAAsC,oCAAI;IAA1C;QAAA,qEAgPC;QA9OS,cAAQ,GAAG,IAAI,OAAO,EAAiB,CAAA;QACvC,iBAAW,GAAG,IAAI,OAAO,EAAiB,CAAA;;IA6OpD,CAAC;IA3OC,sBAAc,2CAAa;aAA3B;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;QAC/B,CAAC;;;OAAA;IAED,sBAAI,gDAAkB;aAAtB;YACE,OAAO,CACL,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI;gBACnC,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,IAAI,CACvC,CAAA;QACH,CAAC;;;OAAA;IAED,sBAAW,sCAAQ;aAAnB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI,CAAA;QAC5C,CAAC;;;OAAA;IAED,sBAAW,oCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;QAC3B,CAAC;;;OAAA;IAED,sBAAW,mCAAK;aAAhB;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;QAC1B,CAAC;;;OAAA;IAES,+BAAI,GAAd;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAA;QAC/C,IAAI,CAAC,cAAc,EAAE,CAAA;IACvB,CAAC;IAES,yCAAc,GAAxB;QACE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QAC7D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;QACvD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAES,wCAAa,GAAvB;QACE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QAC9D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC5D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IAC7D,CAAC;IAES,2CAAgB,GAA1B,UAA2B,EAAmC;YAAjC,CAAC,OAAA;QAC5B,IACE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC;YAC7B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;gBACtB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;gBAC1C,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAC5C;YACA,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;SACxB;aAAM;YACL,IAAI,CAAC,KAAK,EAAE,CAAA;SACb;IACH,CAAC;IAED,0CAAe,GAAf,UAAgB,CAAwB,EAAE,MAAgB;QACxD,OAAO,CACL,CAAC,IAAI,CAAC,kBAAkB;YACxB,mBAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CACnC,CAAA;IACH,CAAC;IAES,0CAAe,GAAzB,UAA0B,EAAqC;YAAnC,IAAI,UAAA;QAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAES,wCAAa,GAAvB,UAAwB,EAAsC;YAApC,CAAC,OAAA,EAAE,IAAI,UAAA;QAC/B,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAA;QAClC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACxC,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAA;YAE9C,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAA;aAChE;YAED,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAA;aAChE;SACF;QAED,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;gBAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;aACjB;iBAAM,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aAC9B;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;aACpB;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aAClB;SACF;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAES,yCAAc,GAAxB,UAAyB,EAAiD;YAA/C,CAAC,OAAA,EAAE,IAAI,UAAA;QAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE;gBACrE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;aACjC;SACF;IACH,CAAC;IAED,kCAAO,GAAP;QACE,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,qCAAU,GAAV,UAAW,IAAmB;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAES,mCAAQ,GAAlB,UAAmB,KAAwC;QAA3D,iBAMC;QALC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aAC5C,GAAG,CAAC,UAAC,IAAI;YACR,OAAA,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;QAA9D,CAA8D,CAC/D;aACA,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,IAAI,IAAI,EAAZ,CAAY,CAAC,CAAA;IACnC,CAAC;IAED,iCAAM,GAAN,UACE,KAAwC,EACxC,OAAmC;QAAnC,wBAAA,EAAA,YAAmC;QAEnC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;aACtC;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;aAC1C;SACF;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,mCAAQ,GAAR,UACE,KAAwC,EACxC,OAAsC;QAAtC,wBAAA,EAAA,YAAsC;QAEtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,gCAAK,GAAL,UACE,KAAyC,EACzC,OAAmC;QAAnC,wBAAA,EAAA,YAAmC;QAEnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAA;IACb,CAAC;IAED,gCAAK,GAAL;QACE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,iCAAM,GAAN;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAA;SAClC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kCAAO,GAAP;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAA;SACnC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,0CAAe,GAAf,UAAgB,CAAwB;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;SAC9B;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,2CAAgB,GAAhB;QACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAA;YACpC,OAAO;YACP,wBAAwB;YACxB,6CAA6C;YAC7C,8CAA8C;YAC9C,MAAM;YACN,MAAM;YACN,yCAAyC;YACzC,IAAI;SACL;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,4CAAiB,GAAjB;QACE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,KAAK,CAAA;SACtC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qCAAU,GAAV;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,KAAK,CAAA;IAC9C,CAAC;IAED,yCAAc,GAAd;QACE,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;QAClC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,0CAAe,GAAf;QACE,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;QACnC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,uCAAY,GAAZ,UAAa,SAAyC;QACpD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qCAAU,GAAV,UAAW,OAA2B;QACpC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QAC/B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,oCAAS,GAAT,UAAU,MAAyB;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,OAAO,IAAI,CAAA;IACb,CAAC;IAGD,kCAAO,GAAP;QACE,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAHD;QADC,WAAI,CAAC,OAAO,EAAE;mDAId;IACH,uBAAC;CAAA,AAhPD,CAAsC,WAAI,GAgPzC;AAhPY,4CAAgB"}