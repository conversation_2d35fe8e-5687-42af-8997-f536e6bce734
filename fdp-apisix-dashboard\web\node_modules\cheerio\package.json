{"name": "cheerio", "version": "1.0.0-rc.3", "description": "Tiny, fast, and elegant implementation of core jQuery designed specifically for the server", "author": "<PERSON> <<EMAIL>> (mat.io)", "license": "MIT", "keywords": ["htmlparser", "j<PERSON>y", "selector", "scraper", "parser", "html"], "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio.git"}, "main": "./index.js", "files": ["index.js", "lib"], "engines": {"node": ">= 0.6"}, "dependencies": {"css-select": "~1.2.0", "dom-serializer": "~0.1.1", "entities": "~1.1.1", "htmlparser2": "^3.9.1", "lodash": "^4.15.0", "parse5": "^3.0.1"}, "devDependencies": {"benchmark": "^2.1.0", "coveralls": "^2.11.9", "expect.js": "~0.3.1", "istanbul": "^0.4.3", "jquery": "^3.0.0", "jsdom": "^9.2.1", "jshint": "^2.9.2", "mocha": "^3.1.2", "xyz": "~1.1.0"}, "scripts": {"test": "make test"}}