{"name": "eslint-import-resolver-node", "version": "0.3.6", "description": "Node default behavior import resolution plugin for eslint-plugin-import.", "main": "index.js", "files": ["index.js"], "scripts": {"prepublishOnly": "cp ../../{LICENSE,.npmrc} ./", "tests-only": "nyc mocha", "test": "npm run tests-only", "coveralls": "nyc report --reporter l<PERSON><PERSON><PERSON> && cd ../.. && coveralls < ./resolvers/node/coverage/lcov.info"}, "repository": {"type": "git", "url": "https://github.com/import-js/eslint-plugin-import"}, "keywords": ["eslint", "eslintplugin", "esnext", "modules", "eslint-plugin-import"], "author": "<PERSON> (<EMAIL>)", "license": "MIT", "bugs": {"url": "https://github.com/import-js/eslint-plugin-import/issues"}, "homepage": "https://github.com/import-js/eslint-plugin-import", "dependencies": {"debug": "^3.2.7", "resolve": "^1.20.0"}, "devDependencies": {"chai": "^3.5.0", "coveralls": "^3.1.0", "mocha": "^3.5.3", "nyc": "^11.9.0"}}