// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusSquareTwoToneSvg from "@ant-design/icons-svg/es/asn/PlusSquareTwoTone";
import AntdIcon from '../components/AntdIcon';

var PlusSquareTwoTone = function PlusSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PlusSquareTwoToneSvg
  }));
};

PlusSquareTwoTone.displayName = 'PlusSquareTwoTone';
export default /*#__PURE__*/React.forwardRef(PlusSquareTwoTone);