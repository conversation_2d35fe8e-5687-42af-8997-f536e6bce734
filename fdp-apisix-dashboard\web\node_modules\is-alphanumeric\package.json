{"name": "is-alphanumeric", "version": "1.0.0", "description": "Check if a string only contains alphanumeric characters", "license": "MIT", "repository": "arthurvr/is-alphanumeric", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "arthurverschaeve.be"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["numbers", "numeric", "alphabet", "alphabetic", "check", "is", "detect", "latin", "alphanumeric", "string", "text", "letters", "digit", "arabic", "alphameric"], "devDependencies": {"mocha": "*"}}