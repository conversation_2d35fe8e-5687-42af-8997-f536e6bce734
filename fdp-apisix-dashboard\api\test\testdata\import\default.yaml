#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# If you want to set the specified configuration value, you can set the new
# in this file. For example if you want to specify the etcd address:
#
openapi: 3.0.0
info:
  version: 1.0.0-oas3
  description: test desc
  license:
    name: Apache License 2.0
    url: 'http://www.apache.org/licenses/LICENSE-2.0'
  title: test title
paths:
  /hello:
    get:
      x-api-limit: 20
      description: hello world.
      operationId: hello
      x-apisix-upstream:
        type: roundrobin
        nodes:
          - host: *************
            port: 1980
            weight: 1
            priority: 10
      responses:
        '200':
          description: list response
        default:
          description: unexpected error
