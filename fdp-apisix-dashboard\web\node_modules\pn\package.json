{"name": "pn", "version": "1.1.0", "description": "Promisify the node standard library.", "scripts": {"generate": "scripts/generate.js", "test": "mocha"}, "repository": {"type": "git", "url": "git://github.com/cscott/node-pn"}, "keywords": ["promise", "node"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/cscott/node-pn/issues"}, "devDependencies": {"es6-shim": "~0.35.3", "mocha": "~3.5.0", "prfun": "~2.1.4"}}