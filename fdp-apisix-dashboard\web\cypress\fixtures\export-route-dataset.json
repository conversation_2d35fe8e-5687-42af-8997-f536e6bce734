{"jsonFile": {"components": {}, "info": {"title": "RoutesExport", "version": "3.0.0"}, "openapi": "3.0.0", "paths": {"/{params}": {"post": {"operationId": "route_name_0POST", "parameters": [{"description": "params in path", "in": "path", "name": "params", "required": true, "schema": {"type": "string"}}], "requestBody": {}, "responses": {"default": {"description": ""}}, "x-apisix-enable_websocket": false, "x-apisix-priority": 0, "x-apisix-status": 1, "x-apisix-upstream": {"nodes": [{"host": "*******", "port": 80, "weight": 1}], "timeout": {"connect": 6, "send": 6, "read": 6}, "type": "roundrobin", "scheme": "http", "pass_host": "pass", "keepalive_pool": {"idle_timeout": 60, "requests": 1000, "size": 320}}}}}}, "yamlFile": {"components": {}, "info": {"title": "RoutesExport", "version": "3.0.0"}, "openapi": "3.0.0", "paths": {"/{params}": {"post": {"operationId": "route_name_0POST", "parameters": [{"description": "params in path", "in": "path", "name": "params", "required": true, "schema": {"type": "string"}}], "requestBody": {}, "responses": {"default": {"description": ""}}, "x-apisix-enable_websocket": false, "x-apisix-priority": 0, "x-apisix-status": 1, "x-apisix-upstream": {"nodes": [{"host": "*******", "port": 80, "weight": 1}], "timeout": {"connect": 6, "send": 6, "read": 6}, "type": "roundrobin", "scheme": "http", "pass_host": "pass", "keepalive_pool": {"idle_timeout": 60, "requests": 1000, "size": 320}}}}, "/{params}-APISIX-REPEAT-URI-2": {"post": {"operationId": "route_name_1POST", "parameters": [{"description": "params in path", "in": "path", "name": "params", "required": true, "schema": {"type": "string"}}, {"description": "params in path", "in": "path", "name": "params", "required": true, "schema": {"type": "string"}}], "requestBody": {}, "responses": {"default": {"description": ""}}, "x-apisix-enable_websocket": false, "x-apisix-priority": 0, "x-apisix-status": 1, "x-apisix-upstream": {"nodes": [{"host": "*******", "port": 80, "weight": 1}], "timeout": {"connect": 6, "send": 6, "read": 6}, "type": "roundrobin", "scheme": "http", "pass_host": "pass", "keepalive_pool": {"idle_timeout": 60, "requests": 1000, "size": 320}}}}}}}