{"api": {"PresentationAvailability": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationAvailability", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "onchange": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationAvailability/onchange", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "value": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationAvailability/value", "support": {"chrome": {"version_added": "48"}, "chrome_android": {"version_added": "48"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.controller.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}