// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProfileOutlinedSvg from "@ant-design/icons-svg/es/asn/ProfileOutlined";
import AntdIcon from '../components/AntdIcon';

var ProfileOutlined = function ProfileOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: ProfileOutlinedSvg
  }));
};

ProfileOutlined.displayName = 'ProfileOutlined';
export default /*#__PURE__*/React.forwardRef(ProfileOutlined);