{"api": {"PictureInPicture": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PictureInPicture", "support": {"chrome": {"version_added": "69", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "chrome_android": {"version_added": false}, "edge": {"version_added": "≤79", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}, "height": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PictureInPicture/height", "support": {"chrome": {"version_added": "69", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "chrome_android": {"version_added": false}, "edge": {"version_added": "≤79", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "onresize": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PictureInPicture/onresize", "support": {"chrome": {"version_added": "69", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "chrome_android": {"version_added": false}, "edge": {"version_added": "≤79", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "width": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PictureInPicture/width", "support": {"chrome": {"version_added": "69", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "chrome_android": {"version_added": false}, "edge": {"version_added": "≤79", "flags": [{"type": "preference", "name": "enable-experimental-web-platform-features", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-surfaces-for-videos", "value_to_set": "enabled"}, {"type": "preference", "name": "enable-picture-in-picture", "value_to_set": "enabled"}]}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": null}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}}}}