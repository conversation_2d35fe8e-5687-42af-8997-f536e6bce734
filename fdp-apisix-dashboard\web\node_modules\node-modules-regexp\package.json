{"name": "node-modules-regexp", "version": "1.0.0", "description": "A regular expression for file paths that contain a `node_modules` folder.", "license": "MIT", "repository": "jamestalmage/node-modules-regexp", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["node_modules", "regular expression", "regular expressions", "regular", "expression", "expressions", "exclude", "include", "ignore", "node", "module"], "dependencies": {}, "devDependencies": {"ava": "^0.7.0", "xo": "^0.11.2"}, "xo": {"ignores": ["test.js"]}}