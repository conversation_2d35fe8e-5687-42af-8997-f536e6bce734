{"name": "pkg-conf", "version": "2.1.0", "description": "Get namespaced config from the closest package.json", "license": "MIT", "repository": "sindresorhus/pkg-conf", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["json", "read", "parse", "file", "fs", "graceful", "load", "pkg", "package", "config", "conf", "configuration", "object", "namespace", "namespaced"], "dependencies": {"find-up": "^2.0.0", "load-json-file": "^4.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "fixture": {"foo": true}}