apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-config
  labels:
    app: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
data:
  conf.yaml: |
    conf:
      listen:
        port: {{ .Values.service.port }}
      apisix:
        base_url: "{{ .Values.config.apisix.baseUrl }}"

      etcd:
        endpoints:
          {{- range .Values.config.etcd.endpoints }}
          - {{ . }}
          {{- end }}

      log:
        error_log:
          level: warn
          file_path:
            logs/error.log
        access_log:
          file_path:
            logs/access.log
      max_cpu: 0

    authentication:
      secret: {{ .Values.config.authentication.secret }}
      expire_time: {{ .Values.config.authentication.expireTime }}
      users:
        {{- range .Values.config.authentication.users }}
        - username: {{ .username }}
          password: {{ .password }}
        {{- end }}

    oidc:
      enabled: false 

    plugins:
      - api-breaker
      - authz-casbin
      - authz-casdoor
      - authz-keycloak
      - aws-lambda
      - azure-functions
      - basic-auth
      # - batch-requests
      - clickhouse-logger
      - client-control
      - consumer-restriction
      - cors
      - csrf
      - datadog
      # - dubbo-proxy
      - echo
      - error-log-logger
      # - example-plugin
      - ext-plugin-post-req
      - ext-plugin-post-resp
      - ext-plugin-pre-req
      - fault-injection
      - file-logger
      - forward-auth
      - google-cloud-logging
      - grpc-transcode
      - grpc-web
      - gzip
      - hmac-auth
      - http-logger
      - ip-restriction
      - jwt-auth
      - kafka-logger
      - kafka-proxy
      - key-auth
      - ldap-auth
      - limit-conn
      - limit-count
      - limit-req
      - loggly
      # - log-rotate
      - mocking
      # - node-status
      - opa
      - openid-connect
      - opentelemetry
      - openwhisk
      - prometheus
      - proxy-cache
      - proxy-control
      - proxy-mirror
      - proxy-rewrite
      - public-api
      - real-ip
      - redirect
      - referer-restriction
      - request-id
      - request-validation
      - response-rewrite
      - rocketmq-logger
      - server-info
      - serverless-post-function
      - serverless-pre-function
      - skywalking
      - skywalking-logger
      - sls-logger
      - splunk-hec-logging
      - syslog
      - tcp-logger
      - traffic-split
      - ua-restriction
      - udp-logger
      - uri-blocker
      - wolf-rbac
      - zipkin
      - elasticsearch-logge
      - openfunction
      - tencent-cloud-cls
      - ai
      - cas-auth