// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import ProfileFilledSvg from "@ant-design/icons-svg/es/asn/ProfileFilled";
import AntdIcon from '../components/AntdIcon';

var ProfileFilled = function ProfileFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: ProfileFilledSvg
  }));
};

ProfileFilled.displayName = 'ProfileFilled';
export default /*#__PURE__*/React.forwardRef(ProfileFilled);