{"version": 3, "file": "stroke.js", "sourceRoot": "", "sources": ["../../../src/registry/highlighter/stroke.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,YAAY,CAAA;AACnD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAA;AAWnC,MAAM,cAAc,GAA6B;IAC/C,OAAO,EAAE,CAAC;IACV,EAAE,EAAE,CAAC;IACL,EAAE,EAAE,CAAC;IACL,KAAK,EAAE;QACL,cAAc,EAAE,CAAC;QACjB,MAAM,EAAE,SAAS;KAClB;CACF,CAAA;AAED,MAAM,CAAC,MAAM,MAAM,GAAqD;IACtE,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO;QACjC,MAAM,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACpD,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;YACxB,OAAM;SACP;QAED,2BAA2B;QAC3B,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QAE7D,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAoB,CAAC,CAAA;QACrD,IAAI,QAAQ,CAAA;QACZ,IAAI,UAAU,CAAA;QAEd,IAAI;YACF,QAAQ,GAAG,SAAS,CAAC,UAAU,EAAE,CAAA;SAClC;QAAC,OAAO,KAAK,EAAE;YACd,+CAA+C;YAC/C,wDAAwD;YACxD,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;YAC1D,QAAQ,GAAG,GAAG,CAAC,cAAc,iCAAM,OAAO,GAAK,UAAU,EAAG,CAAA;SAC7D;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACzC,GAAG,CAAC,IAAI,CAAC,IAAI,kBACX,CAAC,EAAE,QAAQ,EACX,gBAAgB,EAAE,MAAM,EACxB,eAAe,EAAE,oBAAoB,EACrC,IAAI,EAAE,MAAM,IACT,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAC7D,CAAA;QAEF,+CAA+C;QAE/C,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;YAClC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAG,QAAqB,CAAC,qBAAqB,EAAE,CAAC,CAAA;SACpE;aAAM;YACL,IAAI,eAAe,GAAG,SAAS,CAAC,qBAAqB,CACnD,QAAQ,CAAC,SAAuB,CACjC,CAAA;YAED,wCAAwC;YACxC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;YAC/B,IAAI,OAAO,EAAE;gBACX,IAAI,UAAU,IAAI,IAAI,EAAE;oBACtB,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBAClC;gBAED,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAA;gBAC9C,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;gBAE/C,UAAU,GAAG,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAA;gBAEhE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;gBAC7C,MAAM,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,CAAA;gBACpC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,MAAM,CAAA;gBAEtC,MAAM,aAAa,GAAG,GAAG,CAAC,eAAe,CAAC;oBACxC,CAAC,EAAE,EAAE;oBACL,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,CAAC;oBACJ,CAAC,EAAE,EAAE;oBACL,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;oBACf,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;iBAChB,CAAC,CAAA;gBAEF,eAAe,GAAG,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;aAC1D;YAED,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;SACrC;QAED,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAA;QAEnD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC1B,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;QAEzD,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;QACjC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;SACxC;QAED,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACpC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG;QAC/B,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;IAClE,CAAC;CACF,CAAA;AAED,IAAU,OAAO,CA0BhB;AA1BD,WAAU,OAAO;IACf,SAAgB,gBAAgB,CAC9B,MAAe,EACf,OAAiC;QAEjC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACpB,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IAC5C,CAAC;IANe,wBAAgB,mBAM/B,CAAA;IAED,MAAM,KAAK,GAA8B,EAAE,CAAA;IAE3C,SAAgB,QAAQ,CAAC,EAAU,EAAE,IAAa;QAChD,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;IAClB,CAAC;IAFe,gBAAQ,WAEvB,CAAA;IAED,SAAgB,QAAQ,CAAC,EAAU;QACjC,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,CAAA;IAC1B,CAAC;IAFe,gBAAQ,WAEvB,CAAA;IAED,SAAgB,iBAAiB,CAAC,EAAU;QAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,CAAA;QACtB,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAChB,OAAO,KAAK,CAAC,EAAE,CAAC,CAAA;SACjB;IACH,CAAC;IANe,yBAAiB,oBAMhC,CAAA;AACH,CAAC,EA1BS,OAAO,KAAP,OAAO,QA0BhB"}