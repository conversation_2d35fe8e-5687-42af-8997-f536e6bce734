{"css": {"types": {"position": {"__compat": {"description": "<code>&lt;position&gt;</code>", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/position_value", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "1"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "4"}, "opera": {"version_added": "3.5"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "1"}, "safari_ios": {"version_added": "1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "four_value_syntax": {"__compat": {"description": "Four-value syntax for offset from any edge", "support": {"chrome": {"version_added": "25"}, "chrome_android": {"version_added": "25"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "13"}, "firefox_android": {"version_added": "14"}, "ie": {"version_added": "9"}, "opera": {"version_added": "10.5"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "7"}, "safari_ios": {"version_added": "7"}, "samsunginternet_android": {"version_added": "1.5"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "keyword_value_syntax": {"__compat": {"description": "Syntax combining a keyword and <code>&lt;length&gt;</code> or <code>&lt;percentage&gt;</code>", "support": {"chrome": {"version_added": "1"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "12"}, "firefox": {"version_added": "1"}, "firefox_android": {"version_added": "4"}, "ie": {"version_added": "4"}, "opera": {"version_added": "3.5"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "1"}, "safari_ios": {"version_added": "1"}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": "≤37"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "three_value_syntax": {"__compat": {"description": "Three-value syntax for properties other than background-position", "support": {"chrome": {"version_added": "25", "version_removed": "68"}, "chrome_android": {"version_added": "25", "version_removed": "68"}, "edge": {"version_added": "12", "version_removed": "79"}, "firefox": {"version_added": "13", "version_removed": "70"}, "firefox_android": {"version_added": "14"}, "ie": {"version_added": "9"}, "opera": {"version_added": "10.5", "version_removed": "55"}, "opera_android": {"version_added": "14", "version_removed": "48"}, "safari": {"version_added": "1"}, "safari_ios": {"version_added": "1"}, "samsunginternet_android": {"version_added": "1.5", "version_removed": "10.0"}, "webview_android": {"version_added": "≤37", "version_removed": "68"}}, "status": {"experimental": false, "standard_track": true, "deprecated": true}}}}}}}