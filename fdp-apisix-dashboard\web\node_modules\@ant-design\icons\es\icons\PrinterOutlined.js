// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PrinterOutlinedSvg from "@ant-design/icons-svg/es/asn/PrinterOutlined";
import AntdIcon from '../components/AntdIcon';

var PrinterOutlined = function PrinterOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PrinterOutlinedSvg
  }));
};

PrinterOutlined.displayName = 'PrinterOutlined';
export default /*#__PURE__*/React.forwardRef(PrinterOutlined);