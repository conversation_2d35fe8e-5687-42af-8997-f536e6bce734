{"name": "blink-diff", "version": "1.0.13", "description": "A lightweight image comparison tool", "license": "MIT", "main": "index.js", "bugs": "https://github.com/yahoo/blink-diff/issues", "homepage": "https://github.com/yahoo/blink-diff", "bin": {"blink-diff": "bin/blink-diff"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.marcelerz.com"}, "repository": {"type": "git", "url": "https://github.com/yahoo/blink-diff.git"}, "keywords": ["image-diff", "visual-diff", "diff", "testing", "blink", "image", "difference", "compare"], "scripts": {"test": "istanbul cover -- _mocha --reporter spec", "docs": "yuidoc ."}, "dependencies": {"pngjs-image": "~0.11.5", "preceptor-core": "~0.10.0", "promise": "6.0.0"}, "devDependencies": {"chai": "1.9.2", "coveralls": "2.11.2", "codeclimate-test-reporter": "0.0.4", "istanbul": "0.3.2", "mocha": "1.21.4", "sinon": "1.12.2", "sinon-chai": "2.7.0", "yuidocjs": "0.3.50"}}