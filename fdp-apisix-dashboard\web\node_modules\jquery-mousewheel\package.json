{"name": "jquery-mousewheel", "version": "3.1.13", "author": {"name": "jQuery Foundation and other contributors", "url": "https://github.com/jquery/jquery-mousewheel/blob/master/AUTHORS.txt"}, "description": "A jQuery plugin that adds cross-browser mouse wheel support.", "licenses": [{"type": "MIT", "url": "https://github.com/jquery/jquery-mousewheel/blob/master/LICENSE.txt"}], "homepage": "https://github.com/jquery/jquery-mousewheel", "main": "./jquery.mousewheel.js", "repository": {"type": "git", "url": "https://github.com/jquery/jquery-mousewheel.git"}, "bugs": {"url": "https://github.com/jquery/jquery-mousewheel/issues"}, "keywords": ["j<PERSON>y", "mouse", "wheel", "event", "mousewheel", "jquery-plugin", "browser"], "files": ["ChangeLog.md", "jquery.mousewheel.js", "README.md", "LICENSE.txt"], "devDependencies": {"grunt": "~0.4.1", "grunt-contrib-connect": "~0.5.0", "grunt-contrib-jshint": "~0.7.1", "grunt-contrib-uglify": "~0.2.7"}, "directories": {"test": "test"}, "jam": {"dependencies": {"jquery": ">=1.2.2"}}}