{"api": {"PerformanceObserverEntryList": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserverEntryList", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0", "notes": "Stability: Experimental"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "getEntries": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserverEntryList/getEntries", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "getEntriesByName": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserverEntryList/getEntriesByName", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "getEntriesByType": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserverEntryList/getEntriesByType", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}