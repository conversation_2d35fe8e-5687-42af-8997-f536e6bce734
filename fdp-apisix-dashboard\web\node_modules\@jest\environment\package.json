{"name": "@jest/environment", "version": "25.5.0", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-environment"}, "license": "MIT", "main": "build/index.js", "types": "build/index.d.ts", "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "dependencies": {"@jest/fake-timers": "^25.5.0", "@jest/types": "^25.5.0", "jest-mock": "^25.5.0"}, "devDependencies": {"@types/node": "*"}, "engines": {"node": ">= 8.3"}, "publishConfig": {"access": "public"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7"}