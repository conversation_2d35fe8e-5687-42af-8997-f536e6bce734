{"name": "postcss-selector-parser", "version": "5.0.0", "devDependencies": {"ava": "^0.25.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.1.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0", "coveralls": "^3.0.2", "del-cli": "^1.1.0", "eslint": "^4.19.1", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^2.14.0", "glob": "^7.1.3", "minimist": "^1.2.0", "nyc": "^11.7.3", "postcss": "^7.0.7", "semver": "^5.6.0"}, "main": "dist/index.js", "types": "postcss-selector-parser.d.ts", "files": ["API.md", "CHANGELOG.md", "LICENSE-MIT", "dist", "postcss-selector-parser.d.ts"], "scripts": {"pretest": "eslint src", "prepare": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "lintfix": "eslint --fix src", "report": "nyc report --reporter=html", "test": "nyc ava src/__tests__/*.js", "testone": "ava"}, "dependencies": {"cssesc": "^2.0.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "license": "MIT", "engines": {"node": ">=4"}, "homepage": "https://github.com/postcss/postcss-selector-parser", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/chrise<PERSON><PERSON>"}], "repository": "postcss/postcss-selector-parser", "ava": {"require": "babel-register", "concurrency": 5}, "nyc": {"exclude": ["node_modules", "**/__tests__"]}}