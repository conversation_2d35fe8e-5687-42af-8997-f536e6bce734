{"css": {"properties": {"place-self": {"flex_context": {"__compat": {"description": "Supported in Flex Layout", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/place-self", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "grid_context": {"__compat": {"description": "Supported in Grid Layout", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/place-self", "support": {"chrome": {"version_added": "59"}, "chrome_android": {"version_added": "59"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "45"}, "firefox_android": {"version_added": "45"}, "ie": {"version_added": false}, "opera": {"version_added": "46"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "59"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}}