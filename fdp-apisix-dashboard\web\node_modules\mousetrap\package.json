{"name": "mousetrap", "version": "1.6.5", "description": "Simple library for handling keyboard shortcuts", "main": "mousetrap.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha --reporter=nyan tests/test.mousetrap.js"}, "repository": {"type": "git", "url": "git://github.com/ccampbell/mousetrap.git"}, "keywords": ["keyboard", "shortcuts", "events"], "author": "<PERSON>", "license": "Apache-2.0 WITH LLVM-exception", "gitHead": "c202a0bd4967d5a3064f9cb376db51dec9345336", "readmeFilename": "README.md", "devDependencies": {"chai": "^4.2.0", "grunt": "~1.0.3", "grunt-complexity": "~1.1.0", "jsdom": "^13.1.0", "jsdom-global": "^3.0.2", "mocha": "^5.2.0", "sinon": "^7.2.2"}}