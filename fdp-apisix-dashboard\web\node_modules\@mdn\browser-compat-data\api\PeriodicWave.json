{"api": {"PeriodicWave": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicWave", "support": {"chrome": {"version_added": "14"}, "chrome_android": {"version_added": "18"}, "edge": {"version_added": "≤18"}, "firefox": {"version_added": "25"}, "firefox_android": {"version_added": "26"}, "ie": {"version_added": false}, "opera": {"version_added": "15"}, "opera_android": {"version_added": "14"}, "safari": {"version_added": "6"}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": "1.0"}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "PeriodicWave": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicWave/PeriodicWave", "description": "<code>PeriodicWave()</code> constructor", "support": {"chrome": {"version_added": "55", "notes": "Before Chrome 59, the default values were not supported."}, "chrome_android": {"version_added": "55", "notes": "Before Chrome 59, the default values were not supported."}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "53"}, "firefox_android": {"version_added": "53"}, "ie": {"version_added": false}, "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": "6.0", "notes": "Before Samsung Internet 7.0, the default values were not supported."}, "webview_android": {"version_added": "55", "notes": "Before Chrome 59, the default values were not supported."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}