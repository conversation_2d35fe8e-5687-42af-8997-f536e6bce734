// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PropertySafetyFilledSvg from "@ant-design/icons-svg/es/asn/PropertySafetyFilled";
import AntdIcon from '../components/AntdIcon';

var PropertySafetyFilled = function PropertySafetyFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PropertySafetyFilledSvg
  }));
};

PropertySafetyFilled.displayName = 'PropertySafetyFilled';
export default /*#__PURE__*/React.forwardRef(PropertySafetyFilled);