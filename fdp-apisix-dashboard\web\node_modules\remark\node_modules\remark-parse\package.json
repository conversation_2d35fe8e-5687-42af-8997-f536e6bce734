{"name": "remark-parse", "version": "9.0.0", "description": "remark plugin to parse Markdown", "license": "MIT", "keywords": ["unified", "remark", "remark-plugin", "plugin", "markdown", "mdast", "abstract", "syntax", "tree", "ast", "parse"], "types": "types/index.d.ts", "homepage": "https://remark.js.org", "repository": "https://github.com/remarkjs/remark/tree/main/packages/remark-parse", "bugs": "https://github.com/remarkjs/remark/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <elija<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>"], "files": ["index.js", "types/index.d.ts"], "dependencies": {"mdast-util-from-markdown": "^0.8.0"}, "scripts": {"test": "tape test.js"}, "xo": false}