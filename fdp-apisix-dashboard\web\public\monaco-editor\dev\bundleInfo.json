{"graph": {"===anonymous1===": ["require"], "===anonymous2===": ["vs/editor/editor.main", "vs/css", "vs/nls", "vs/base/common/worker/simpleWorker", "vs/editor/common/services/editorSimpleWorker"], "vs/editor/editor.main": ["require", "exports", "vs/editor/editor.api", "vs/editor/editor.all", "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast"], "vs/css": ["require", "exports", "module"], "vs/nls": ["require", "exports", "module"], "vs/base/common/worker/simpleWorker": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/base/common/strings"], "vs/editor/common/services/editorSimpleWorker": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/platform", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/diff/diffComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/services/editorBaseApi", "vs/base/common/types", "vs/base/common/stopwatch", "vs/editor/common/languages/unicodeTextModelHighlighter"], "vs/editor/editor.api": ["require", "exports", "vs/editor/common/config/editorOptions", "vs/editor/common/services/editorBaseApi", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/base/common/platform", "vs/editor/contrib/format/browser/format"], "vs/editor/editor.all": ["require", "exports", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/browser/widget/diffNavigator", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/editor/contrib/inlineCompletions/browser/ghostTextController", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/hover/browser/hover", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/contrib/viewportSemanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/common/standaloneStrings", "vs/base/browser/ui/codicons/codiconStyles"], "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/widget", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/standaloneStrings", "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp"], "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/base/common/platform", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard"], "vs/editor/standalone/browser/inspectTokens/inspectTokens": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/languages", "vs/editor/common/languages/nullMode", "vs/editor/common/services/language", "vs/editor/standalone/common/standaloneTheme", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/standaloneStrings", "vs/platform/theme/common/theme", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens"], "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/platform/quickinput/browser/helpQuickAccess"], "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/base/common/types", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess": ["require", "exports", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/browser/services/codeEditorService", "vs/base/common/types", "vs/editor/common/standaloneStrings", "vs/base/common/event", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/base/browser/ui/codicons/codiconStyles", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess": ["require", "exports", "vs/platform/registry/common/platform", "vs/platform/quickinput/common/quickAccess", "vs/editor/common/standaloneStrings", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/base/common/types", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/commands/common/commands", "vs/platform/telemetry/common/telemetry", "vs/platform/dialogs/common/dialogs", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/quickinput/common/quickInput"], "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage"], "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/standalone/common/standaloneTheme", "vs/editor/common/standaloneStrings"], "vs/base/common/errors": ["require", "exports"], "vs/base/common/event": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/stopwatch"], "vs/base/common/lifecycle": ["require", "exports", "vs/base/common/functional", "vs/base/common/iterator"], "vs/base/common/platform": ["require", "exports"], "vs/base/common/types": ["require", "exports"], "vs/base/common/strings": ["require", "exports", "vs/base/common/cache", "vs/base/common/lazy"], "vs/base/common/diff/diff": ["require", "exports", "vs/base/common/diff/diffChange", "vs/base/common/hash"], "vs/base/common/uri": ["require", "exports", "vs/base/common/path", "vs/base/common/platform"], "vs/editor/common/core/position": ["require", "exports"], "vs/editor/common/core/range": ["require", "exports", "vs/editor/common/core/position"], "vs/editor/common/diff/diffComputer": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings"], "vs/editor/common/model/mirrorTextModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/model/prefixSumComputer"], "vs/editor/common/core/wordHelper": ["require", "exports"], "vs/editor/common/languages/linkComputer": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/editor/common/languages/supports/inplaceReplaceSupport": ["require", "exports"], "vs/editor/common/services/editorBaseApi": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/keyCodes", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/standalone/standaloneEnums"], "vs/base/common/stopwatch": ["require", "exports", "vs/base/common/platform"], "vs/editor/common/languages/unicodeTextModelHighlighter": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model/textModelSearch", "vs/base/common/strings", "vs/base/common/types"], "vs/editor/common/config/editorOptions": ["require", "exports", "vs/nls!vs/editor/common/config/editorOptions", "vs/base/common/platform", "vs/editor/common/core/wordHelper", "vs/base/common/arrays", "vs/base/common/objects", "vs/editor/common/core/textModelDefaults"], "vs/editor/standalone/browser/standaloneEditor": ["require", "exports", "vs/base/common/strings", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/diffNavigator", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/model", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/nullMode", "vs/editor/common/services/language", "vs/editor/common/services/model", "vs/editor/browser/services/webWorker", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/colorizer", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/commands/common/commands", "vs/platform/markers/common/markers", "vs/css!vs/editor/standalone/browser/standalone-tokens"], "vs/editor/standalone/browser/standaloneLanguages": ["require", "exports", "vs/base/common/color", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/language", "vs/editor/common/standalone/standaloneEnums", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/monarch/monarchCompile", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/common/standaloneTheme", "vs/platform/markers/common/markers"], "vs/editor/contrib/format/browser/format": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/linkedList", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/resolverService", "vs/editor/contrib/format/browser/formattingEdit", "vs/nls!vs/editor/contrib/format/browser/format", "vs/platform/commands/common/commands", "vs/platform/extensions/common/extensions", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/controller/coreCommands": ["require", "exports", "vs/nls!vs/editor/browser/controller/coreCommands", "vs/base/browser/browser", "vs/base/common/types", "vs/base/browser/ui/aria/aria", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/editor/browser/widget/codeEditorWidget": ["require", "exports", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/base/browser/dom", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/editor/browser/config/editorConfiguration", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/view/view", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor/cursor", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/colorRegistry", "vs/editor/common/viewModel/viewModelImpl", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/base/common/types", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/services/markerDecorations", "vs/css!vs/editor/browser/widget/media/editor"], "vs/editor/browser/widget/diffEditorWidget": ["require", "exports", "vs/nls!vs/editor/browser/widget/diffEditorWidget", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/sash/sash", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffReview", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/common/model/textModel", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/platform/contextview/browser/contextView", "vs/editor/browser/widget/inlineDiffMargin", "vs/platform/clipboard/common/clipboardService", "vs/editor/browser/editorExtensions", "vs/base/common/errors", "vs/platform/progress/common/progress", "vs/editor/browser/config/elementSizeObserver", "vs/base/common/codicons", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/platform/theme/common/iconRegistry", "vs/css!vs/editor/browser/widget/media/diffEditor"], "vs/editor/browser/widget/diffNavigator": ["require", "exports", "vs/base/common/assert", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range"], "vs/editor/contrib/anchorSelect/browser/anchorSelect": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/htmlContent", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect"], "vs/editor/contrib/bracketMatching/browser/bracketMatching": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/core/editorColorRegistry", "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/platform/actions/common/actions", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching"], "vs/editor/contrib/caretOperations/browser/caretOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations"], "vs/editor/contrib/caretOperations/browser/transpose": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/caretOperations/browser/transpose"], "vs/editor/contrib/clipboard/browser/clipboard": ["require", "exports", "vs/base/browser/browser", "vs/base/common/platform", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/clipboard/browser/clipboard", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService"], "vs/editor/contrib/codeAction/browser/codeActionContributions": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/contrib/codeAction/browser/codeActionCommands"], "vs/editor/contrib/codelens/browser/codelensController": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/hash", "vs/base/common/lifecycle", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/nls!vs/editor/contrib/codelens/browser/codelensController", "vs/platform/commands/common/commands", "vs/platform/notification/common/notification", "vs/platform/quickinput/common/quickInput", "vs/editor/common/services/languageFeatureDebounce"], "vs/editor/contrib/colorPicker/browser/colorContributions": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/hover/browser/hover"], "vs/editor/contrib/comment/browser/comment": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/nls!vs/editor/contrib/comment/browser/comment", "vs/platform/actions/common/actions"], "vs/editor/contrib/contextmenu/browser/contextmenu": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/cursorUndo/browser/cursorUndo": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo"], "vs/editor/contrib/dnd/browser/dnd": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/css!vs/editor/contrib/dnd/browser/dnd"], "vs/editor/contrib/find/browser/findController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/nls!vs/editor/contrib/find/browser/findController", "vs/platform/actions/common/actions", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService"], "vs/editor/contrib/folding/browser/folding": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/contrib/folding/browser/intializingRangeProvider", "vs/nls!vs/editor/contrib/folding/browser/folding", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/platform/notification/common/notification", "vs/base/common/severity", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch", "vs/css!vs/editor/contrib/folding/browser/folding"], "vs/editor/contrib/fontZoom/browser/fontZoom": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorZoom", "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom"], "vs/editor/contrib/format/browser/formatActions": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formattingEdit", "vs/nls!vs/editor/contrib/format/browser/formatActions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/progress/common/progress"], "vs/editor/contrib/documentSymbols/browser/documentSymbols": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/services/resolverService", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/browser/ghostTextController": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/core/cursorColumns", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/inlineCompletions/browser/consts", "vs/editor/contrib/inlineCompletions/browser/ghostTextModel", "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget", "vs/nls!vs/editor/contrib/inlineCompletions/browser/ghostTextController", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/editor/contrib/gotoSymbol/browser/goToCommands": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/platform", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorBrowser", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/editor/contrib/gotoSymbol/browser/goToSymbol"], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition"], "vs/editor/contrib/gotoError/browser/gotoError": ["require", "exports", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/nls!vs/editor/contrib/gotoError/browser/gotoError", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/iconRegistry", "vs/editor/contrib/gotoError/browser/gotoErrorWidget"], "vs/editor/contrib/hover/browser/hover": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/services/language", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/contentHover", "vs/editor/contrib/hover/browser/marginHover", "vs/nls!vs/editor/contrib/hover/browser/hover", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/indentation/browser/indentation": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/model", "vs/editor/contrib/indentation/browser/indentUtils", "vs/nls!vs/editor/contrib/indentation/browser/indentation", "vs/platform/quickinput/common/quickInput"], "vs/editor/contrib/inlayHints/browser/inlayHintsController": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/types", "vs/base/common/uri", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/platform/commands/common/commands", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/core/editorColorRegistry", "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/platform/theme/common/themeService", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand"], "vs/editor/contrib/lineSelection/browser/lineSelection": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/editorContext<PERSON>eys", "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection"], "vs/editor/contrib/linesOperations/browser/linesOperations": ["require", "exports", "vs/base/common/keyCodes", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations", "vs/platform/actions/common/actions"], "vs/editor/contrib/linkedEditing/browser/linkedEditing": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/links/browser/links": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/editorExtensions", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/links/browser/getLinks", "vs/nls!vs/editor/contrib/links/browser/links", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/links/browser/links"], "vs/editor/contrib/multicursor/browser/multicursor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/contrib/find/browser/findController", "vs/nls!vs/editor/contrib/multicursor/browser/multicursor", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/parameterHints/browser/parameterHints": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget"], "vs/editor/contrib/rename/browser/rename": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/common/services/textResourceConfiguration", "vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/rename/browser/rename", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/registry/common/platform", "vs/editor/contrib/rename/browser/renameInputField"], "vs/editor/contrib/smartSelect/browser/smartSelect": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands"], "vs/editor/contrib/snippet/browser/snippetController2": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/snippet/browser/snippetController2", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/editor/contrib/snippet/browser/snippetSession"], "vs/editor/contrib/suggest/browser/suggestController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/stopwatch", "vs/base/common/types", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/editorExtensions", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/nls!vs/editor/contrib/suggest/browser/suggestController", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/log/common/log", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/platform/telemetry/common/telemetry", "vs/base/common/resources", "vs/base/common/hash"], "vs/editor/contrib/tokenization/browser/tokenization": ["require", "exports", "vs/base/common/stopwatch", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/tokenization/browser/tokenization"], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/editor/browser/config/tabFocus", "vs/editor/browser/editorExtensions", "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode"], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/common/config/editorOptions", "vs/editor/common/model/textModel", "vs/editor/common/languages/unicodeTextModelHighlighter", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/language", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/theme/common/iconRegistry", "vs/platform/workspace/common/workspaceTrust", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter"], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/platform/dialogs/common/dialogs"], "vs/editor/contrib/viewportSemanticTokens/browser/viewportSemanticTokens": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/languages", "vs/editor/common/services/getSemanticTokens", "vs/editor/common/services/model", "vs/editor/common/services/modelService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/platform/configuration/common/configuration", "vs/platform/theme/common/themeService", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch"], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/wordOperations/browser/wordOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/commands/replaceCommand", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/common/languages/languageConfigurationRegistry", "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys"], "vs/editor/contrib/wordPartOperations/browser/wordPartOperations": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/range", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/platform/commands/common/commands"], "vs/editor/common/standaloneStrings": ["require", "exports", "vs/nls!vs/editor/common/standaloneStrings"], "vs/base/browser/ui/codicons/codiconStyles": ["require", "exports", "vs/base/common/codicons", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers"], "vs/base/browser/dom": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/platform"], "vs/base/browser/fastDomNode": ["require", "exports"], "vs/base/browser/formattedTextRenderer": ["require", "exports", "vs/base/browser/dom"], "vs/base/browser/ui/aria/aria": ["require", "exports", "vs/base/browser/dom", "vs/base/common/platform", "vs/css!vs/base/browser/ui/aria/aria"], "vs/base/browser/ui/widget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/touch", "vs/base/common/lifecycle"], "vs/editor/browser/editorExtensions": ["require", "exports", "vs/nls!vs/editor/browser/editorExtensions", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/registry/common/platform", "vs/platform/telemetry/common/telemetry", "vs/base/common/types", "vs/platform/log/common/log"], "vs/editor/common/editorContextKeys": ["require", "exports", "vs/nls!vs/editor/common/editorContextKeys", "vs/platform/contextkey/common/contextkey"], "vs/platform/contextkey/common/contextkey": ["require", "exports", "vs/base/common/platform", "vs/base/common/strings", "vs/platform/instantiation/common/instantiation"], "vs/platform/instantiation/common/instantiation": ["require", "exports"], "vs/platform/keybinding/common/keybinding": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/opener/common/opener": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/colorRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/event", "vs/base/common/types", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/platform/theme/common/themeService": ["require", "exports", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/registry/common/platform", "vs/platform/theme/common/theme"], "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp": [], "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard": [], "vs/base/common/color": ["require", "exports"], "vs/editor/common/languages": ["require", "exports", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/tokenizationRegistry", "vs/base/common/codicons"], "vs/editor/common/languages/nullMode": ["require", "exports", "vs/editor/common/languages"], "vs/editor/common/services/language": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/common/standaloneTheme": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/theme/common/theme": ["require", "exports"], "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens": [], "vs/platform/registry/common/platform": ["require", "exports", "vs/base/common/assert", "vs/base/common/types"], "vs/platform/quickinput/common/quickAccess": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/platform/registry/common/platform"], "vs/platform/quickinput/browser/helpQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/browser/editorBrowser", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess"], "vs/editor/browser/services/codeEditorService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/quickinput/common/quickInput": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/parts/quickinput/common/quickInput"], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/fuzzyScorer", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess"], "vs/editor/contrib/documentSymbols/browser/outlineModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/iterator", "vs/base/common/map", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/languageFeatureDebounce", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/editor/common/services/model", "vs/base/common/lifecycle"], "vs/editor/contrib/symbolIcons/browser/symbolIcons": ["require", "exports", "vs/base/common/codicons", "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/quickAccess/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/iconLabels", "vs/platform/quickinput/browser/commandsQuickAccess"], "vs/platform/commands/common/commands": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/types", "vs/platform/instantiation/common/instantiation"], "vs/platform/telemetry/common/telemetry": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/dialogs/common/dialogs": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/keyCodes", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/list/browser/listService", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget"], "vs/platform/configuration/common/configuration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/notification/common/notification": ["require", "exports", "vs/base/common/severity", "vs/platform/instantiation/common/instantiation"], "vs/platform/storage/common/storage": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/parts/storage/common/storage", "vs/platform/instantiation/common/instantiation"], "vs/base/common/linkedList": ["require", "exports"], "vs/base/common/functional": ["require", "exports"], "vs/base/common/iterator": ["require", "exports"], "vs/base/common/cache": ["require", "exports"], "vs/base/common/lazy": ["require", "exports"], "vs/base/common/diff/diffChange": ["require", "exports"], "vs/base/common/hash": ["require", "exports", "vs/base/common/strings"], "vs/base/common/path": ["require", "exports", "vs/base/common/process"], "vs/editor/common/model/prefixSumComputer": ["require", "exports", "vs/base/common/arrays", "vs/base/common/uint"], "vs/editor/common/core/characterClassifier": ["require", "exports", "vs/base/common/uint"], "vs/base/common/cancellation": ["require", "exports", "vs/base/common/event"], "vs/base/common/keyCodes": ["require", "exports"], "vs/editor/common/core/selection": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/standalone/standaloneEnums": ["require", "exports"], "vs/editor/common/model/textModelSearch": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model"], "vs/nls!vs/editor/common/config/editorOptions": [], "vs/base/common/arrays": ["require", "exports"], "vs/base/common/objects": ["require", "exports", "vs/base/common/types"], "vs/editor/common/core/textModelDefaults": ["require", "exports"], "===anonymous3===": ["vs/editor/common/config/editorOptions.nls", "vs/editor/common/config/editorOptions.nls.keys"], "vs/editor/common/config/editorOptions.nls": [], "vs/editor/common/config/editorOptions.nls.keys": [], "vs/editor/browser/config/fontMeasurements": ["require", "exports", "vs/base/browser/browser", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/charWidthReader", "vs/editor/common/config/editorOptions", "vs/editor/common/config/fontInfo"], "vs/editor/common/config/fontInfo": ["require", "exports", "vs/base/common/platform", "vs/editor/common/config/editorZoom"], "vs/editor/common/editorCommon": ["require", "exports"], "vs/editor/common/model": ["require", "exports", "vs/base/common/objects"], "vs/editor/common/languages/languageConfigurationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/richEditBrackets", "vs/platform/instantiation/common/instantiation", "vs/platform/configuration/common/configuration", "vs/editor/common/services/language", "vs/platform/instantiation/common/extensions"], "vs/editor/common/services/model": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/services/webWorker": ["require", "exports", "vs/editor/browser/services/editorWorkerService", "vs/base/common/types"], "vs/editor/standalone/browser/colorizer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/editor/standalone/common/monarch/monarchLexer"], "vs/editor/standalone/browser/standaloneCodeEditor": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/lifecycle", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/common/editorAction", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/common/standaloneTheme", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/standaloneStrings", "vs/platform/clipboard/common/clipboardService", "vs/platform/progress/common/progress", "vs/editor/common/services/model", "vs/editor/common/services/language", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/standalone/browser/standaloneServices": ["require", "exports", "vs/base/common/strings", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/event", "vs/base/common/keybindings", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/uri", "vs/editor/browser/services/bulkEditService", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/textResourceConfiguration", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationModels", "vs/platform/contextkey/common/contextkey", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/platform/workspace/common/workspace", "vs/platform/layout/browser/layoutService", "vs/editor/common/standaloneStrings", "vs/editor/browser/services/codeEditorService", "vs/platform/log/common/log", "vs/platform/workspace/common/workspaceTrust", "vs/platform/contextview/browser/contextView", "vs/platform/contextview/browser/contextViewService", "vs/editor/common/services/languageService", "vs/platform/contextview/browser/contextMenuService", "vs/platform/theme/common/themeService", "vs/platform/instantiation/common/extensions", "vs/editor/browser/services/openerService", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/browser/services/editorWorkerService", "vs/editor/common/services/language", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/modelService", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/editor/standalone/browser/standaloneThemeService", "vs/editor/standalone/common/standaloneTheme", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/accessibility/common/accessibility", "vs/platform/actions/common/actions", "vs/platform/actions/common/menuService", "vs/platform/clipboard/browser/clipboardService", "vs/platform/clipboard/common/clipboardService", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/instantiationService", "vs/platform/instantiation/common/serviceCollection", "vs/platform/list/browser/listService", "vs/platform/markers/common/markers", "vs/platform/markers/common/markerService", "vs/platform/opener/common/opener", "vs/platform/quickinput/common/quickInput", "vs/platform/storage/common/storage", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/undoRedo/common/undoRedoService", "vs/editor/common/services/languageFeatureDebounce"], "vs/platform/markers/common/markers": ["require", "exports", "vs/base/common/severity", "vs/nls!vs/platform/markers/common/markers", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/standalone/browser/standalone-tokens": [], "vs/editor/common/languages/modesRegistry": ["require", "exports", "vs/nls!vs/editor/common/languages/modesRegistry", "vs/base/common/event", "vs/editor/common/languages/languageConfigurationRegistry", "vs/platform/registry/common/platform", "vs/base/common/mime", "vs/platform/configuration/common/configurationRegistry"], "vs/editor/standalone/common/monarch/monarchCompile": ["require", "exports", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/standalone/common/monarch/monarchLexer": ["require", "exports", "vs/editor/common/languages", "vs/editor/common/languages/nullMode", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon"], "vs/editor/contrib/editorState/browser/editorState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range", "vs/base/common/cancellation", "vs/base/common/lifecycle", "vs/editor/contrib/editorState/browser/keybindingCancellation"], "vs/editor/browser/editorBrowser": ["require", "exports", "vs/editor/common/editor<PERSON><PERSON><PERSON>"], "vs/editor/common/services/editorWorker": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/services/resolverService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/format/browser/formattingEdit": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/format/browser/format": [], "vs/platform/extensions/common/extensions": ["require", "exports"], "===anonymous4===": ["vs/editor/contrib/format/browser/format.nls", "vs/editor/contrib/format/browser/format.nls.keys"], "vs/editor/contrib/format/browser/format.nls": [], "vs/editor/contrib/format/browser/format.nls.keys": [], "vs/nls!vs/editor/browser/controller/coreCommands": [], "vs/base/browser/browser": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/cursor/cursorColumnSelection": ["require", "exports", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursor/cursorCommon": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/editor/common/languages/supports", "vs/editor/common/core/cursorColumns"], "vs/editor/common/cursor/cursorDeleteOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/cursorColumns", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/core/range", "vs/editor/common/core/position"], "vs/editor/common/cursor/cursorMoveCommands": ["require", "exports", "vs/base/common/types", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/common/cursor/cursorTypeOperations": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/shiftCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/position", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/supports"], "vs/platform/keybinding/common/keybindingsRegistry": ["require", "exports", "vs/base/common/keybindings", "vs/base/common/platform", "vs/platform/commands/common/commands", "vs/platform/registry/common/platform"], "===anonymous5===": ["vs/editor/browser/controller/coreCommands.nls", "vs/editor/browser/controller/coreCommands.nls.keys"], "vs/editor/browser/controller/coreCommands.nls": [], "vs/editor/browser/controller/coreCommands.nls.keys": [], "vs/nls!vs/editor/browser/widget/codeEditorWidget": [], "vs/base/common/network": ["require", "exports", "vs/base/common/platform", "vs/base/common/uri"], "vs/editor/browser/config/editorConfiguration": ["require", "exports", "vs/base/browser/browser", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/platform", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/common/config/editorOptions", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/platform/accessibility/common/accessibility"], "vs/editor/browser/view/view": ["require", "exports", "vs/base/browser/dom", "vs/editor/common/core/selection", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/view/viewController", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/lines/viewLines", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/browser/view/renderingContext", "vs/editor/common/viewModel/viewContext", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel/viewEventHandler", "vs/platform/theme/common/themeService", "vs/editor/browser/controller/mouseTarget"], "vs/editor/browser/view/viewUserInputEvents": ["require", "exports"], "vs/editor/common/cursor/cursor": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel/viewEvents", "vs/base/common/lifecycle", "vs/editor/common/viewModel/viewModelEventDispatcher"], "vs/editor/common/core/cursorColumns": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/editorAction": ["require", "exports"], "vs/editor/common/core/editorColorRegistry": ["require", "exports", "vs/nls!vs/editor/common/core/editorColorRegistry", "vs/base/common/color", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/viewModel/viewModelImpl": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/config/editorOptions", "vs/editor/common/cursor/cursor", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelEvents", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/viewModel/viewEvents", "vs/editor/common/viewLayout/viewLayout", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/viewModelDecorations", "vs/editor/common/viewModel/viewModelEventDispatcher", "vs/editor/common/viewModel/viewModelLines"], "vs/platform/instantiation/common/serviceCollection": ["require", "exports"], "vs/platform/accessibility/common/accessibility": ["require", "exports", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/editor/common/viewModel/monospaceLineBreaksComputer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/characterClassifier", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel/modelLineProjectionData"], "vs/editor/browser/view/domLineBreaksComputer": ["require", "exports", "vs/editor/common/core/stringBuilder", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel/modelLineProjectionData"], "vs/editor/common/cursor/cursorWordOperations": ["require", "exports", "vs/base/common/strings", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/browser/config/domFontInfo": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/editor/common/config/editorOptions"], "vs/editor/browser/services/markerDecorations": ["require", "exports", "vs/editor/common/services/markerDecorations", "vs/editor/browser/editorExtensions"], "vs/css!vs/editor/browser/widget/media/editor": [], "===anonymous6===": ["vs/editor/browser/widget/codeEditorWidget.nls", "vs/editor/browser/widget/codeEditorWidget.nls.keys"], "vs/editor/browser/widget/codeEditorWidget.nls": [], "vs/editor/browser/widget/codeEditorWidget.nls.keys": [], "vs/nls!vs/editor/browser/widget/diffEditorWidget": [], "vs/base/browser/ui/sash/sash": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/css!vs/base/browser/ui/sash/sash"], "vs/base/common/async": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/editor/browser/stableEditorScroll": ["require", "exports"], "vs/editor/browser/widget/diffReview": ["require", "exports", "vs/nls!vs/editor/browser/widget/diffReview", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/config/editorOptions", "vs/editor/common/tokens/lineTokens", "vs/editor/common/core/position", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewModel/viewModel", "vs/platform/contextkey/common/contextkey", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/codicons", "vs/platform/theme/common/iconRegistry", "vs/editor/common/services/language", "vs/css!vs/editor/browser/widget/media/diffReview"], "vs/editor/common/core/stringBuilder": ["require", "exports", "vs/base/common/strings", "vs/base/common/platform", "vs/base/common/buffer"], "vs/editor/common/model/textModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/model/editStack", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/textModelEvents", "vs/editor/common/model/textModelSearch", "vs/editor/common/model/textModelTokens", "vs/editor/common/core/eolCounter", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseTokensStore", "vs/editor/common/core/wordHelper", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/language", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/core/textModelDefaults"], "vs/editor/common/viewModel/overviewZoneManager": ["require", "exports"], "vs/editor/common/viewLayout/lineDecorations": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewLayout/viewLineRenderer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/viewLayout/lineDecorations"], "vs/editor/common/viewModel/viewModel": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/platform/contextview/browser/contextView": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/widget/inlineDiffMargin": ["require", "exports", "vs/nls!vs/editor/browser/widget/inlineDiffMargin", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/base/common/codicons"], "vs/platform/clipboard/common/clipboardService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/progress/common/progress": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/browser/config/elementSizeObserver": ["require", "exports", "vs/base/common/lifecycle", "vs/base/common/event"], "vs/base/common/codicons": ["require", "exports"], "vs/base/browser/ui/mouseCursor/mouseCursor": ["require", "exports", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/platform/theme/common/iconRegistry": ["require", "exports", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/event", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/browser/widget/media/diffEditor": [], "===anonymous7===": ["vs/editor/browser/widget/diffEditorWidget.nls", "vs/editor/browser/widget/diffEditorWidget.nls.keys"], "vs/editor/browser/widget/diffEditorWidget.nls": [], "vs/editor/browser/widget/diffEditorWidget.nls.keys": [], "vs/base/common/assert": ["require", "exports"], "vs/base/common/htmlContent": ["require", "exports", "vs/base/common/errors", "vs/base/common/iconLabels"], "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect": [], "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect": [], "===anonymous8===": ["vs/editor/contrib/anchorSelect/browser/anchorSelect.nls", "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls.keys"], "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls": [], "vs/editor/contrib/anchorSelect/browser/anchorSelect.nls.keys": [], "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching": [], "vs/platform/actions/common/actions": ["require", "exports", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching": [], "===anonymous9===": ["vs/editor/contrib/bracketMatching/browser/bracketMatching.nls", "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls.keys"], "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls": [], "vs/editor/contrib/bracketMatching/browser/bracketMatching.nls.keys": [], "vs/editor/contrib/caretOperations/browser/moveCaretCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations": [], "===anonymous10===": ["vs/editor/contrib/caretOperations/browser/caretOperations.nls", "vs/editor/contrib/caretOperations/browser/caretOperations.nls.keys"], "vs/editor/contrib/caretOperations/browser/caretOperations.nls": [], "vs/editor/contrib/caretOperations/browser/caretOperations.nls.keys": [], "vs/editor/common/commands/replaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "vs/editor/common/cursor/cursorMoveOperations": ["require", "exports", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/common/strings", "vs/editor/common/cursor/cursorAtomicMoveOperations"], "vs/nls!vs/editor/contrib/caretOperations/browser/transpose": [], "===anonymous11===": ["vs/editor/contrib/caretOperations/browser/transpose.nls", "vs/editor/contrib/caretOperations/browser/transpose.nls.keys"], "vs/editor/contrib/caretOperations/browser/transpose.nls": [], "vs/editor/contrib/caretOperations/browser/transpose.nls.keys": [], "vs/editor/browser/controller/textAreaInput": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/mime", "vs/base/common/strings", "vs/editor/browser/controller/textAreaState", "vs/editor/common/core/selection"], "vs/nls!vs/editor/contrib/clipboard/browser/clipboard": [], "===anonymous12===": ["vs/editor/contrib/clipboard/browser/clipboard.nls", "vs/editor/contrib/clipboard/browser/clipboard.nls.keys"], "vs/editor/contrib/clipboard/browser/clipboard.nls": [], "vs/editor/contrib/clipboard/browser/clipboard.nls.keys": [], "vs/editor/contrib/codeAction/browser/codeActionCommands": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/bulkEditService", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionUi", "vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/notification/common/notification", "vs/platform/progress/common/progress", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/codeAction/browser/codeActionModel", "vs/editor/contrib/codeAction/browser/types"], "vs/editor/contrib/codelens/browser/codelens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/languages", "vs/editor/common/services/model", "vs/platform/commands/common/commands"], "vs/editor/contrib/codelens/browser/codeLensCache": ["require", "exports", "vs/base/common/async", "vs/base/common/functional", "vs/base/common/map", "vs/editor/common/core/range", "vs/editor/contrib/codelens/browser/codelens", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/codelens/browser/codelensWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget"], "vs/nls!vs/editor/contrib/codelens/browser/codelensController": [], "vs/editor/common/services/languageFeatureDebounce": ["require", "exports", "vs/base/common/hash", "vs/base/common/map", "vs/base/common/numbers", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/log/common/log"], "===anonymous13===": ["vs/editor/contrib/codelens/browser/codelensController.nls", "vs/editor/contrib/codelens/browser/codelensController.nls.keys"], "vs/editor/contrib/codelens/browser/codelensController.nls": [], "vs/editor/contrib/codelens/browser/codelensController.nls.keys": [], "vs/editor/contrib/colorPicker/browser/colorDetector": ["require", "exports", "vs/base/common/async", "vs/base/common/color", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/editorDom", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/contrib/colorPicker/browser/color", "vs/platform/configuration/common/configuration"], "vs/editor/contrib/comment/browser/blockCommentCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/comment/browser/lineCommentCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/comment/browser/blockCommentCommand"], "vs/nls!vs/editor/contrib/comment/browser/comment": [], "===anonymous14===": ["vs/editor/contrib/comment/browser/comment.nls", "vs/editor/contrib/comment/browser/comment.nls.keys"], "vs/editor/contrib/comment/browser/comment.nls": [], "vs/editor/contrib/comment/browser/comment.nls.keys": [], "vs/base/browser/ui/actionbar/actionViewItems": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/common/actions": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/base/common/actions"], "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu": [], "===anonymous15===": ["vs/editor/contrib/contextmenu/browser/contextmenu.nls", "vs/editor/contrib/contextmenu/browser/contextmenu.nls.keys"], "vs/editor/contrib/contextmenu/browser/contextmenu.nls": [], "vs/editor/contrib/contextmenu/browser/contextmenu.nls.keys": [], "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo": [], "===anonymous16===": ["vs/editor/contrib/cursorUndo/browser/cursorUndo.nls", "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls.keys"], "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls": [], "vs/editor/contrib/cursorUndo/browser/cursorUndo.nls.keys": [], "vs/editor/contrib/dnd/browser/dragAndDropCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/css!vs/editor/contrib/dnd/browser/dnd": [], "vs/editor/contrib/find/browser/findModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/common/commands/replaceCommand", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModelSearch", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/find/browser/findOptionsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/editor/contrib/find/browser/findModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/browser/findState": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel"], "vs/editor/contrib/find/browser/findWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/find/browser/findModel", "vs/nls!vs/editor/contrib/find/browser/findWidget", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/find/browser/findWidget"], "vs/nls!vs/editor/contrib/find/browser/findController": [], "===anonymous17===": ["vs/editor/contrib/find/browser/findController.nls", "vs/editor/contrib/find/browser/findController.nls.keys"], "vs/editor/contrib/find/browser/findController.nls": [], "vs/editor/contrib/find/browser/findController.nls.keys": [], "vs/editor/contrib/folding/browser/foldingModel": ["require", "exports", "vs/base/common/event", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/editor/contrib/folding/browser/hiddenRangeModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/editor/contrib/folding/browser/indentRangeProvider": ["require", "exports", "vs/editor/common/model/utils", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/editor/contrib/folding/browser/intializingRangeProvider": ["require", "exports", "vs/editor/contrib/folding/browser/syntaxRangeProvider"], "vs/nls!vs/editor/contrib/folding/browser/folding": [], "vs/editor/contrib/folding/browser/foldingDecorations": ["require", "exports", "vs/base/common/codicons", "vs/editor/common/model/textModel", "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/folding/browser/syntaxRangeProvider": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/contrib/folding/browser/foldingRanges"], "vs/base/common/severity": ["require", "exports", "vs/base/common/strings"], "vs/css!vs/editor/contrib/folding/browser/folding": [], "===anonymous18===": ["vs/editor/contrib/folding/browser/folding.nls", "vs/editor/contrib/folding/browser/folding.nls.keys"], "vs/editor/contrib/folding/browser/folding.nls": [], "vs/editor/contrib/folding/browser/folding.nls.keys": [], "vs/editor/common/config/editorZoom": ["require", "exports", "vs/base/common/event"], "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom": [], "===anonymous19===": ["vs/editor/contrib/fontZoom/browser/fontZoom.nls", "vs/editor/contrib/fontZoom/browser/fontZoom.nls.keys"], "vs/editor/contrib/fontZoom/browser/fontZoom.nls": [], "vs/editor/contrib/fontZoom/browser/fontZoom.nls.keys": [], "vs/nls!vs/editor/contrib/format/browser/formatActions": [], "===anonymous20===": ["vs/editor/contrib/format/browser/formatActions.nls", "vs/editor/contrib/format/browser/formatActions.nls.keys"], "vs/editor/contrib/format/browser/formatActions.nls": [], "vs/editor/contrib/format/browser/formatActions.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/consts": ["require", "exports"], "vs/editor/contrib/inlineCompletions/browser/ghostTextModel": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetPreviewModel", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/platform/commands/common/commands"], "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/tokens/lineTokens", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/stringBuilder", "vs/editor/common/services/language", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText"], "vs/nls!vs/editor/contrib/inlineCompletions/browser/ghostTextController": [], "===anonymous21===": ["vs/editor/contrib/inlineCompletions/browser/ghostTextController.nls", "vs/editor/contrib/inlineCompletions/browser/ghostTextController.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/ghostTextController.nls": [], "vs/editor/contrib/inlineCompletions/browser/ghostTextController.nls.keys": [], "vs/editor/browser/widget/embeddedCodeEditorWidget": ["require", "exports", "vs/base/common/objects", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/codeEditorWidget", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification", "vs/platform/theme/common/themeService", "vs/platform/accessibility/common/accessibility", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/contrib/gotoSymbol/browser/referencesModel": ["require", "exports", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/notification/common/notification"], "vs/editor/contrib/message/browser/messageController": ["require", "exports", "vs/base/browser/ui/aria/aria", "vs/base/common/async", "vs/base/common/lifecycle", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/nls!vs/editor/contrib/message/browser/messageController", "vs/platform/contextkey/common/contextkey", "vs/css!vs/editor/contrib/message/browser/messageController"], "vs/editor/contrib/peekView/browser/peekView": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/base/common/objects", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/codeEditorService", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/nls!vs/editor/contrib/peekView/browser/peekView", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/theme/common/colorRegistry", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands": [], "vs/editor/contrib/gotoSymbol/browser/goToSymbol": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/languages", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "===anonymous22===": ["vs/editor/contrib/gotoSymbol/browser/goToCommands.nls", "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls": [], "vs/editor/contrib/gotoSymbol/browser/goToCommands.nls.keys": [], "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [], "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [], "===anonymous23===": ["vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls": [], "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition.nls.keys": [], "vs/editor/contrib/gotoError/browser/markerNavigationService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/strings", "vs/base/common/uri", "vs/editor/common/core/range", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/markers/common/markers", "vs/platform/configuration/common/configuration"], "vs/nls!vs/editor/contrib/gotoError/browser/gotoError": [], "vs/editor/contrib/gotoError/browser/gotoErrorWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/labels", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/label/common/label", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/severityIcon/common/severityIcon", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget"], "===anonymous24===": ["vs/editor/contrib/gotoError/browser/gotoError.nls", "vs/editor/contrib/gotoError/browser/gotoError.nls.keys"], "vs/editor/contrib/gotoError/browser/gotoError.nls": [], "vs/editor/contrib/gotoError/browser/gotoError.nls.keys": [], "vs/editor/contrib/hover/browser/contentHover": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/hover/hoverWidget", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages", "vs/editor/contrib/hover/browser/colorHoverParticipant", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/base/common/async", "vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/editor/common/editorContext<PERSON>eys", "vs/base/common/event"], "vs/editor/contrib/hover/browser/marginHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/hover/browser/hoverOperation", "vs/platform/opener/common/opener", "vs/base/browser/ui/hover/hoverWidget"], "vs/nls!vs/editor/contrib/hover/browser/hover": [], "===anonymous25===": ["vs/editor/contrib/hover/browser/hover.nls", "vs/editor/contrib/hover/browser/hover.nls.keys"], "vs/editor/contrib/hover/browser/hover.nls": [], "vs/editor/contrib/hover/browser/hover.nls.keys": [], "vs/editor/common/commands/shiftCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfigurationRegistry"], "vs/editor/common/core/editOperation": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/indentation/browser/indentUtils": ["require", "exports"], "vs/nls!vs/editor/contrib/indentation/browser/indentation": [], "===anonymous26===": ["vs/editor/contrib/indentation/browser/indentation.nls", "vs/editor/contrib/indentation/browser/indentation.nls.keys"], "vs/editor/contrib/indentation/browser/indentation.nls": [], "vs/editor/contrib/indentation/browser/indentation.nls.keys": [], "vs/base/common/map": ["require", "exports", "vs/base/common/strings"], "vs/editor/browser/editorDom": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/theme/common/colorRegistry"], "vs/editor/contrib/inlayHints/browser/inlayHints": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages"], "vs/editor/contrib/inlayHints/browser/inlayHintsLocations": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/cancellation", "vs/editor/browser/editorExtensions", "vs/editor/common/core/range", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/peekView/browser/peekView", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/notification/common/notification"], "vs/platform/instantiation/common/extensions": ["require", "exports", "vs/platform/instantiation/common/descriptors"], "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand": ["require", "exports", "vs/editor/common/core/selection"], "===anonymous27===": ["vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls.keys"], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls": [], "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace.nls.keys": [], "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection": [], "===anonymous28===": ["vs/editor/contrib/lineSelection/browser/lineSelection.nls", "vs/editor/contrib/lineSelection/browser/lineSelection.nls.keys"], "vs/editor/contrib/lineSelection/browser/lineSelection.nls": [], "vs/editor/contrib/lineSelection/browser/lineSelection.nls.keys": [], "vs/editor/common/commands/trimTrailingWhitespaceCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/editor/contrib/linesOperations/browser/copyLinesCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/contrib/linesOperations/browser/moveLinesCommand": ["require", "exports", "vs/base/common/strings", "vs/editor/common/commands/shiftCommand", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/indentation/browser/indentUtils"], "vs/editor/contrib/linesOperations/browser/sortLinesCommand": ["require", "exports", "vs/editor/common/core/editOperation", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations": [], "===anonymous29===": ["vs/editor/contrib/linesOperations/browser/linesOperations.nls", "vs/editor/contrib/linesOperations/browser/linesOperations.nls.keys"], "vs/editor/contrib/linesOperations/browser/linesOperations.nls": [], "vs/editor/contrib/linesOperations/browser/linesOperations.nls.keys": [], "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing": [], "===anonymous30===": ["vs/editor/contrib/linkedEditing/browser/linkedEditing.nls", "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls.keys"], "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls": [], "vs/editor/contrib/linkedEditing/browser/linkedEditing.nls.keys": [], "vs/base/common/resources": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings", "vs/base/common/uri"], "vs/editor/contrib/links/browser/getLinks": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/model", "vs/platform/commands/common/commands"], "vs/nls!vs/editor/contrib/links/browser/links": [], "vs/css!vs/editor/contrib/links/browser/links": [], "===anonymous31===": ["vs/editor/contrib/links/browser/links.nls", "vs/editor/contrib/links/browser/links.nls.keys"], "vs/editor/contrib/links/browser/links.nls": [], "vs/editor/contrib/links/browser/links.nls.keys": [], "vs/nls!vs/editor/contrib/multicursor/browser/multicursor": [], "===anonymous32===": ["vs/editor/contrib/multicursor/browser/multicursor.nls", "vs/editor/contrib/multicursor/browser/multicursor.nls.keys"], "vs/editor/contrib/multicursor/browser/multicursor.nls": [], "vs/editor/contrib/multicursor/browser/multicursor.nls.keys": [], "vs/editor/contrib/parameterHints/browser/provideSignatureHelp": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/services/resolverService", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints": [], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/base/common/types", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/common/services/language", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/theme", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints"], "===anonymous33===": ["vs/editor/contrib/parameterHints/browser/parameterHints.nls", "vs/editor/contrib/parameterHints/browser/parameterHints.nls.keys"], "vs/editor/contrib/parameterHints/browser/parameterHints.nls": [], "vs/editor/contrib/parameterHints/browser/parameterHints.nls.keys": [], "vs/editor/browser/services/bulkEditService": ["require", "exports", "vs/platform/instantiation/common/instantiation", "vs/base/common/uri", "vs/base/common/types"], "vs/editor/common/services/textResourceConfiguration": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/rename/browser/rename": [], "vs/platform/configuration/common/configurationRegistry": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/types", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/registry/common/platform"], "vs/platform/log/common/log": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/rename/browser/renameInputField": ["require", "exports", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/nls!vs/editor/contrib/rename/browser/renameInputField", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/rename/browser/renameInputField"], "===anonymous34===": ["vs/editor/contrib/rename/browser/rename.nls", "vs/editor/contrib/rename/browser/rename.nls.keys"], "vs/editor/contrib/rename/browser/rename.nls": [], "vs/editor/contrib/rename/browser/rename.nls.keys": [], "vs/editor/contrib/smartSelect/browser/bracketSelections": ["require", "exports", "vs/base/common/linkedList", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/contrib/smartSelect/browser/wordSelections": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect": [], "===anonymous35===": ["vs/editor/contrib/smartSelect/browser/smartSelect.nls", "vs/editor/contrib/smartSelect/browser/smartSelect.nls.keys"], "vs/editor/contrib/smartSelect/browser/smartSelect.nls": [], "vs/editor/contrib/smartSelect/browser/smartSelect.nls.keys": [], "vs/editor/contrib/suggest/browser/suggest": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/base/common/types", "vs/base/common/uri", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/resolverService", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls!vs/editor/contrib/suggest/browser/suggest", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/snippet/browser/snippetController2": [], "vs/editor/contrib/snippet/browser/snippetSession": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/model/textModel", "vs/platform/label/common/label", "vs/platform/workspace/common/workspace", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/css!vs/editor/contrib/snippet/browser/snippetSession"], "===anonymous36===": ["vs/editor/contrib/snippet/browser/snippetController2.nls", "vs/editor/contrib/snippet/browser/snippetController2.nls.keys"], "vs/editor/contrib/snippet/browser/snippetController2.nls": [], "vs/editor/contrib/snippet/browser/snippetController2.nls.keys": [], "vs/base/common/keybindings": ["require", "exports", "vs/base/common/errors"], "vs/editor/contrib/snippet/browser/snippetParser": ["require", "exports"], "vs/editor/contrib/suggest/browser/suggestMemory": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/map", "vs/editor/common/languages", "vs/platform/configuration/common/configuration", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage"], "vs/editor/contrib/suggest/browser/wordContextKey": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/nls!vs/editor/contrib/suggest/browser/suggestController": [], "vs/editor/contrib/suggest/browser/suggestAlternatives": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/editor/contrib/suggest/browser/suggestCommitCharacters": ["require", "exports", "vs/base/common/arrays", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier"], "vs/editor/contrib/suggest/browser/suggestModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/suggest/browser/wordDistance", "vs/platform/clipboard/common/clipboardService", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey", "vs/platform/log/common/log", "vs/platform/telemetry/common/telemetry", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/suggest"], "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/strings", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/storage/common/storage", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/browser/resizable", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/editor/contrib/symbolIcons/browser/symbolIcons"], "===anonymous37===": ["vs/editor/contrib/suggest/browser/suggestController.nls", "vs/editor/contrib/suggest/browser/suggestController.nls.keys"], "vs/editor/contrib/suggest/browser/suggestController.nls": [], "vs/editor/contrib/suggest/browser/suggestController.nls.keys": [], "vs/nls!vs/editor/contrib/tokenization/browser/tokenization": [], "===anonymous38===": ["vs/editor/contrib/tokenization/browser/tokenization.nls", "vs/editor/contrib/tokenization/browser/tokenization.nls.keys"], "vs/editor/contrib/tokenization/browser/tokenization.nls": [], "vs/editor/contrib/tokenization/browser/tokenization.nls.keys": [], "vs/editor/browser/config/tabFocus": ["require", "exports", "vs/base/common/event"], "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": [], "===anonymous39===": ["vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls.keys"], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls": [], "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.nls.keys": [], "vs/editor/common/viewModel/viewModelDecorations": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/viewModel/viewModel", "vs/editor/common/config/editorOptions"], "vs/editor/contrib/hover/browser/markdownHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/language", "vs/editor/contrib/hover/browser/getHover", "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener"], "vs/editor/contrib/unicodeHighlighter/browser/bannerController": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/actions", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/platform/instantiation/common/instantiation", "vs/platform/opener/browser/link", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController"], "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [], "vs/platform/workspace/common/workspaceTrust": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [], "===anonymous40===": ["vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls.keys"], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls": [], "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter.nls.keys": [], "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": [], "===anonymous41===": ["vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls.keys"], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls": [], "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators.nls.keys": [], "vs/editor/common/services/getSemanticTokens": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/languages", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/base/common/types", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/core/range"], "vs/editor/common/services/modelService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/errors", "vs/editor/common/model/textModel", "vs/editor/common/core/textModelDefaults", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/language", "vs/editor/common/services/model", "vs/editor/common/services/textResourceConfiguration", "vs/platform/configuration/common/configuration", "vs/base/common/async", "vs/base/common/cancellation", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/platform/undoRedo/common/undoRedo", "vs/base/common/hash", "vs/editor/common/model/editStack", "vs/base/common/network", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/common/services/getSemanticTokens", "vs/base/common/objects", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/languageFeatureDebounce", "vs/base/common/stopwatch"], "vs/editor/common/services/semanticTokensProviderStyling": ["require", "exports", "vs/editor/common/languages", "vs/platform/theme/common/themeService", "vs/platform/log/common/log", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/services/language"], "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter": [], "===anonymous42===": ["vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls.keys"], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls": [], "vs/editor/contrib/wordHighlighter/browser/wordHighlighter.nls.keys": [], "vs/editor/common/core/wordCharacterClassifier": ["require", "exports", "vs/editor/common/core/characterClassifier"], "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations": [], "vs/platform/contextkey/common/contextkeys": ["require", "exports", "vs/base/common/platform", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/platform/contextkey/common/contextkey"], "===anonymous43===": ["vs/editor/contrib/wordOperations/browser/wordOperations.nls", "vs/editor/contrib/wordOperations/browser/wordOperations.nls.keys"], "vs/editor/contrib/wordOperations/browser/wordOperations.nls": [], "vs/editor/contrib/wordOperations/browser/wordOperations.nls.keys": [], "vs/nls!vs/editor/common/standaloneStrings": [], "===anonymous44===": ["vs/editor/common/standaloneStrings.nls", "vs/editor/common/standaloneStrings.nls.keys"], "vs/editor/common/standaloneStrings.nls": [], "vs/editor/common/standaloneStrings.nls.keys": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon": [], "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers": [], "vs/base/browser/canIUse": ["require", "exports", "vs/base/browser/browser", "vs/base/common/platform"], "vs/base/browser/keyboardEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/platform"], "vs/base/browser/mouseEvent": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/iframe", "vs/base/common/platform"], "vs/css!vs/base/browser/ui/aria/aria": [], "vs/base/browser/touch": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/decorators", "vs/base/common/lifecycle"], "vs/nls!vs/editor/browser/editorExtensions": [], "===anonymous45===": ["vs/editor/browser/editorExtensions.nls", "vs/editor/browser/editorExtensions.nls.keys"], "vs/editor/browser/editorExtensions.nls": [], "vs/editor/browser/editorExtensions.nls.keys": [], "vs/nls!vs/editor/common/editorContextKeys": [], "===anonymous46===": ["vs/editor/common/editorContextKeys.nls", "vs/editor/common/editorContextKeys.nls.keys"], "vs/editor/common/editorContextKeys.nls": [], "vs/editor/common/editorContextKeys.nls.keys": [], "vs/nls!vs/platform/theme/common/colorRegistry": [], "vs/platform/jsonschemas/common/jsonContributionRegistry": ["require", "exports", "vs/base/common/event", "vs/platform/registry/common/platform"], "===anonymous47===": ["vs/platform/theme/common/colorRegistry.nls", "vs/platform/theme/common/colorRegistry.nls.keys"], "vs/platform/theme/common/colorRegistry.nls": [], "vs/platform/theme/common/colorRegistry.nls.keys": [], "vs/editor/common/languageFeatureRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/editor/common/languageSelector"], "vs/editor/common/tokenizationRegistry": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/nls!vs/platform/quickinput/browser/helpQuickAccess": [], "===anonymous48===": ["vs/platform/quickinput/browser/helpQuickAccess.nls", "vs/platform/quickinput/browser/helpQuickAccess.nls.keys"], "vs/platform/quickinput/browser/helpQuickAccess.nls": [], "vs/platform/quickinput/browser/helpQuickAccess.nls.keys": [], "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess": ["require", "exports", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/base/common/types", "vs/editor/browser/editorBrowser", "vs/editor/common/model", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": [], "===anonymous49===": ["vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls": [], "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess.nls.keys": [], "vs/base/parts/quickinput/common/quickInput": ["require", "exports"], "vs/base/common/fuzzyScorer": ["require", "exports", "vs/base/common/filters", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": [], "===anonymous50===": ["vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls.keys"], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls": [], "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess.nls.keys": [], "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons": [], "===anonymous51===": ["vs/editor/contrib/symbolIcons/browser/symbolIcons.nls", "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls.keys"], "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls": [], "vs/editor/contrib/symbolIcons/browser/symbolIcons.nls.keys": [], "vs/base/common/iconLabels": ["require", "exports", "vs/base/common/codicons", "vs/base/common/filters", "vs/base/common/strings"], "vs/platform/quickinput/browser/commandsQuickAccess": ["require", "exports", "vs/base/common/errorMessage", "vs/base/common/errors", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/map", "vs/base/common/severity", "vs/base/common/types", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/storage/common/storage", "vs/platform/telemetry/common/telemetry"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController": [], "vs/platform/list/browser/listService": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listPaging", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/table/tableWidget", "vs/base/browser/ui/tree/asyncDataTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/platform/list/browser/listService", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/contextkey/common/contextkey", "vs/platform/contextkey/common/contextkeys", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/registry/common/platform", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/splitview/splitview", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/resources", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/list/browser/listService", "vs/platform/theme/common/themeService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget"], "===anonymous52===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesController.nls.keys": [], "vs/base/parts/storage/common/storage": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types"], "vs/base/common/process": ["require", "exports", "vs/base/common/platform"], "vs/base/common/uint": ["require", "exports"], "vs/editor/browser/config/charWidthReader": ["require", "exports", "vs/editor/browser/config/domFontInfo"], "vs/editor/common/languages/languageConfiguration": ["require", "exports"], "vs/editor/common/languages/supports": ["require", "exports"], "vs/editor/common/languages/supports/characterPair": ["require", "exports", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/electricCharacter": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets"], "vs/editor/common/languages/supports/indentRules": ["require", "exports"], "vs/editor/common/languages/supports/onEnter": ["require", "exports", "vs/base/common/errors", "vs/base/common/strings", "vs/editor/common/languages/languageConfiguration"], "vs/editor/common/languages/supports/richEditBrackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/stringBuilder", "vs/editor/common/core/range"], "vs/editor/browser/services/editorWorkerService": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/services/model", "vs/editor/common/services/textResourceConfiguration", "vs/base/common/strings", "vs/base/common/arrays", "vs/platform/log/common/log", "vs/base/common/stopwatch", "vs/base/common/errors"], "vs/editor/common/tokens/lineTokens": ["require", "exports", "vs/editor/common/languages"], "vs/editor/standalone/browser/standaloneCodeEditorService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/network", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/services/codeEditorService", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/extensions", "vs/platform/theme/common/themeService"], "vs/editor/common/config/editorConfigurationSchema": ["require", "exports", "vs/editor/common/config/editorOptions", "vs/editor/common/core/textModelDefaults", "vs/nls!vs/editor/common/config/editorConfigurationSchema", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/configuration/common/configurationModels": ["require", "exports", "vs/base/common/arrays", "vs/base/common/map", "vs/base/common/objects", "vs/base/common/types", "vs/base/common/uri", "vs/platform/configuration/common/configuration", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/keybinding/common/abstractKeybindingService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService"], "vs/platform/keybinding/common/keybindingResolver": ["require", "exports", "vs/platform/contextkey/common/contextkey"], "vs/platform/keybinding/common/resolvedKeybindingItem": ["require", "exports"], "vs/platform/keybinding/common/usLayoutResolvedKeybinding": ["require", "exports", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/resolvedKeybindingItem"], "vs/platform/label/common/label": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/workspace/common/workspace": ["require", "exports", "vs/base/common/map", "vs/platform/instantiation/common/instantiation"], "vs/platform/layout/browser/layoutService": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/platform/contextview/browser/contextViewService": ["require", "exports", "vs/base/browser/ui/contextview/contextview", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService"], "vs/editor/common/services/languageService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/services/languagesRegistry", "vs/base/common/arrays", "vs/editor/common/languages", "vs/editor/common/languages/modesRegistry"], "vs/platform/contextview/browser/contextMenuService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/telemetry/common/telemetry", "vs/platform/theme/common/themeService", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/contextview/browser/contextView"], "vs/editor/browser/services/openerService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/base/common/map", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/resources", "vs/base/common/uri", "vs/editor/browser/services/codeEditorService", "vs/platform/commands/common/commands", "vs/platform/editor/common/editor", "vs/platform/opener/common/opener"], "vs/editor/common/services/markerDecorationsService": ["require", "exports", "vs/platform/markers/common/markers", "vs/base/common/lifecycle", "vs/editor/common/model", "vs/platform/theme/common/themeService", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/services/model", "vs/editor/common/core/range", "vs/base/common/network", "vs/base/common/event", "vs/platform/theme/common/colorRegistry", "vs/base/common/map"], "vs/editor/common/services/markerDecorations": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/editor/standalone/browser/quickInput/standaloneQuickInputService": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/theme/common/themeService", "vs/base/common/cancellation", "vs/platform/instantiation/common/instantiation", "vs/platform/contextkey/common/contextkey", "vs/platform/accessibility/common/accessibility", "vs/editor/standalone/browser/standaloneLayoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/quickinput/browser/quickInput", "vs/base/common/functional", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput"], "vs/editor/standalone/browser/standaloneThemeService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/event", "vs/editor/common/languages", "vs/editor/common/languages/supports/tokenization", "vs/editor/standalone/common/themes", "vs/platform/registry/common/platform", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/base/common/lifecycle", "vs/platform/theme/common/theme", "vs/platform/theme/browser/iconsStyleSheet"], "vs/platform/accessibility/browser/accessibilityService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/accessibility/common/accessibility", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/platform/actions/common/menuService": ["require", "exports", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey"], "vs/platform/clipboard/browser/clipboardService": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/lifecycle", "vs/platform/layout/browser/layoutService", "vs/platform/log/common/log"], "vs/platform/contextkey/browser/contextKeyService": ["require", "exports", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle", "vs/base/common/map", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/platform/commands/common/commands", "vs/platform/configuration/common/configuration", "vs/platform/contextkey/common/contextkey"], "vs/platform/instantiation/common/descriptors": ["require", "exports"], "vs/platform/instantiation/common/instantiationService": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/serviceCollection"], "vs/platform/markers/common/markerService": ["require", "exports", "vs/base/common/arrays", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/map", "vs/base/common/network", "vs/base/common/uri", "vs/platform/markers/common/markers"], "vs/editor/standalone/browser/standaloneLayoutService": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/platform/layout/browser/layoutService", "vs/editor/browser/services/codeEditorService", "vs/platform/instantiation/common/extensions"], "vs/platform/undoRedo/common/undoRedoService": ["require", "exports", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/network", "vs/base/common/severity", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/extensions", "vs/platform/notification/common/notification", "vs/platform/undoRedo/common/undoRedo"], "vs/nls!vs/platform/markers/common/markers": [], "===anonymous53===": ["vs/platform/markers/common/markers.nls", "vs/platform/markers/common/markers.nls.keys"], "vs/platform/markers/common/markers.nls": [], "vs/platform/markers/common/markers.nls.keys": [], "vs/nls!vs/editor/common/languages/modesRegistry": [], "vs/base/common/mime": ["require", "exports"], "===anonymous54===": ["vs/editor/common/languages/modesRegistry.nls", "vs/editor/common/languages/modesRegistry.nls.keys"], "vs/editor/common/languages/modesRegistry.nls": [], "vs/editor/common/languages/modesRegistry.nls.keys": [], "vs/editor/standalone/common/monarch/monarchCommon": ["require", "exports"], "vs/editor/contrib/editorState/browser/keybindingCancellation": ["require", "exports", "vs/editor/browser/editorExtensions", "vs/platform/contextkey/common/contextkey", "vs/base/common/cancellation", "vs/base/common/linkedList", "vs/platform/instantiation/common/instantiation", "vs/platform/instantiation/common/extensions", "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation"], "vs/editor/common/commands/surroundSelectionCommand": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/browser/config/migrateOptions": ["require", "exports", "vs/base/common/collections"], "vs/editor/browser/controller/pointerHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/common/platform", "vs/base/browser/touch", "vs/base/common/lifecycle", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/editorDom", "vs/base/browser/canIUse", "vs/editor/browser/controller/textAreaInput"], "vs/editor/browser/controller/textAreaHandler": ["require", "exports", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/controller/textAreaInput", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/viewParts/margin/margin", "vs/editor/common/config/editorOptions", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/editor/common/languages", "vs/base/common/color", "vs/css!vs/editor/browser/controller/textAreaHandler"], "vs/editor/browser/view/viewController": ["require", "exports", "vs/editor/browser/controller/coreCommands", "vs/editor/common/core/position", "vs/base/common/platform"], "vs/editor/browser/view/viewOverlays": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart"], "vs/editor/browser/view/viewPart": ["require", "exports", "vs/editor/common/viewModel/viewEventHandler"], "vs/editor/browser/viewParts/contentWidgets/contentWidgets": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/base/common/arrays", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight"], "vs/editor/browser/viewParts/decorations/decorations": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/range", "vs/editor/browser/view/renderingContext", "vs/css!vs/editor/browser/viewParts/decorations/decorations"], "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/themeService", "vs/platform/theme/common/colorRegistry"], "vs/editor/browser/viewParts/glyphMargin/glyphMargin": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin"], "vs/editor/browser/viewParts/indentGuides/indentGuides": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/position", "vs/base/common/arrays", "vs/base/common/types", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/textModelGuides", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides"], "vs/editor/browser/viewParts/lineNumbers/lineNumbers": ["require", "exports", "vs/base/common/platform", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/common/core/position", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers"], "vs/editor/browser/viewParts/lines/viewLines": ["require", "exports", "vs/base/common/platform", "vs/base/common/async", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/browser/view/renderingContext", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/editor/browser/viewParts/lines/viewLines"], "vs/editor/browser/viewParts/linesDecorations/linesDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations"], "vs/editor/browser/viewParts/margin/margin": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart"], "vs/editor/browser/viewParts/marginDecorations/marginDecorations": ["require", "exports", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations"], "vs/editor/browser/viewParts/minimap/minimap": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalMouseMoveMonitor", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings", "vs/editor/browser/view/viewLayer", "vs/editor/browser/view/viewPart", "vs/editor/common/config/editorOptions", "vs/editor/common/core/range", "vs/editor/common/core/rgba", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/viewModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/editor/common/core/selection", "vs/base/browser/touch", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/common/model", "vs/base/common/functional", "vs/css!vs/editor/browser/viewParts/minimap/minimap"], "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets"], "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/color", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/core/editorColorRegistry", "vs/editor/common/viewModel/viewModel"], "vs/editor/browser/viewParts/overviewRuler/overviewRuler": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/common/viewModel/viewEventHandler"], "vs/editor/browser/viewParts/rulers/rulers": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/rulers/rulers"], "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/browser/view/viewPart", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration"], "vs/editor/browser/viewParts/selections/selections": ["require", "exports", "vs/editor/browser/view/dynamicViewOverlay", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/selections/selections"], "vs/editor/browser/viewParts/viewCursors/viewCursors": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/async", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/config/editorOptions", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors"], "vs/editor/browser/viewParts/viewZones/viewZones": ["require", "exports", "vs/base/browser/fastDomNode", "vs/base/common/errors", "vs/editor/browser/view/viewPart", "vs/editor/common/core/position"], "vs/editor/browser/view/renderingContext": ["require", "exports"], "vs/editor/common/viewModel/viewContext": ["require", "exports"], "vs/editor/common/viewLayout/viewLinesViewportData": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/common/viewModel/viewEventHandler": ["require", "exports", "vs/base/common/lifecycle"], "vs/editor/browser/controller/mouseTarget": ["require", "exports", "vs/editor/browser/editorDom", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/cursorColumns", "vs/base/browser/dom", "vs/editor/common/cursor/cursorAtomicMoveOperations"], "vs/editor/common/cursor/cursorCollection": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/oneCursor", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/editor/common/textModelEvents": ["require", "exports"], "vs/editor/common/viewModel/viewEvents": ["require", "exports"], "vs/editor/common/viewModel/viewModelEventDispatcher": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/nls!vs/editor/common/core/editorColorRegistry": [], "===anonymous55===": ["vs/editor/common/core/editorColorRegistry.nls", "vs/editor/common/core/editorColorRegistry.nls.keys"], "vs/editor/common/core/editorColorRegistry.nls": [], "vs/editor/common/core/editorColorRegistry.nls.keys": [], "vs/editor/common/languages/textToHtmlTokenizer": ["require", "exports", "vs/base/common/strings", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages", "vs/editor/common/languages/nullMode"], "vs/editor/common/viewLayout/viewLayout": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/scrollable", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/viewModelEventDispatcher"], "vs/editor/common/viewModel/minimapTokensColorTracker": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/rgba", "vs/editor/common/languages"], "vs/editor/common/viewModel/viewModelLines": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/textModelGuides", "vs/editor/common/model/textModel", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel/viewEvents", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/viewModel/viewModel"], "vs/editor/common/viewModel/modelLineProjectionData": ["require", "exports", "vs/base/common/types", "vs/editor/common/core/position", "vs/editor/common/model"], "vs/base/browser/event": ["require", "exports", "vs/base/common/event"], "vs/base/common/decorators": ["require", "exports"], "vs/css!vs/base/browser/ui/sash/sash": [], "vs/nls!vs/editor/browser/widget/diffReview": [], "vs/base/browser/ui/actionbar/actionbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/common/actions", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/types", "vs/css!vs/base/browser/ui/actionbar/actionbar"], "vs/base/browser/ui/scrollbar/scrollableElement": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/browser/ui/widget", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/scrollable", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars"], "vs/css!vs/editor/browser/widget/media/diffReview": [], "===anonymous56===": ["vs/editor/browser/widget/diffReview.nls", "vs/editor/browser/widget/diffReview.nls.keys"], "vs/editor/browser/widget/diffReview.nls": [], "vs/editor/browser/widget/diffReview.nls.keys": [], "vs/base/common/buffer": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/richEditBrackets"], "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/common/model/editStack": ["require", "exports", "vs/nls!vs/editor/common/model/editStack", "vs/base/common/errors", "vs/editor/common/core/selection", "vs/base/common/uri", "vs/editor/common/core/textChange", "vs/base/common/buffer", "vs/base/common/resources"], "vs/editor/common/model/guidesTextModelPart": ["require", "exports", "vs/base/common/arrays", "vs/base/common/strings", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/range", "vs/editor/common/model/textModelPart", "vs/editor/common/model/utils", "vs/editor/common/textModelGuides"], "vs/editor/common/model/indentationGuesser": ["require", "exports"], "vs/editor/common/model/intervalTree": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer": ["require", "exports", "vs/base/common/event", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/core/eolCounter", "vs/editor/common/core/textChange", "vs/base/common/lifecycle"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer"], "vs/editor/common/model/textModelTokens": ["require", "exports", "vs/base/common/arrays", "vs/base/common/errors", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages", "vs/editor/common/languages/nullMode", "vs/base/common/lifecycle", "vs/base/common/stopwatch", "vs/editor/common/core/eolCounter", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/base/common/async", "vs/base/common/platform"], "vs/editor/common/core/eolCounter": ["require", "exports"], "vs/editor/common/tokens/contiguousTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/position", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages"], "vs/editor/common/tokens/sparseTokensStore": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/tokens/lineTokens"], "vs/platform/undoRedo/common/undoRedo": ["require", "exports", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/browser/widget/inlineDiffMargin": [], "===anonymous57===": ["vs/editor/browser/widget/inlineDiffMargin.nls", "vs/editor/browser/widget/inlineDiffMargin.nls.keys"], "vs/editor/browser/widget/inlineDiffMargin.nls": [], "vs/editor/browser/widget/inlineDiffMargin.nls.keys": [], "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor": [], "vs/nls!vs/platform/theme/common/iconRegistry": [], "===anonymous58===": ["vs/platform/theme/common/iconRegistry.nls", "vs/platform/theme/common/iconRegistry.nls.keys"], "vs/platform/theme/common/iconRegistry.nls": [], "vs/platform/theme/common/iconRegistry.nls.keys": [], "vs/editor/common/cursor/cursorAtomicMoveOperations": ["require", "exports", "vs/editor/common/core/cursorColumns"], "vs/editor/browser/controller/textAreaState": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/position", "vs/editor/common/core/range"], "vs/editor/contrib/codeAction/browser/codeAction": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/languages", "vs/editor/common/services/model", "vs/platform/commands/common/commands", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/browser/types"], "vs/editor/contrib/codeAction/browser/codeActionUi": ["require", "exports", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/contrib/message/browser/messageController", "vs/platform/instantiation/common/instantiation", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/editor/contrib/codeAction/browser/lightBulbWidget"], "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands": [], "vs/editor/contrib/codeAction/browser/codeActionModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/platform/contextkey/common/contextkey", "vs/platform/progress/common/progress", "vs/editor/contrib/codeAction/browser/codeAction"], "vs/editor/contrib/codeAction/browser/types": ["require", "exports"], "===anonymous59===": ["vs/editor/contrib/codeAction/browser/codeActionCommands.nls", "vs/editor/contrib/codeAction/browser/codeActionCommands.nls.keys"], "vs/editor/contrib/codeAction/browser/codeActionCommands.nls": [], "vs/editor/contrib/codeAction/browser/codeActionCommands.nls.keys": [], "vs/base/browser/ui/iconLabel/iconLabels": ["require", "exports", "vs/base/browser/dom", "vs/base/common/codicons"], "vs/css!vs/editor/contrib/codelens/browser/codelensWidget": [], "vs/base/common/numbers": ["require", "exports"], "vs/editor/contrib/colorPicker/browser/color": ["require", "exports", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/uri", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/common/services/model", "vs/platform/commands/common/commands"], "vs/base/browser/dnd": ["require", "exports", "vs/base/common/mime"], "vs/nls!vs/base/browser/ui/actionbar/actionViewItems": [], "vs/css!vs/base/browser/ui/actionbar/actionbar": [], "===anonymous60===": ["vs/base/browser/ui/actionbar/actionViewItems.nls", "vs/base/browser/ui/actionbar/actionViewItems.nls.keys"], "vs/base/browser/ui/actionbar/actionViewItems.nls": [], "vs/base/browser/ui/actionbar/actionViewItems.nls.keys": [], "vs/nls!vs/base/common/actions": [], "===anonymous61===": ["vs/base/common/actions.nls", "vs/base/common/actions.nls.keys"], "vs/base/common/actions.nls": [], "vs/base/common/actions.nls.keys": [], "vs/editor/contrib/find/browser/findDecorations": ["require", "exports", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/textModel", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/find/browser/replaceAllCommand": ["require", "exports", "vs/editor/common/core/range"], "vs/editor/contrib/find/browser/replacePattern": ["require", "exports", "vs/base/common/search"], "vs/base/browser/ui/findinput/findInputCheckboxes": ["require", "exports", "vs/base/browser/ui/checkbox/checkbox", "vs/base/common/codicons", "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes"], "vs/base/browser/ui/checkbox/checkbox": ["require", "exports", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/color", "vs/base/common/event", "vs/css!vs/base/browser/ui/checkbox/checkbox"], "vs/nls!vs/editor/contrib/find/browser/findWidget": [], "vs/platform/history/browser/contextScopedHistoryWidget": ["require", "exports", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/platform/contextkey/common/contextkey", "vs/platform/keybinding/common/keybindingsRegistry"], "vs/platform/history/browser/historyWidgetKeybindingHint": ["require", "exports"], "vs/css!vs/editor/contrib/find/browser/findWidget": [], "===anonymous62===": ["vs/editor/contrib/find/browser/findWidget.nls", "vs/editor/contrib/find/browser/findWidget.nls.keys"], "vs/editor/contrib/find/browser/findWidget.nls": [], "vs/editor/contrib/find/browser/findWidget.nls.keys": [], "vs/editor/contrib/folding/browser/foldingRanges": ["require", "exports"], "vs/editor/common/model/utils": ["require", "exports"], "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations": [], "===anonymous63===": ["vs/editor/contrib/folding/browser/foldingDecorations.nls", "vs/editor/contrib/folding/browser/foldingDecorations.nls.keys"], "vs/editor/contrib/folding/browser/foldingDecorations.nls": [], "vs/editor/contrib/folding/browser/foldingDecorations.nls.keys": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/browser/controller/coreCommands", "vs/editor/common/core/editOperation", "vs/editor/common/core/range", "vs/editor/common/languages", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/platform/commands/common/commands", "vs/editor/contrib/inlineCompletions/browser/consts", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionToGhostText", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets"], "vs/editor/contrib/inlineCompletions/browser/suggestWidgetPreviewModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/editor/common/languages", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionToGhostText", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider"], "vs/editor/contrib/inlineCompletions/browser/utils": ["require", "exports"], "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText": [], "vs/base/common/idGenerator": ["require", "exports"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel": [], "===anonymous64===": ["vs/editor/contrib/gotoSymbol/browser/referencesModel.nls", "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls": [], "vs/editor/contrib/gotoSymbol/browser/referencesModel.nls.keys": [], "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation": [], "===anonymous65===": ["vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls": [], "vs/editor/contrib/gotoSymbol/browser/symbolNavigation.nls.keys": [], "vs/nls!vs/editor/contrib/message/browser/messageController": [], "vs/css!vs/editor/contrib/message/browser/messageController": [], "===anonymous66===": ["vs/editor/contrib/message/browser/messageController.nls", "vs/editor/contrib/message/browser/messageController.nls.keys"], "vs/editor/contrib/message/browser/messageController.nls": [], "vs/editor/contrib/message/browser/messageController.nls.keys": [], "vs/editor/contrib/zoneWidget/browser/zoneWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/color", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/editor/common/core/range", "vs/editor/common/model/textModel", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget"], "vs/nls!vs/editor/contrib/peekView/browser/peekView": [], "vs/platform/actions/browser/menuEntryActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/common/actions", "vs/base/common/keybindingLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/contextview/browser/contextView", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/notification/common/notification", "vs/platform/storage/common/storage", "vs/platform/theme/common/themeService", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem"], "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget": [], "===anonymous67===": ["vs/editor/contrib/peekView/browser/peekView.nls", "vs/editor/contrib/peekView/browser/peekView.nls.keys"], "vs/editor/contrib/peekView/browser/peekView.nls": [], "vs/editor/contrib/peekView/browser/peekView.nls.keys": [], "vs/base/common/labels": ["require", "exports", "vs/base/common/extpath", "vs/base/common/network", "vs/base/common/platform", "vs/base/common/resources", "vs/base/common/uri"], "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget": [], "vs/platform/severityIcon/common/severityIcon": ["require", "exports", "vs/base/common/codicons", "vs/base/common/severity", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget": [], "===anonymous68===": ["vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls", "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls.keys"], "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls": [], "vs/editor/contrib/gotoError/browser/gotoErrorWidget.nls.keys": [], "vs/base/browser/ui/hover/hoverWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/hover/hover"], "vs/editor/contrib/hover/browser/colorHoverParticipant": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/color", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/platform/theme/common/themeService"], "vs/editor/contrib/hover/browser/hoverOperation": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/hover/browser/hoverTypes": ["require", "exports"], "vs/editor/contrib/hover/browser/markerHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/core/range", "vs/editor/common/services/markerDecorations", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/types", "vs/editor/contrib/gotoError/browser/gotoError", "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/platform/markers/common/markers", "vs/platform/opener/common/opener", "vs/platform/progress/common/progress", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant": ["require", "exports", "vs/base/browser/dom", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/common/core/range", "vs/editor/common/services/language", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inlineCompletions/browser/ghostTextController", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant", "vs/platform/accessibility/common/accessibility", "vs/platform/actions/common/actions", "vs/platform/commands/common/commands", "vs/platform/contextkey/common/contextkey", "vs/platform/opener/common/opener"], "vs/editor/contrib/inlayHints/browser/inlayHintsHover": ["require", "exports", "vs/base/common/async", "vs/base/common/htmlContent", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/common/model/textModel", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/common/services/language", "vs/editor/common/services/resolverService", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/platform/configuration/common/configuration", "vs/platform/opener/common/opener"], "vs/editor/contrib/markdownRenderer/browser/markdownRenderer": ["require", "exports", "vs/base/browser/markdownRenderer", "vs/platform/opener/common/opener", "vs/editor/common/services/language", "vs/base/common/errors", "vs/editor/common/languages/textToHtmlTokenizer", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/browser/config/domFontInfo", "vs/editor/common/languages/modesRegistry"], "vs/base/browser/globalMouseMoveMonitor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/iframe", "vs/base/browser/mouseEvent", "vs/base/common/lifecycle", "vs/base/common/platform"], "vs/base/common/extpath": ["require", "exports", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/editor/contrib/parameterHints/browser/parameterHintsModel": ["require", "exports", "vs/base/common/async", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/characterClassifier", "vs/editor/common/languages", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp"], "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget": [], "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints": [], "===anonymous69===": ["vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls.keys"], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls": [], "vs/editor/contrib/parameterHints/browser/parameterHintsWidget.nls.keys": [], "vs/nls!vs/platform/configuration/common/configurationRegistry": [], "===anonymous70===": ["vs/platform/configuration/common/configurationRegistry.nls", "vs/platform/configuration/common/configurationRegistry.nls.keys"], "vs/platform/configuration/common/configurationRegistry.nls": [], "vs/platform/configuration/common/configurationRegistry.nls.keys": [], "vs/nls!vs/editor/contrib/rename/browser/renameInputField": [], "vs/css!vs/editor/contrib/rename/browser/renameInputField": [], "===anonymous71===": ["vs/editor/contrib/rename/browser/renameInputField.nls", "vs/editor/contrib/rename/browser/renameInputField.nls.keys"], "vs/editor/contrib/rename/browser/renameInputField.nls": [], "vs/editor/contrib/rename/browser/renameInputField.nls.keys": [], "vs/base/common/filters": ["require", "exports", "vs/base/common/map", "vs/base/common/strings"], "vs/nls!vs/editor/contrib/suggest/browser/suggest": [], "===anonymous72===": ["vs/editor/contrib/suggest/browser/suggest.nls", "vs/editor/contrib/suggest/browser/suggest.nls.keys"], "vs/editor/contrib/suggest/browser/suggest.nls": [], "vs/editor/contrib/suggest/browser/suggest.nls.keys": [], "vs/editor/contrib/snippet/browser/snippetVariables": ["require", "exports", "vs/base/common/labels", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uuid", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/contrib/snippet/browser/snippetParser", "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables", "vs/platform/workspaces/common/workspaces"], "vs/css!vs/editor/contrib/snippet/browser/snippetSession": [], "vs/editor/contrib/suggest/browser/wordDistance": ["require", "exports", "vs/base/common/arrays", "vs/editor/common/core/range", "vs/editor/contrib/smartSelect/browser/bracketSelections"], "vs/editor/contrib/suggest/browser/completionModel": ["require", "exports", "vs/base/common/arrays", "vs/base/common/filters", "vs/base/common/strings"], "vs/base/browser/ui/list/listWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/list/splice", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/objects", "vs/base/common/platform", "vs/base/common/types", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/listView", "vs/css!vs/base/browser/ui/list/list"], "vs/editor/contrib/suggest/browser/suggestWidgetStatus": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionbar", "vs/base/common/lifecycle", "vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/platform/actions/common/actions", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation"], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget": [], "vs/platform/theme/common/styler": ["require", "exports", "vs/platform/theme/common/colorRegistry"], "vs/editor/contrib/suggest/browser/resizable": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/contrib/suggest/browser/suggestWidgetDetails": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/lifecycle", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/common/config/editorOptions", "vs/editor/contrib/suggest/browser/resizable", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/platform/instantiation/common/instantiation"], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/uri", "vs/editor/common/config/editorOptions", "vs/editor/common/languages", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/model", "vs/editor/common/services/language", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/platform/files/common/files", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService", "vs/editor/contrib/suggest/browser/suggestWidgetDetails"], "vs/css!vs/editor/contrib/suggest/browser/media/suggest": [], "===anonymous73===": ["vs/editor/contrib/suggest/browser/suggestWidget.nls", "vs/editor/contrib/suggest/browser/suggestWidget.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidget.nls": [], "vs/editor/contrib/suggest/browser/suggestWidget.nls.keys": [], "vs/editor/contrib/hover/browser/getHover": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/editor/browser/editorExtensions", "vs/editor/common/languages"], "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant": [], "===anonymous74===": ["vs/editor/contrib/hover/browser/markdownHoverParticipant.nls", "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls.keys"], "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls": [], "vs/editor/contrib/hover/browser/markdownHoverParticipant.nls.keys": [], "vs/platform/opener/browser/link": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/opener/common/opener", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController": [], "vs/editor/common/services/semanticTokensDto": ["require", "exports", "vs/base/common/buffer", "vs/base/common/platform"], "vs/editor/common/tokens/sparseMultilineTokens": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/eolCounter"], "vs/nls!vs/platform/contextkey/common/contextkeys": [], "===anonymous75===": ["vs/platform/contextkey/common/contextkeys.nls", "vs/platform/contextkey/common/contextkeys.nls.keys"], "vs/platform/contextkey/common/contextkeys.nls": [], "vs/platform/contextkey/common/contextkeys.nls.keys": [], "vs/base/browser/iframe": ["require", "exports"], "vs/editor/common/languageSelector": ["require", "exports", "vs/base/common/glob", "vs/base/common/path"], "vs/base/common/errorMessage": ["require", "exports", "vs/base/common/arrays", "vs/base/common/types", "vs/nls!vs/base/common/errorMessage"], "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess": [], "vs/platform/quickinput/browser/pickerQuickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/lifecycle"], "===anonymous76===": ["vs/platform/quickinput/browser/commandsQuickAccess.nls", "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys"], "vs/platform/quickinput/browser/commandsQuickAccess.nls": [], "vs/platform/quickinput/browser/commandsQuickAccess.nls.keys": [], "vs/base/browser/ui/list/listPaging": ["require", "exports", "vs/base/common/arrays", "vs/base/common/cancellation", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/browser/ui/list/listWidget", "vs/css!vs/base/browser/ui/list/list"], "vs/base/browser/ui/table/tableWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/splitview/splitview", "vs/base/common/event", "vs/base/common/lifecycle", "vs/css!vs/base/browser/ui/table/table"], "vs/base/browser/ui/tree/asyncDataTree": ["require", "exports", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/tree", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/iterator", "vs/base/common/lifecycle"], "vs/base/browser/ui/tree/dataTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/objectTreeModel"], "vs/base/browser/ui/tree/objectTree": ["require", "exports", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/common/decorators", "vs/base/common/iterator"], "vs/nls!vs/platform/list/browser/listService": [], "===anonymous77===": ["vs/platform/list/browser/listService.nls", "vs/platform/list/browser/listService.nls.keys"], "vs/platform/list/browser/listService.nls": [], "vs/platform/list/browser/listService.nls.keys": [], "vs/base/browser/ui/splitview/splitview": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/sash/sash", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/scrollable", "vs/base/common/types", "vs/css!vs/base/browser/ui/splitview/splitview"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/common/filters", "vs/base/common/labels", "vs/base/common/lifecycle", "vs/base/common/resources", "vs/editor/common/services/resolverService", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/platform/instantiation/common/instantiation", "vs/platform/keybinding/common/keybinding", "vs/platform/label/common/label", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService", "vs/editor/contrib/gotoSymbol/browser/referencesModel"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [], "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [], "===anonymous78===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget.nls.keys": [], "vs/base/browser/defaultWorkerFactory": ["require", "exports", "vs/base/common/platform", "vs/base/common/worker/simpleWorker"], "vs/editor/browser/services/abstractCodeEditorService": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/platform/theme/common/themeService"], "vs/nls!vs/editor/common/config/editorConfigurationSchema": [], "===anonymous79===": ["vs/editor/common/config/editorConfigurationSchema.nls", "vs/editor/common/config/editorConfigurationSchema.nls.keys"], "vs/editor/common/config/editorConfigurationSchema.nls": [], "vs/editor/common/config/editorConfigurationSchema.nls.keys": [], "vs/nls!vs/platform/keybinding/common/abstractKeybindingService": [], "===anonymous80===": ["vs/platform/keybinding/common/abstractKeybindingService.nls", "vs/platform/keybinding/common/abstractKeybindingService.nls.keys"], "vs/platform/keybinding/common/abstractKeybindingService.nls": [], "vs/platform/keybinding/common/abstractKeybindingService.nls.keys": [], "vs/platform/keybinding/common/baseResolvedKeybinding": ["require", "exports", "vs/base/common/errors", "vs/base/common/keybindingLabels", "vs/base/common/keybindings"], "vs/base/browser/ui/contextview/contextview": ["require", "exports", "vs/base/browser/canIUse", "vs/base/browser/dom", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/range", "vs/css!vs/base/browser/ui/contextview/contextview"], "vs/editor/common/services/languagesRegistry": ["require", "exports", "vs/base/common/arrays", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/strings", "vs/editor/common/services/languagesAssociations", "vs/editor/common/languages/modesRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/platform/registry/common/platform"], "vs/platform/contextview/browser/contextMenuHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/browser/ui/menu/menu", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lifecycle", "vs/platform/theme/common/styler", "vs/css!vs/platform/contextview/browser/contextMenuHandler"], "vs/base/common/marshalling": ["require", "exports", "vs/base/common/buffer", "vs/base/common/uri"], "vs/platform/editor/common/editor": ["require", "exports"], "vs/platform/quickinput/browser/quickInput": ["require", "exports", "vs/base/common/cancellation", "vs/base/parts/quickinput/browser/quickInput", "vs/platform/accessibility/common/accessibility", "vs/platform/contextkey/common/contextkey", "vs/platform/instantiation/common/instantiation", "vs/platform/layout/browser/layoutService", "vs/platform/list/browser/listService", "vs/platform/quickinput/browser/quickAccess", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/styler", "vs/platform/theme/common/themeService"], "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput": [], "vs/editor/common/languages/supports/tokenization": ["require", "exports", "vs/base/common/color"], "vs/editor/standalone/common/themes": ["require", "exports", "vs/editor/common/core/editorColorRegistry", "vs/platform/theme/common/colorRegistry"], "vs/platform/theme/browser/iconsStyleSheet": ["require", "exports", "vs/base/browser/dom", "vs/base/common/event", "vs/platform/theme/common/iconRegistry", "vs/platform/theme/common/themeService"], "vs/nls!vs/platform/contextkey/browser/contextKeyService": [], "===anonymous81===": ["vs/platform/contextkey/browser/contextKeyService.nls", "vs/platform/contextkey/browser/contextKeyService.nls.keys"], "vs/platform/contextkey/browser/contextKeyService.nls": [], "vs/platform/contextkey/browser/contextKeyService.nls.keys": [], "vs/platform/instantiation/common/graph": ["require", "exports"], "vs/nls!vs/platform/undoRedo/common/undoRedoService": [], "===anonymous82===": ["vs/platform/undoRedo/common/undoRedoService.nls", "vs/platform/undoRedo/common/undoRedoService.nls.keys"], "vs/platform/undoRedo/common/undoRedoService.nls": [], "vs/platform/undoRedo/common/undoRedoService.nls.keys": [], "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation": [], "===anonymous83===": ["vs/editor/contrib/editorState/browser/keybindingCancellation.nls", "vs/editor/contrib/editorState/browser/keybindingCancellation.nls.keys"], "vs/editor/contrib/editorState/browser/keybindingCancellation.nls": [], "vs/editor/contrib/editorState/browser/keybindingCancellation.nls.keys": [], "vs/base/common/collections": ["require", "exports"], "vs/editor/browser/controller/mouseHandler": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/mouseEvent", "vs/base/common/async", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/editorDom", "vs/editor/common/config/editorZoom", "vs/editor/common/core/position", "vs/editor/common/core/selection", "vs/editor/common/viewModel/viewEventHandler"], "vs/nls!vs/editor/browser/controller/textAreaHandler": [], "vs/css!vs/editor/browser/controller/textAreaHandler": [], "===anonymous84===": ["vs/editor/browser/controller/textAreaHandler.nls", "vs/editor/browser/controller/textAreaHandler.nls.keys"], "vs/editor/browser/controller/textAreaHandler.nls": [], "vs/editor/browser/controller/textAreaHandler.nls.keys": [], "vs/editor/browser/view/viewLayer": ["require", "exports", "vs/base/browser/fastDomNode", "vs/editor/common/core/stringBuilder"], "vs/editor/browser/view/dynamicViewOverlay": ["require", "exports", "vs/editor/common/viewModel/viewEventHandler"], "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight": [], "vs/css!vs/editor/browser/viewParts/decorations/decorations": [], "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin": [], "vs/editor/common/textModelGuides": ["require", "exports"], "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides": [], "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers": [], "vs/editor/browser/viewParts/lines/viewLine": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/fastDomNode", "vs/base/common/platform", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/view/renderingContext", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/viewLineRenderer", "vs/platform/theme/common/theme", "vs/editor/common/config/editorOptions"], "vs/css!vs/editor/browser/viewParts/lines/viewLines": [], "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations": [], "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations": [], "vs/editor/common/core/rgba": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/base/common/uint"], "vs/css!vs/editor/browser/viewParts/minimap/minimap": [], "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets": [], "vs/css!vs/editor/browser/viewParts/rulers/rulers": [], "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration": [], "vs/css!vs/editor/browser/viewParts/selections/selections": [], "vs/editor/browser/viewParts/viewCursors/viewCursor": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/common/strings", "vs/editor/browser/config/domFontInfo", "vs/editor/common/config/editorOptions", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/base/browser/ui/mouseCursor/mouseCursor"], "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors": [], "vs/editor/common/cursor/oneCursor": ["require", "exports", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection"], "vs/base/common/scrollable": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle"], "vs/editor/common/viewLayout/linesLayout": ["require", "exports", "vs/base/common/strings"], "vs/editor/common/viewModel/modelLineProjection": ["require", "exports", "vs/editor/common/tokens/lineTokens", "vs/editor/common/core/position", "vs/editor/common/textModelEvents", "vs/editor/common/viewModel/viewModel"], "vs/base/browser/ui/scrollbar/horizontalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/base/browser/ui/scrollbar/verticalScrollbar": ["require", "exports", "vs/base/browser/mouseEvent", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/common/codicons"], "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars": [], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range", "vs/editor/common/textModelBracketPairs", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/nls!vs/editor/common/model/editStack": [], "vs/editor/common/core/textChange": ["require", "exports", "vs/base/common/buffer", "vs/editor/common/core/stringBuilder"], "===anonymous85===": ["vs/editor/common/model/editStack.nls", "vs/editor/common/model/editStack.nls.keys"], "vs/editor/common/model/editStack.nls": [], "vs/editor/common/model/editStack.nls.keys": [], "vs/editor/common/model/textModelPart": ["require", "exports"], "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase": ["require", "exports", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/model", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/textModelSearch"], "vs/editor/common/tokens/contiguousMultilineTokensBuilder": ["require", "exports", "vs/editor/common/tokens/contiguousMultilineTokens"], "vs/editor/common/tokens/contiguousTokensEditing": ["require", "exports", "vs/editor/common/tokens/lineTokens"], "vs/editor/contrib/codeAction/browser/codeActionMenu": ["require", "exports", "vs/base/browser/dom", "vs/base/common/actions", "vs/base/common/errors", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/languages", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/types", "vs/platform/contextview/browser/contextView", "vs/platform/keybinding/common/keybinding"], "vs/editor/contrib/codeAction/browser/lightBulbWidget": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/touch", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/model/utils", "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/platform/keybinding/common/keybinding", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget"], "vs/base/common/search": ["require", "exports", "vs/base/common/strings"], "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes": [], "===anonymous86===": ["vs/base/browser/ui/findinput/findInputCheckboxes.nls", "vs/base/browser/ui/findinput/findInputCheckboxes.nls.keys"], "vs/base/browser/ui/findinput/findInputCheckboxes.nls": [], "vs/base/browser/ui/findinput/findInputCheckboxes.nls.keys": [], "vs/css!vs/base/browser/ui/checkbox/checkbox": [], "vs/base/browser/ui/findinput/findInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/base/browser/ui/findinput/replaceInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/widget", "vs/base/common/codicons", "vs/base/common/event", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/css!vs/base/browser/ui/findinput/findInput"], "vs/editor/contrib/inlineCompletions/browser/ghostText": ["require", "exports", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/range"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionToGhostText": ["require", "exports", "vs/base/common/diff/diff", "vs/base/common/strings", "vs/editor/common/core/range", "vs/editor/contrib/inlineCompletions/browser/ghostText"], "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider": ["require", "exports", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/event", "vs/base/common/lifecycle", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionToGhostText"], "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget": [], "vs/base/browser/ui/dropdown/dropdownActionViewItem": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/dropdown/dropdown", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/base/common/keybindingLabels": ["require", "exports", "vs/nls!vs/base/common/keybindingLabels"], "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem": [], "vs/css!vs/platform/actions/browser/menuEntryActionViewItem": [], "===anonymous87===": ["vs/platform/actions/browser/menuEntryActionViewItem.nls", "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys"], "vs/platform/actions/browser/menuEntryActionViewItem.nls": [], "vs/platform/actions/browser/menuEntryActionViewItem.nls.keys": [], "vs/css!vs/base/browser/ui/hover/hover": [], "vs/editor/contrib/colorPicker/browser/colorPickerModel": ["require", "exports", "vs/base/common/event"], "vs/editor/contrib/colorPicker/browser/colorPickerWidget": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dom", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/platform/theme/common/colorRegistry", "vs/platform/theme/common/themeService", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker"], "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant": [], "===anonymous88===": ["vs/editor/contrib/hover/browser/markerHoverParticipant.nls", "vs/editor/contrib/hover/browser/markerHoverParticipant.nls.keys"], "vs/editor/contrib/hover/browser/markerHoverParticipant.nls": [], "vs/editor/contrib/hover/browser/markerHoverParticipant.nls.keys": [], "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant": [], "===anonymous89===": ["vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant.nls", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant.nls.keys"], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant.nls": [], "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant.nls.keys": [], "vs/base/browser/markdownRenderer": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/dompurify/dompurify", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/mouseEvent", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/errors", "vs/base/common/event", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/idGenerator", "vs/base/common/lifecycle", "vs/base/common/marked/marked", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/common/objects", "vs/base/common/resources", "vs/base/common/strings", "vs/base/common/uri"], "vs/base/common/uuid": ["require", "exports"], "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables": [], "vs/platform/workspaces/common/workspaces": ["require", "exports", "vs/base/common/uri", "vs/nls!vs/platform/workspaces/common/workspaces"], "===anonymous90===": ["vs/editor/contrib/snippet/browser/snippetVariables.nls", "vs/editor/contrib/snippet/browser/snippetVariables.nls.keys"], "vs/editor/contrib/snippet/browser/snippetVariables.nls": [], "vs/editor/contrib/snippet/browser/snippetVariables.nls.keys": [], "vs/base/browser/ui/list/splice": ["require", "exports"], "vs/base/browser/ui/list/list": ["require", "exports"], "vs/base/browser/ui/list/listView": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/touch", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/base/common/scrollable", "vs/base/browser/ui/list/rangeMap", "vs/base/browser/ui/list/rowCache"], "vs/css!vs/base/browser/ui/list/list": [], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus": [], "===anonymous91===": ["vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls", "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetStatus.nls.keys": [], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails": [], "===anonymous92===": ["vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls", "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetDetails.nls.keys": [], "vs/base/browser/ui/iconLabel/iconLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/base/common/range", "vs/css!vs/base/browser/ui/iconLabel/iconlabel"], "vs/editor/common/services/getIconClasses": ["require", "exports", "vs/base/common/network", "vs/base/common/resources", "vs/editor/common/languages/modesRegistry", "vs/platform/files/common/files"], "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer": [], "vs/platform/files/common/files": ["require", "exports"], "===anonymous93===": ["vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls.keys"], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls": [], "vs/editor/contrib/suggest/browser/suggestWidgetRenderer.nls.keys": [], "vs/base/common/glob": ["require", "exports", "vs/base/common/async", "vs/base/common/extpath", "vs/base/common/map", "vs/base/common/path", "vs/base/common/platform", "vs/base/common/strings"], "vs/nls!vs/base/common/errorMessage": [], "===anonymous94===": ["vs/base/common/errorMessage.nls", "vs/base/common/errorMessage.nls.keys"], "vs/base/common/errorMessage.nls": [], "vs/base/common/errorMessage.nls.keys": [], "vs/css!vs/base/browser/ui/table/table": [], "vs/base/browser/ui/tree/abstractTree": ["require", "exports", "vs/base/browser/dnd", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/collections", "vs/base/common/event", "vs/base/common/filters", "vs/base/common/lifecycle", "vs/base/common/numbers", "vs/base/common/platform", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/css!vs/base/browser/ui/tree/media/tree"], "vs/base/browser/ui/tree/indexTreeModel": ["require", "exports", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/diff/diff", "vs/base/common/event", "vs/base/common/iterator"], "vs/base/browser/ui/tree/tree": ["require", "exports"], "vs/base/browser/ui/tree/objectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/iterator"], "vs/base/browser/ui/tree/compressedObjectTreeModel": ["require", "exports", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/tree", "vs/base/common/event", "vs/base/common/iterator"], "vs/css!vs/base/browser/ui/splitview/splitview": [], "vs/base/browser/ui/countBadge/countBadge": ["require", "exports", "vs/base/browser/dom", "vs/base/common/color", "vs/base/common/objects", "vs/base/common/strings", "vs/css!vs/base/browser/ui/countBadge/countBadge"], "vs/base/browser/ui/highlightedlabel/highlightedLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/objects"], "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": [], "===anonymous95===": ["vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls.keys"], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls": [], "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree.nls.keys": [], "vs/base/common/range": ["require", "exports"], "vs/css!vs/base/browser/ui/contextview/contextview": [], "vs/editor/common/services/languagesAssociations": ["require", "exports", "vs/base/common/glob", "vs/base/common/mime", "vs/base/common/network", "vs/base/common/path", "vs/base/common/resources", "vs/base/common/strings"], "vs/base/browser/ui/menu/menu": ["require", "exports", "vs/base/browser/browser", "vs/base/browser/touch", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/codicons/codiconStyles", "vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/common/actions", "vs/base/common/async", "vs/base/common/codicons", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/strings"], "vs/css!vs/platform/contextview/browser/contextMenuHandler": [], "vs/base/parts/quickinput/browser/quickInput": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/button/button", "vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/progressbar/progressbar", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/codicons", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/severity", "vs/base/common/types", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/base/parts/quickinput/common/quickInput", "vs/nls!vs/base/parts/quickinput/browser/quickInput", "vs/base/parts/quickinput/browser/quickInputBox", "vs/base/parts/quickinput/browser/quickInputList", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/platform/quickinput/browser/quickAccess": ["require", "exports", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/functional", "vs/base/common/lifecycle", "vs/platform/instantiation/common/instantiation", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform"], "vs/editor/browser/viewParts/lines/rangeUtil": ["require", "exports", "vs/editor/browser/view/renderingContext"], "vs/editor/browser/viewParts/minimap/minimapCharRenderer": ["require", "exports", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/base/common/uint"], "vs/editor/browser/viewParts/minimap/minimapCharSheet": ["require", "exports"], "vs/editor/browser/viewParts/minimap/minimapPreBaked": ["require", "exports", "vs/base/common/functional"], "vs/base/browser/ui/scrollbar/abstractScrollbar": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/fastDomNode", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/browser/ui/widget", "vs/base/common/platform"], "vs/base/browser/ui/scrollbar/scrollbarArrow": ["require", "exports", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/ui/widget", "vs/base/common/async"], "vs/base/browser/ui/scrollbar/scrollbarState": ["require", "exports"], "vs/editor/common/textModelBracketPairs": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets": ["require", "exports", "vs/base/common/strings", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length": ["require", "exports", "vs/base/common/strings", "vs/editor/common/core/range"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet": ["require", "exports"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer": ["require", "exports", "vs/base/common/errors", "vs/editor/common/languages", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase": ["require", "exports"], "vs/editor/common/tokens/contiguousMultilineTokens": ["require", "exports"], "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget": [], "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget": [], "===anonymous96===": ["vs/editor/contrib/codeAction/browser/lightBulbWidget.nls", "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls.keys"], "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls": [], "vs/editor/contrib/codeAction/browser/lightBulbWidget.nls.keys": [], "vs/base/browser/ui/inputbox/inputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/event", "vs/base/browser/formattedTextRenderer", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/aria/aria", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/widget", "vs/base/common/color", "vs/base/common/event", "vs/base/common/history", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/inputbox/inputBox"], "vs/nls!vs/base/browser/ui/findinput/findInput": [], "vs/css!vs/base/browser/ui/findinput/findInput": [], "===anonymous97===": ["vs/base/browser/ui/findinput/findInput.nls", "vs/base/browser/ui/findinput/findInput.nls.keys"], "vs/base/browser/ui/findinput/findInput.nls": [], "vs/base/browser/ui/findinput/findInput.nls.keys": [], "vs/nls!vs/base/browser/ui/findinput/replaceInput": [], "===anonymous98===": ["vs/base/browser/ui/findinput/replaceInput.nls", "vs/base/browser/ui/findinput/replaceInput.nls.keys"], "vs/base/browser/ui/findinput/replaceInput.nls": [], "vs/base/browser/ui/findinput/replaceInput.nls.keys": [], "vs/base/browser/ui/dropdown/dropdown": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/common/actions", "vs/base/common/event", "vs/css!vs/base/browser/ui/dropdown/dropdown"], "vs/css!vs/base/browser/ui/dropdown/dropdown": [], "vs/nls!vs/base/common/keybindingLabels": [], "===anonymous99===": ["vs/base/common/keybindingLabels.nls", "vs/base/common/keybindingLabels.nls.keys"], "vs/base/common/keybindingLabels.nls": [], "vs/base/common/keybindingLabels.nls.keys": [], "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget": [], "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker": [], "===anonymous100===": ["vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls", "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls.keys"], "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls": [], "vs/editor/contrib/colorPicker/browser/colorPickerWidget.nls.keys": [], "vs/base/browser/dompurify/dompurify": ["require", "exports", "module"], "vs/base/common/marked/marked": ["exports"], "vs/nls!vs/platform/workspaces/common/workspaces": [], "===anonymous101===": ["vs/platform/workspaces/common/workspaces.nls", "vs/platform/workspaces/common/workspaces.nls.keys"], "vs/platform/workspaces/common/workspaces.nls": [], "vs/platform/workspaces/common/workspaces.nls.keys": [], "vs/base/browser/ui/list/rangeMap": ["require", "exports", "vs/base/common/range"], "vs/base/browser/ui/list/rowCache": ["require", "exports", "vs/base/browser/dom"], "vs/base/browser/ui/iconLabel/iconLabelHover": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/cancellation", "vs/base/common/htmlContent", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/types", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover"], "vs/css!vs/base/browser/ui/iconLabel/iconlabel": [], "vs/nls!vs/base/browser/ui/tree/abstractTree": [], "vs/css!vs/base/browser/ui/tree/media/tree": [], "===anonymous102===": ["vs/base/browser/ui/tree/abstractTree.nls", "vs/base/browser/ui/tree/abstractTree.nls.keys"], "vs/base/browser/ui/tree/abstractTree.nls": [], "vs/base/browser/ui/tree/abstractTree.nls.keys": [], "vs/css!vs/base/browser/ui/countBadge/countBadge": [], "vs/base/browser/ui/button/button": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/common/color", "vs/base/common/event", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/css!vs/base/browser/ui/button/button"], "vs/base/browser/ui/progressbar/progressbar": ["require", "exports", "vs/base/browser/dom", "vs/base/common/async", "vs/base/common/color", "vs/base/common/lifecycle", "vs/base/common/objects", "vs/css!vs/base/browser/ui/progressbar/progressbar"], "vs/base/parts/quickinput/browser/quickInputUtils": ["require", "exports", "vs/base/browser/dom", "vs/base/common/idGenerator", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/nls!vs/base/parts/quickinput/browser/quickInput": [], "vs/base/parts/quickinput/browser/quickInputBox": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/browser/ui/inputbox/inputBox", "vs/base/common/lifecycle", "vs/base/common/severity", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/base/parts/quickinput/browser/quickInputList": ["require", "exports", "vs/base/browser/dom", "vs/base/browser/keyboardEvent", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/iconLabel/iconLabel", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/base/common/actions", "vs/base/common/arrays", "vs/base/common/codicons", "vs/base/common/comparers", "vs/base/common/decorators", "vs/base/common/event", "vs/base/common/iconLabels", "vs/base/common/lifecycle", "vs/base/common/platform", "vs/base/common/types", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/nls!vs/base/parts/quickinput/browser/quickInputList", "vs/css!vs/base/parts/quickinput/browser/media/quickInput"], "vs/css!vs/base/parts/quickinput/browser/media/quickInput": [], "===anonymous103===": ["vs/base/parts/quickinput/browser/quickInput.nls", "vs/base/parts/quickinput/browser/quickInput.nls.keys"], "vs/base/parts/quickinput/browser/quickInput.nls": [], "vs/base/parts/quickinput/browser/quickInput.nls.keys": [], "vs/base/browser/ui/scrollbar/scrollbarVisibilityController": ["require", "exports", "vs/base/common/async", "vs/base/common/lifecycle"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast": ["require", "exports", "vs/editor/common/core/cursorColumns", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast"], "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader": ["require", "exports", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length"], "vs/base/common/history": ["require", "exports", "vs/base/common/navigator"], "vs/nls!vs/base/browser/ui/inputbox/inputBox": [], "vs/css!vs/base/browser/ui/inputbox/inputBox": [], "===anonymous104===": ["vs/base/browser/ui/inputbox/inputBox.nls", "vs/base/browser/ui/inputbox/inputBox.nls.keys"], "vs/base/browser/ui/inputbox/inputBox.nls": [], "vs/base/browser/ui/inputbox/inputBox.nls.keys": [], "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover": [], "===anonymous105===": ["vs/base/browser/ui/iconLabel/iconLabelHover.nls", "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys"], "vs/base/browser/ui/iconLabel/iconLabelHover.nls": [], "vs/base/browser/ui/iconLabel/iconLabelHover.nls.keys": [], "vs/css!vs/base/browser/ui/button/button": [], "vs/css!vs/base/browser/ui/progressbar/progressbar": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel": ["require", "exports", "vs/base/browser/dom", "vs/base/common/keybindingLabels", "vs/base/common/objects", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel"], "vs/base/common/comparers": ["require", "exports", "vs/base/common/async"], "vs/nls!vs/base/parts/quickinput/browser/quickInputList": [], "===anonymous106===": ["vs/base/parts/quickinput/browser/quickInputList.nls", "vs/base/parts/quickinput/browser/quickInputList.nls.keys"], "vs/base/parts/quickinput/browser/quickInputList.nls": [], "vs/base/parts/quickinput/browser/quickInputList.nls.keys": [], "vs/base/common/navigator": ["require", "exports"], "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel": [], "===anonymous107===": ["vs/base/browser/ui/keybindingLabel/keybindingLabel.nls", "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys"], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls": [], "vs/base/browser/ui/keybindingLabel/keybindingLabel.nls.keys": []}, "bundles": {"vs/editor/editor.main": ["vs/base/browser/dompurify/dompurify", "vs/base/browser/fastDomNode", "vs/base/browser/iframe", "vs/base/browser/ui/list/list", "vs/base/browser/ui/list/splice", "vs/base/browser/ui/scrollbar/scrollbarState", "vs/base/browser/ui/tree/tree", "vs/base/common/arrays", "vs/base/common/assert", "vs/base/common/buffer", "vs/base/common/cache", "vs/base/common/codicons", "vs/base/common/collections", "vs/base/common/color", "vs/base/common/decorators", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/idGenerator", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/keybindings", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/marked/marked", "vs/base/common/mime", "vs/base/browser/dnd", "vs/base/common/navigator", "vs/base/common/history", "vs/base/common/numbers", "vs/base/common/platform", "vs/base/common/process", "vs/base/common/path", "vs/base/common/range", "vs/base/browser/ui/list/rangeMap", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/browser/browser", "vs/base/browser/canIUse", "vs/base/browser/event", "vs/base/browser/keyboardEvent", "vs/base/browser/mouseEvent", "vs/base/common/cancellation", "vs/base/common/async", "vs/base/browser/ui/scrollbar/scrollbarVisibilityController", "vs/base/common/comparers", "vs/base/common/scrollable", "vs/base/common/strings", "vs/base/common/extpath", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/browser/ui/tree/indexTreeModel", "vs/base/browser/ui/tree/objectTreeModel", "vs/base/browser/ui/tree/compressedObjectTreeModel", "vs/base/common/map", "vs/base/common/filters", "vs/base/common/fuzzyScorer", "vs/base/common/glob", "vs/base/common/iconLabels", "vs/base/common/htmlContent", "vs/base/common/search", "vs/base/common/severity", "vs/base/common/types", "vs/base/common/objects", "vs/base/common/uint", "vs/base/common/uri", "vs/base/common/marshalling", "vs/base/common/network", "vs/base/browser/dom", "vs/base/browser/formattedTextRenderer", "vs/base/browser/globalMouseMoveMonitor", "vs/base/browser/touch", "vs/base/browser/ui/iconLabel/iconLabels", "vs/base/browser/ui/highlightedlabel/highlightedLabel", "vs/base/browser/ui/list/rowCache", "vs/base/browser/ui/widget", "vs/base/browser/ui/scrollbar/scrollbarArrow", "vs/base/browser/ui/scrollbar/abstractScrollbar", "vs/base/browser/ui/scrollbar/horizontalScrollbar", "vs/base/browser/ui/scrollbar/verticalScrollbar", "vs/base/common/resources", "vs/base/browser/markdownRenderer", "vs/base/common/labels", "vs/base/common/uuid", "vs/base/common/worker/simpleWorker", "vs/base/browser/defaultWorkerFactory", "vs/base/parts/quickinput/common/quickInput", "vs/base/parts/storage/common/storage", "vs/css!vs/base/browser/ui/actionbar/actionbar", "vs/css!vs/base/browser/ui/aria/aria", "vs/base/browser/ui/aria/aria", "vs/css!vs/base/browser/ui/button/button", "vs/base/browser/ui/button/button", "vs/css!vs/base/browser/ui/checkbox/checkbox", "vs/base/browser/ui/checkbox/checkbox", "vs/css!vs/base/browser/ui/codicons/codicon/codicon", "vs/css!vs/base/browser/ui/codicons/codicon/codicon-modifiers", "vs/base/browser/ui/codicons/codiconStyles", "vs/css!vs/base/browser/ui/contextview/contextview", "vs/base/browser/ui/contextview/contextview", "vs/css!vs/base/browser/ui/countBadge/countBadge", "vs/base/browser/ui/countBadge/countBadge", "vs/css!vs/base/browser/ui/dropdown/dropdown", "vs/css!vs/base/browser/ui/findinput/findInput", "vs/css!vs/base/browser/ui/hover/hover", "vs/css!vs/base/browser/ui/iconLabel/iconlabel", "vs/css!vs/base/browser/ui/inputbox/inputBox", "vs/css!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/css!vs/base/browser/ui/list/list", "vs/css!vs/base/browser/ui/mouseCursor/mouseCursor", "vs/base/browser/ui/mouseCursor/mouseCursor", "vs/css!vs/base/browser/ui/progressbar/progressbar", "vs/base/browser/ui/progressbar/progressbar", "vs/css!vs/base/browser/ui/sash/sash", "vs/base/browser/ui/sash/sash", "vs/css!vs/base/browser/ui/scrollbar/media/scrollbars", "vs/base/browser/ui/scrollbar/scrollableElement", "vs/base/browser/ui/hover/hoverWidget", "vs/base/browser/ui/list/listView", "vs/base/browser/ui/list/listWidget", "vs/base/browser/ui/list/listPaging", "vs/css!vs/base/browser/ui/splitview/splitview", "vs/base/browser/ui/splitview/splitview", "vs/css!vs/base/browser/ui/table/table", "vs/base/browser/ui/table/tableWidget", "vs/css!vs/base/browser/ui/tree/media/tree", "vs/css!vs/base/parts/quickinput/browser/media/quickInput", "vs/base/parts/quickinput/browser/quickInputUtils", "vs/css!vs/editor/browser/controller/textAreaHandler", "vs/css!vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/css!vs/editor/browser/viewParts/decorations/decorations", "vs/css!vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/css!vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/css!vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/css!vs/editor/browser/viewParts/lines/viewLines", "vs/css!vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/css!vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/css!vs/editor/browser/viewParts/minimap/minimap", "vs/css!vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/css!vs/editor/browser/viewParts/rulers/rulers", "vs/css!vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/css!vs/editor/browser/viewParts/selections/selections", "vs/css!vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/css!vs/editor/browser/widget/media/diffEditor", "vs/css!vs/editor/browser/widget/media/diffReview", "vs/css!vs/editor/browser/widget/media/editor", "vs/css!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/css!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/css!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/css!vs/editor/contrib/codelens/browser/codelensWidget", "vs/css!vs/editor/contrib/colorPicker/browser/colorPicker", "vs/css!vs/editor/contrib/dnd/browser/dnd", "vs/css!vs/editor/contrib/find/browser/findWidget", "vs/css!vs/editor/contrib/folding/browser/folding", "vs/css!vs/editor/contrib/gotoError/browser/media/gotoErrorWidget", "vs/css!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/css!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/css!vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/css!vs/editor/contrib/links/browser/links", "vs/css!vs/editor/contrib/message/browser/messageController", "vs/css!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/css!vs/editor/contrib/peekView/browser/media/peekViewWidget", "vs/css!vs/editor/contrib/rename/browser/renameInputField", "vs/css!vs/editor/contrib/snippet/browser/snippetSession", "vs/css!vs/editor/contrib/suggest/browser/media/suggest", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/css!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/css!vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/css!vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/css!vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/css!vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/css!vs/editor/standalone/browser/quickInput/standaloneQuickInput", "vs/css!vs/editor/standalone/browser/standalone-tokens", "vs/css!vs/platform/actions/browser/menuEntryActionViewItem", "vs/css!vs/platform/contextview/browser/contextMenuHandler", "vs/editor/browser/config/elementSizeObserver", "vs/editor/browser/config/migrateOptions", "vs/editor/browser/config/tabFocus", "vs/editor/browser/stableEditorScroll", "vs/editor/browser/view/renderingContext", "vs/editor/browser/view/viewUserInputEvents", "vs/editor/browser/viewParts/lines/rangeUtil", "vs/editor/browser/viewParts/minimap/minimapCharSheet", "vs/editor/browser/viewParts/minimap/minimapChar<PERSON><PERSON>er", "vs/editor/browser/viewParts/minimap/minimapPreBaked", "vs/editor/browser/viewParts/minimap/minimapCharRendererFactory", "vs/editor/common/config/editorZoom", "vs/editor/common/config/fontInfo", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/cursorColumns", "vs/editor/common/core/eolCounter", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/browser/controller/textAreaState", "vs/editor/browser/widget/diffNavigator", "vs/editor/common/core/editOperation", "vs/editor/common/commands/trimTrailingWhitespaceCommand", "vs/editor/common/core/rgba", "vs/editor/common/core/selection", "vs/editor/browser/controller/textAreaInput", "vs/editor/common/commands/replaceCommand", "vs/editor/common/commands/surroundSelectionCommand", "vs/editor/common/core/stringBuilder", "vs/editor/browser/view/viewLayer", "vs/editor/common/core/textChange", "vs/editor/common/core/textModelDefaults", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/cursor/cursorAtomicMoveOperations", "vs/editor/common/diff/diffComputer", "vs/editor/common/editorAction", "vs/editor/common/editor<PERSON><PERSON><PERSON>", "vs/editor/browser/editorBrowser", "vs/editor/common/languageSelector", "vs/editor/common/languages/languageConfiguration", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports", "vs/editor/common/languages/supports/characterPair", "vs/editor/common/languages/supports/indentRules", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/languages/supports/onEnter", "vs/editor/common/languages/supports/richEditBrackets", "vs/editor/common/languages/supports/electricCharacter", "vs/editor/common/languages/supports/tokenization", "vs/editor/common/model", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/length", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/beforeEditPositionMapper", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/nodeReader", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/smallImmutableSet", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/ast", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/concat23Trees", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/parser", "vs/editor/common/model/indentationG<PERSON>ser", "vs/editor/common/model/intervalTree", "vs/editor/common/model/pieceTreeTextBuffer/rbTreeBase", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelPart", "vs/editor/common/model/textModelSearch", "vs/editor/common/languages/unicodeTextModelHighlighter", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeBase", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBuffer", "vs/editor/common/model/pieceTreeTextBuffer/pieceTreeTextBufferBuilder", "vs/editor/common/model/utils", "vs/editor/common/services/languagesAssociations", "vs/editor/common/services/semanticTokensDto", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/textModelBracketPairs", "vs/editor/common/textModelEvents", "vs/editor/common/textModelGuides", "vs/editor/common/model/guidesTextModelPart", "vs/editor/common/tokenizationRegistry", "vs/editor/common/languages", "vs/editor/common/languages/nullMode", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/tokenizer", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/brackets", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsTree/bracketPairsTree", "vs/editor/common/model/bracketPairsTextModelPart/bracketPairsImpl", "vs/editor/common/model/bracketPairsTextModelPart/fixBrackets", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/editorSimpleWorker", "vs/editor/common/tokens/contiguousMultilineTokens", "vs/editor/common/tokens/contiguousMultilineTokensBuilder", "vs/editor/common/tokens/lineTokens", "vs/editor/common/languages/textToHtmlTokenizer", "vs/editor/common/model/textModelTokens", "vs/editor/common/tokens/contiguousTokensEditing", "vs/editor/common/tokens/contiguousTokensStore", "vs/editor/common/tokens/sparseMultilineTokens", "vs/editor/common/tokens/sparseTokensStore", "vs/editor/common/viewLayout/lineDecorations", "vs/editor/common/viewLayout/linesLayout", "vs/editor/common/viewLayout/viewLineRenderer", "vs/editor/common/viewLayout/viewLinesViewportData", "vs/editor/common/viewModel/minimapTokensColorTracker", "vs/editor/common/viewModel/modelLineProjectionData", "vs/editor/common/viewModel/monospaceLineBreaksComputer", "vs/editor/common/viewModel/overviewZoneManager", "vs/editor/common/viewModel/viewContext", "vs/editor/common/viewModel/viewEventHandler", "vs/editor/browser/view/dynamicViewOverlay", "vs/editor/browser/view/viewPart", "vs/editor/browser/viewParts/contentWidgets/contentWidgets", "vs/editor/browser/viewParts/decorations/decorations", "vs/editor/browser/viewParts/glyphMargin/glyphMargin", "vs/editor/browser/viewParts/linesDecorations/linesDecorations", "vs/editor/browser/viewParts/margin/margin", "vs/editor/browser/viewParts/marginDecorations/marginDecorations", "vs/editor/browser/viewParts/overlayWidgets/overlayWidgets", "vs/editor/browser/viewParts/overviewRuler/overviewRuler", "vs/editor/browser/viewParts/viewZones/viewZones", "vs/editor/common/viewModel/viewEvents", "vs/editor/common/viewModel/viewModel", "vs/editor/common/viewModel/modelLineProjection", "vs/editor/common/viewModel/viewModelEventDispatcher", "vs/editor/common/viewLayout/viewLayout", "vs/editor/contrib/caretOperations/browser/moveCaretCommand", "vs/editor/contrib/codeAction/browser/types", "vs/editor/contrib/colorPicker/browser/colorPickerModel", "vs/editor/contrib/comment/browser/blockCommentCommand", "vs/editor/contrib/dnd/browser/dragAndDropCommand", "vs/editor/contrib/find/browser/replaceAllCommand", "vs/editor/contrib/find/browser/replacePattern", "vs/editor/contrib/folding/browser/foldingRanges", "vs/editor/contrib/folding/browser/foldingModel", "vs/editor/contrib/folding/browser/hiddenRangeModel", "vs/editor/contrib/folding/browser/indentRangeProvider", "vs/editor/contrib/folding/browser/syntaxRangeProvider", "vs/editor/contrib/folding/browser/intializingRangeProvider", "vs/editor/contrib/format/browser/formattingEdit", "vs/editor/contrib/gotoSymbol/browser/link/clickLinkGesture", "vs/editor/contrib/hover/browser/hoverOperation", "vs/editor/contrib/hover/browser/hoverTypes", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplaceCommand", "vs/editor/contrib/indentation/browser/indentUtils", "vs/editor/contrib/inlayHints/browser/inlayHints", "vs/editor/contrib/inlineCompletions/browser/consts", "vs/editor/contrib/inlineCompletions/browser/ghostText", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionToGhostText", "vs/editor/contrib/inlineCompletions/browser/utils", "vs/editor/contrib/linesOperations/browser/copyLinesCommand", "vs/editor/contrib/linesOperations/browser/sortLinesCommand", "vs/editor/contrib/smartSelect/browser/bracketSelections", "vs/editor/contrib/smartSelect/browser/wordSelections", "vs/editor/contrib/snippet/browser/snippetParser", "vs/editor/contrib/suggest/browser/completionModel", "vs/editor/contrib/suggest/browser/resizable", "vs/editor/contrib/suggest/browser/suggestCommitCharacters", "vs/editor/contrib/suggest/browser/suggestOvertypingCapturer", "vs/editor/contrib/suggest/browser/wordDistance", "vs/editor/standalone/common/monarch/monarch<PERSON><PERSON>mon", "vs/editor/standalone/common/monarch/monarchCompile", "vs/editor/standalone/common/monarch/monarchLexer", "vs/editor/standalone/browser/colorizer", "vs/nls!vs/base/browser/ui/actionbar/actionViewItems", "vs/nls!vs/base/browser/ui/findinput/findInput", "vs/nls!vs/base/browser/ui/findinput/findInputCheckboxes", "vs/base/browser/ui/findinput/findInputCheckboxes", "vs/nls!vs/base/browser/ui/findinput/replaceInput", "vs/nls!vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/iconLabel/iconLabelHover", "vs/base/browser/ui/iconLabel/iconLabel", "vs/nls!vs/base/browser/ui/inputbox/inputBox", "vs/nls!vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/nls!vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/abstractTree", "vs/base/browser/ui/tree/dataTree", "vs/base/browser/ui/tree/objectTree", "vs/base/browser/ui/tree/asyncDataTree", "vs/nls!vs/base/common/actions", "vs/base/common/actions", "vs/base/browser/ui/actionbar/actionViewItems", "vs/base/browser/ui/actionbar/actionbar", "vs/base/browser/ui/dropdown/dropdown", "vs/base/browser/ui/dropdown/dropdownActionViewItem", "vs/base/browser/ui/inputbox/inputBox", "vs/base/browser/ui/findinput/findInput", "vs/base/browser/ui/findinput/replaceInput", "vs/base/browser/ui/menu/menu", "vs/base/parts/quickinput/browser/quickInputBox", "vs/nls!vs/base/common/errorMessage", "vs/base/common/errorMessage", "vs/nls!vs/base/common/keybindingLabels", "vs/base/common/keybindingLabels", "vs/base/browser/ui/keybindingLabel/keybindingLabel", "vs/nls!vs/base/parts/quickinput/browser/quickInput", "vs/nls!vs/base/parts/quickinput/browser/quickInputList", "vs/base/parts/quickinput/browser/quickInputList", "vs/base/parts/quickinput/browser/quickInput", "vs/nls!vs/editor/browser/controller/coreCommands", "vs/nls!vs/editor/browser/controller/textAreaHandler", "vs/nls!vs/editor/browser/editorExtensions", "vs/nls!vs/editor/browser/widget/codeEditorWidget", "vs/nls!vs/editor/browser/widget/diffEditorWidget", "vs/nls!vs/editor/browser/widget/diffReview", "vs/nls!vs/editor/browser/widget/inlineDiffMargin", "vs/editor/browser/widget/inlineDiffMargin", "vs/nls!vs/editor/common/config/editorConfigurationSchema", "vs/nls!vs/editor/common/config/editorOptions", "vs/editor/common/config/editorOptions", "vs/editor/browser/config/domFontInfo", "vs/editor/browser/config/charWidthReader", "vs/editor/browser/config/fontMeasurements", "vs/editor/browser/view/domLineBreaksComputer", "vs/editor/browser/view/viewOverlays", "vs/editor/browser/viewParts/viewCursors/viewCursor", "vs/editor/common/viewModel/viewModelDecorations", "vs/nls!vs/editor/common/core/editorColorRegistry", "vs/nls!vs/editor/common/editorContextKeys", "vs/nls!vs/editor/common/languages/modesRegistry", "vs/nls!vs/editor/common/model/editStack", "vs/editor/common/model/editStack", "vs/nls!vs/editor/common/standaloneStrings", "vs/editor/common/standaloneStrings", "vs/nls!vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/nls!vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/nls!vs/editor/contrib/caretOperations/browser/caretOperations", "vs/nls!vs/editor/contrib/caretOperations/browser/transpose", "vs/nls!vs/editor/contrib/clipboard/browser/clipboard", "vs/nls!vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/nls!vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/nls!vs/editor/contrib/codelens/browser/codelensController", "vs/nls!vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/nls!vs/editor/contrib/comment/browser/comment", "vs/nls!vs/editor/contrib/contextmenu/browser/contextmenu", "vs/nls!vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/nls!vs/editor/contrib/editorState/browser/keybindingCancellation", "vs/nls!vs/editor/contrib/find/browser/findController", "vs/nls!vs/editor/contrib/find/browser/findWidget", "vs/nls!vs/editor/contrib/folding/browser/folding", "vs/nls!vs/editor/contrib/folding/browser/foldingDecorations", "vs/nls!vs/editor/contrib/fontZoom/browser/fontZoom", "vs/nls!vs/editor/contrib/format/browser/format", "vs/nls!vs/editor/contrib/format/browser/formatActions", "vs/nls!vs/editor/contrib/gotoError/browser/gotoError", "vs/nls!vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/nls!vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/nls!vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/nls!vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/nls!vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/editor/contrib/gotoSymbol/browser/referencesModel", "vs/nls!vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/nls!vs/editor/contrib/hover/browser/hover", "vs/nls!vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/nls!vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/nls!vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/nls!vs/editor/contrib/indentation/browser/indentation", "vs/nls!vs/editor/contrib/inlineCompletions/browser/ghostTextController", "vs/nls!vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant", "vs/nls!vs/editor/contrib/lineSelection/browser/lineSelection", "vs/nls!vs/editor/contrib/linesOperations/browser/linesOperations", "vs/nls!vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/nls!vs/editor/contrib/links/browser/links", "vs/nls!vs/editor/contrib/message/browser/messageController", "vs/nls!vs/editor/contrib/multicursor/browser/multicursor", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHints", "vs/nls!vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/nls!vs/editor/contrib/peekView/browser/peekView", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/nls!vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/nls!vs/editor/contrib/rename/browser/rename", "vs/nls!vs/editor/contrib/rename/browser/renameInputField", "vs/nls!vs/editor/contrib/smartSelect/browser/smartSelect", "vs/nls!vs/editor/contrib/snippet/browser/snippetController2", "vs/nls!vs/editor/contrib/snippet/browser/snippetVariables", "vs/nls!vs/editor/contrib/suggest/browser/suggest", "vs/nls!vs/editor/contrib/suggest/browser/suggestController", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidget", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/nls!vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/nls!vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/nls!vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/nls!vs/editor/contrib/tokenization/browser/tokenization", "vs/nls!vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/nls!vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/nls!vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/nls!vs/editor/contrib/wordOperations/browser/wordOperations", "vs/nls!vs/platform/actions/browser/menuEntryActionViewItem", "vs/nls!vs/platform/configuration/common/configurationRegistry", "vs/nls!vs/platform/contextkey/browser/contextKeyService", "vs/nls!vs/platform/contextkey/common/contextkeys", "vs/nls!vs/platform/keybinding/common/abstractKeybindingService", "vs/nls!vs/platform/list/browser/listService", "vs/nls!vs/platform/markers/common/markers", "vs/nls!vs/platform/quickinput/browser/commandsQuickAccess", "vs/nls!vs/platform/quickinput/browser/helpQuickAccess", "vs/nls!vs/platform/theme/common/colorRegistry", "vs/nls!vs/platform/theme/common/iconRegistry", "vs/nls!vs/platform/undoRedo/common/undoRedoService", "vs/nls!vs/platform/workspaces/common/workspaces", "vs/platform/editor/common/editor", "vs/platform/extensions/common/extensions", "vs/platform/files/common/files", "vs/platform/history/browser/historyWidgetKeybindingHint", "vs/platform/instantiation/common/descriptors", "vs/platform/instantiation/common/extensions", "vs/platform/instantiation/common/graph", "vs/platform/instantiation/common/instantiation", "vs/editor/browser/services/bulkEditService", "vs/editor/browser/services/codeEditorService", "vs/editor/common/services/editorW<PERSON>ker", "vs/editor/common/services/language", "vs/editor/common/services/markerDecorations", "vs/editor/common/services/model", "vs/editor/common/services/resolverService", "vs/editor/common/services/textResourceConfiguration", "vs/editor/standalone/common/standaloneTheme", "vs/platform/clipboard/common/clipboardService", "vs/platform/commands/common/commands", "vs/editor/common/services/getSemanticTokens", "vs/editor/contrib/codelens/browser/codelens", "vs/editor/contrib/colorPicker/browser/color", "vs/editor/contrib/links/browser/getLinks", "vs/platform/configuration/common/configuration", "vs/editor/common/languages/languageConfigurationRegistry", "vs/editor/common/commands/shiftCommand", "vs/editor/contrib/comment/browser/lineCommentCommand", "vs/editor/contrib/linesOperations/browser/moveLinesCommand", "vs/platform/contextkey/common/contextkey", "vs/editor/common/editorContext<PERSON>eys", "vs/editor/contrib/parameterHints/browser/provideSignatureHelp", "vs/editor/contrib/parameterHints/browser/parameterHintsModel", "vs/editor/contrib/suggest/browser/suggestAlternatives", "vs/editor/contrib/suggest/browser/wordContextKey", "vs/platform/accessibility/common/accessibility", "vs/editor/browser/config/editorConfiguration", "vs/platform/accessibility/browser/accessibilityService", "vs/platform/contextkey/browser/contextKeyService", "vs/platform/contextkey/common/contextkeys", "vs/platform/contextview/browser/contextView", "vs/platform/dialogs/common/dialogs", "vs/platform/instantiation/common/serviceCollection", "vs/platform/instantiation/common/instantiationService", "vs/platform/keybinding/common/abstractKeybindingService", "vs/platform/keybinding/common/baseResolvedKeybinding", "vs/platform/keybinding/common/keybinding", "vs/platform/keybinding/common/keybindingResolver", "vs/platform/keybinding/common/resolvedKeybindingItem", "vs/platform/keybinding/common/usLayoutResolvedKeybinding", "vs/platform/label/common/label", "vs/platform/layout/browser/layoutService", "vs/editor/standalone/browser/standaloneLayoutService", "vs/platform/contextview/browser/contextViewService", "vs/platform/log/common/log", "vs/editor/browser/services/editorWorkerService", "vs/editor/browser/services/webWorker", "vs/editor/common/services/languageFeatureDebounce", "vs/editor/contrib/documentSymbols/browser/outlineModel", "vs/editor/contrib/documentSymbols/browser/documentSymbols", "vs/platform/clipboard/browser/clipboardService", "vs/platform/markers/common/markers", "vs/editor/contrib/gotoError/browser/markerNavigationService", "vs/platform/markers/common/markerService", "vs/platform/notification/common/notification", "vs/platform/opener/common/opener", "vs/editor/browser/services/openerService", "vs/platform/progress/common/progress", "vs/platform/quickinput/browser/pickerQuickAccess", "vs/platform/quickinput/common/quickInput", "vs/platform/registry/common/platform", "vs/platform/jsonschemas/common/jsonContributionRegistry", "vs/platform/configuration/common/configurationRegistry", "vs/editor/common/config/editorConfigurationSchema", "vs/editor/common/languages/modesRegistry", "vs/editor/common/services/getIconClasses", "vs/editor/common/services/languagesRegistry", "vs/editor/common/services/languageService", "vs/editor/contrib/markdownRenderer/browser/markdownRenderer", "vs/editor/contrib/hover/browser/marginHover", "vs/editor/contrib/suggest/browser/suggestWidgetDetails", "vs/platform/configuration/common/configurationModels", "vs/platform/keybinding/common/keybindingsRegistry", "vs/platform/history/browser/contextScopedHistoryWidget", "vs/platform/quickinput/common/quickAccess", "vs/platform/quickinput/browser/helpQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneHelpQuickAccess", "vs/platform/quickinput/browser/quickAccess", "vs/platform/storage/common/storage", "vs/editor/contrib/codelens/browser/codeLensCache", "vs/editor/contrib/suggest/browser/suggestMemory", "vs/platform/telemetry/common/telemetry", "vs/platform/quickinput/browser/commandsQuickAccess", "vs/editor/contrib/quickAccess/browser/commandsQuickAccess", "vs/platform/theme/common/colorRegistry", "vs/editor/browser/editorDom", "vs/platform/theme/common/styler", "vs/platform/contextview/browser/contextMenuHandler", "vs/platform/theme/common/theme", "vs/editor/browser/viewParts/lines/viewLine", "vs/editor/browser/controller/mouseTarget", "vs/editor/browser/controller/mouseHandler", "vs/editor/browser/controller/pointerHandler", "vs/editor/browser/viewParts/lines/viewLines", "vs/platform/theme/common/themeService", "vs/editor/browser/services/abstractCodeEditorService", "vs/editor/browser/viewParts/editorScrollbar/editorScrollbar", "vs/editor/browser/viewParts/minimap/minimap", "vs/editor/browser/viewParts/scrollDecoration/scrollDecoration", "vs/editor/browser/viewParts/selections/selections", "vs/editor/common/core/editorColorRegistry", "vs/editor/browser/viewParts/currentLineHighlight/currentLineHighlight", "vs/editor/browser/viewParts/indentGuides/indentGuides", "vs/editor/browser/viewParts/lineNumbers/lineNumbers", "vs/editor/browser/controller/text<PERSON><PERSON>Hand<PERSON>", "vs/editor/browser/viewParts/overviewRuler/decorationsOverviewRuler", "vs/editor/browser/viewParts/rulers/rulers", "vs/editor/browser/viewParts/viewCursors/viewCursors", "vs/editor/common/model/bracketPairsTextModelPart/colorizedBracketPairsDecorationProvider", "vs/editor/common/services/markerDecorationsService", "vs/editor/common/services/semanticTokensProviderStyling", "vs/editor/contrib/codeAction/browser/lightBulbWidget", "vs/editor/contrib/colorPicker/browser/colorPickerWidget", "vs/editor/contrib/gotoSymbol/browser/peek/referencesTree", "vs/editor/contrib/inlineCompletions/browser/ghostTextWidget", "vs/editor/contrib/quickAccess/browser/editorNavigationQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess", "vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess", "vs/editor/contrib/rename/browser/renameInputField", "vs/editor/contrib/symbolIcons/browser/symbolIcons", "vs/editor/standalone/browser/standaloneCodeEditorService", "vs/editor/standalone/common/themes", "vs/platform/actions/common/actions", "vs/editor/browser/editorExtensions", "vs/editor/browser/services/markerDecorations", "vs/editor/contrib/anchorSelect/browser/anchorSelect", "vs/editor/contrib/caretOperations/browser/caretOperations", "vs/editor/contrib/clipboard/browser/clipboard", "vs/editor/contrib/comment/browser/comment", "vs/editor/contrib/contextmenu/browser/contextmenu", "vs/editor/contrib/cursorUndo/browser/cursorUndo", "vs/editor/contrib/editorState/browser/keybindingCancellation", "vs/editor/contrib/editorState/browser/editorState", "vs/editor/contrib/codeAction/browser/codeAction", "vs/editor/contrib/codeAction/browser/codeActionMenu", "vs/editor/contrib/codeAction/browser/codeActionModel", "vs/editor/contrib/fontZoom/browser/fontZoom", "vs/editor/contrib/format/browser/format", "vs/editor/contrib/format/browser/formatActions", "vs/editor/contrib/gotoSymbol/browser/goToSymbol", "vs/editor/contrib/gotoSymbol/browser/symbolNavigation", "vs/editor/contrib/hover/browser/getHover", "vs/editor/contrib/hover/browser/markdownHoverParticipant", "vs/editor/contrib/message/browser/messageController", "vs/editor/contrib/codeAction/browser/codeActionUi", "vs/editor/contrib/codeAction/browser/codeActionCommands", "vs/editor/contrib/codeAction/browser/codeActionContributions", "vs/editor/contrib/rename/browser/rename", "vs/editor/contrib/smartSelect/browser/smartSelect", "vs/editor/contrib/suggest/browser/suggest", "vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode", "vs/editor/contrib/tokenization/browser/tokenization", "vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators", "vs/editor/standalone/browser/accessibilityHelp/accessibilityHelp", "vs/editor/standalone/browser/iPadShowKeyboard/iPadShowKeyboard", "vs/editor/standalone/browser/inspectTokens/inspectTokens", "vs/editor/standalone/browser/quickAccess/standaloneCommandsQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoLineQuickAccess", "vs/editor/standalone/browser/quickAccess/standaloneGotoSymbolQuickAccess", "vs/editor/standalone/browser/toggleHighContrast/toggleHighContrast", "vs/platform/actions/browser/menuEntryActionViewItem", "vs/editor/contrib/suggest/browser/suggestWidgetStatus", "vs/platform/actions/common/menuService", "vs/platform/contextview/browser/contextMenuService", "vs/platform/list/browser/listService", "vs/platform/opener/browser/link", "vs/platform/quickinput/browser/quickInput", "vs/editor/standalone/browser/quickInput/standaloneQuickInputService", "vs/platform/severityIcon/common/severityIcon", "vs/platform/theme/common/iconRegistry", "vs/editor/browser/widget/diffReview", "vs/editor/contrib/parameterHints/browser/parameterHintsWidget", "vs/editor/contrib/parameterHints/browser/parameterHints", "vs/editor/contrib/suggest/browser/suggestWidgetRenderer", "vs/editor/contrib/unicodeHighlighter/browser/bannerController", "vs/platform/theme/browser/iconsStyleSheet", "vs/editor/standalone/browser/standaloneThemeService", "vs/platform/undoRedo/common/undoRedo", "vs/editor/common/model/textModel", "vs/editor/common/cursor/cursorCommon", "vs/editor/common/cursor/cursorColumnSelection", "vs/editor/common/cursor/cursorMoveOperations", "vs/editor/common/cursor/cursorDeleteOperations", "vs/editor/common/cursor/cursorTypeOperations", "vs/editor/common/cursor/cursorWordOperations", "vs/editor/common/cursor/cursorMoveCommands", "vs/editor/browser/controller/coreCommands", "vs/editor/browser/view/viewController", "vs/editor/browser/view/view", "vs/editor/common/cursor/oneCursor", "vs/editor/common/cursor/cursorCollection", "vs/editor/common/cursor/cursor", "vs/editor/common/services/modelService", "vs/editor/common/viewModel/viewModelLines", "vs/editor/common/viewModel/viewModelImpl", "vs/editor/browser/widget/codeEditorWidget", "vs/editor/browser/widget/diffEditorWidget", "vs/editor/browser/widget/embeddedCodeEditorWidget", "vs/editor/contrib/bracketMatching/browser/bracketMatching", "vs/editor/contrib/caretOperations/browser/transpose", "vs/editor/contrib/codelens/browser/codelensWidget", "vs/editor/contrib/codelens/browser/codelensController", "vs/editor/contrib/colorPicker/browser/colorDetector", "vs/editor/contrib/dnd/browser/dnd", "vs/editor/contrib/find/browser/findDecorations", "vs/editor/contrib/find/browser/findModel", "vs/editor/contrib/find/browser/findOptionsWidget", "vs/editor/contrib/find/browser/findState", "vs/editor/contrib/find/browser/findWidget", "vs/editor/contrib/find/browser/findController", "vs/editor/contrib/folding/browser/foldingDecorations", "vs/editor/contrib/folding/browser/folding", "vs/editor/contrib/hover/browser/colorHoverParticipant", "vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace", "vs/editor/contrib/indentation/browser/indentation", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsModel", "vs/editor/contrib/lineSelection/browser/lineSelection", "vs/editor/contrib/linesOperations/browser/linesOperations", "vs/editor/contrib/linkedEditing/browser/linkedEditing", "vs/editor/contrib/links/browser/links", "vs/editor/contrib/multicursor/browser/multicursor", "vs/editor/contrib/suggest/browser/suggestWidget", "vs/editor/contrib/viewportSemanticTokens/browser/viewportSemanticTokens", "vs/editor/contrib/wordHighlighter/browser/wordHighlighter", "vs/editor/contrib/wordOperations/browser/wordOperations", "vs/editor/contrib/wordPartOperations/browser/wordPartOperations", "vs/editor/contrib/zoneWidget/browser/zoneWidget", "vs/editor/contrib/peekView/browser/peekView", "vs/editor/contrib/gotoError/browser/gotoErrorWidget", "vs/editor/contrib/gotoError/browser/gotoError", "vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget", "vs/editor/contrib/gotoSymbol/browser/peek/referencesController", "vs/editor/contrib/gotoSymbol/browser/goToCommands", "vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition", "vs/editor/contrib/hover/browser/markerHoverParticipant", "vs/editor/contrib/inlayHints/browser/inlayHintsLocations", "vs/editor/contrib/inlayHints/browser/inlayHintsController", "vs/editor/contrib/inlayHints/browser/inlayHintsHover", "vs/editor/standalone/browser/referenceSearch/standaloneReferenceSearch", "vs/platform/undoRedo/common/undoRedoService", "vs/platform/workspace/common/workspace", "vs/platform/workspace/common/workspaceTrust", "vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter", "vs/editor/standalone/browser/standaloneServices", "vs/editor/standalone/browser/standaloneCodeEditor", "vs/editor/standalone/browser/standaloneEditor", "vs/editor/standalone/browser/standaloneLanguages", "vs/editor/editor.api", "vs/platform/workspaces/common/workspaces", "vs/editor/contrib/snippet/browser/snippetVariables", "vs/editor/contrib/snippet/browser/snippetSession", "vs/editor/contrib/snippet/browser/snippetController2", "vs/editor/contrib/suggest/browser/suggestModel", "vs/editor/contrib/suggest/browser/suggestController", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetInlineCompletionProvider", "vs/editor/contrib/inlineCompletions/browser/suggestWidgetPreviewModel", "vs/editor/contrib/inlineCompletions/browser/ghostTextModel", "vs/editor/contrib/inlineCompletions/browser/ghostTextController", "vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant", "vs/editor/contrib/hover/browser/contentHover", "vs/editor/contrib/hover/browser/hover", "vs/editor/contrib/colorPicker/browser/colorContributions", "vs/editor/editor.all", "vs/editor/editor.main"], "vs/base/common/worker/simpleWorker": ["vs/base/common/arrays", "vs/base/common/cache", "vs/base/common/codicons", "vs/base/common/diff/diffChange", "vs/base/common/errors", "vs/base/common/functional", "vs/base/common/iterator", "vs/base/common/keyCodes", "vs/base/common/lazy", "vs/base/common/lifecycle", "vs/base/common/linkedList", "vs/base/common/platform", "vs/base/common/process", "vs/base/common/path", "vs/base/common/stopwatch", "vs/base/common/event", "vs/base/common/cancellation", "vs/base/common/async", "vs/base/common/strings", "vs/base/common/extpath", "vs/base/common/hash", "vs/base/common/diff/diff", "vs/base/common/map", "vs/base/common/glob", "vs/base/common/types", "vs/base/common/objects", "vs/base/common/uint", "vs/base/common/uri", "vs/base/common/worker/simpleWorker", "vs/editor/common/core/characterClassifier", "vs/editor/common/core/position", "vs/editor/common/core/range", "vs/editor/common/core/selection", "vs/editor/common/core/wordCharacterClassifier", "vs/editor/common/core/wordHelper", "vs/editor/common/diff/diffComputer", "vs/editor/common/languageSelector", "vs/editor/common/languages/linkComputer", "vs/editor/common/languages/supports/inplaceReplaceSupport", "vs/editor/common/model", "vs/editor/common/languageFeatureRegistry", "vs/editor/common/model/prefixSumComputer", "vs/editor/common/model/mirrorTextModel", "vs/editor/common/model/textModelSearch", "vs/editor/common/languages/unicodeTextModelHighlighter", "vs/editor/common/standalone/standaloneEnums", "vs/editor/common/tokenizationRegistry", "vs/editor/common/languages", "vs/editor/common/services/editorBaseApi", "vs/editor/common/services/editorSimpleWorker"]}}