# History
----

## 1.9.1 2018-05-10

- Fix invalid aria-expanded prop in preact

## 1.9.0 2018-04-02

- Add keyboard support [#84](https://github.com/react-component/collapse/pull/84)

## 1.8.0 2018-01-30

- Add prop forceRender to Panel [#82](https://github.com/react-component/collapse/pull/82)

## 1.7.6 2017-06-06

- Add prop id for Panel [#69](https://github.com/react-component/collapse/issues/69)

## 1.7.4 2017-05-16

- Add prop disabled [!71](https://github.com/react-component/collapse/pull/71)
- Add es module export [!70](https://github.com/react-component/collapse/pull/70)

## 1.7.2 2017-04-25

- Allow user to add custom header classe [!66](https://github.com/react-component/collapse/pull/66)

## 1.7.1 2017-04-19

- Add prop destroyInactivePanel [!61](https://github.com/react-component/collapse/pull/61)

## 1.7.0

- Change createClass to React.Component [!58](https://github.com/react-component/collapse/pull/58)

## 1.6.12

- Fix `style` support for Panel

## 1.6.11

- Add 'showArrow' prop to Panel to toggle arrow visibility [!48](https://github.com/react-component/collapse/pull/48)

## 1.6.10

- Child item support null [!45](https://github.com/react-component/collapse/pull/45)

## 1.6.6

- add className props to Panel

## 1.6.5

- fix missing rc-collapse-item-active classname on active panel header

## 1.6.0

- lazy render/controllable

## 1.5.0

- use css animation instead of velocity.js

## 1.4.0

- only support react 0.14+

## 1.2.0 2015-07-10

- 'chore' Change name to Collapse
- 'feat' Support Collapse and Accordion

## 1.1.0 2015-07-09

- `test` Add test
- `refactor` add Panel Api
