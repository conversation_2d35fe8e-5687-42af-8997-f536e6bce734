{"api": {"PerformanceObserver": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0", "notes": ["Stability: Experimental", "Exported from the <code>perf_hooks</code> module"], "partial_implementation": true}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "52"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "PerformanceObserver": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver/PerformanceObserver", "description": "<code>PerformanceObserver()</code> constructor", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": null}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "52"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "disconnect": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver/disconnect", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0", "notes": "Exported from the <code>perf_hooks</code> module", "partial_implementation": true}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "52"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "observe": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver/observe", "support": {"chrome": {"version_added": "52"}, "chrome_android": {"version_added": "52"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "57"}, "firefox_android": {"version_added": "57"}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0"}, "opera": {"version_added": "39"}, "opera_android": {"version_added": "41"}, "safari": {"version_added": "11"}, "safari_ios": {"version_added": "11"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "52"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "supportedEntryTypes": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver/supportedEntryTypes", "support": {"chrome": {"version_added": "73"}, "chrome_android": {"version_added": "73"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "nodejs": {"version_added": "8.5.0"}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": "12.1"}, "safari_ios": {"version_added": "12.2"}, "samsunginternet_android": {"version_added": "11.0"}, "webview_android": {"version_added": "73"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "takeRecords": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PerformanceObserver/takeRecords", "support": {"chrome": {"version_added": "65"}, "chrome_android": {"version_added": "65"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "60"}, "firefox_android": {"version_added": "60"}, "ie": {"version_added": false}, "nodejs": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0"}, "webview_android": {"version_added": "65"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "worker_support": {"__compat": {"description": "Available in workers", "support": {"chrome": {"version_added": "62"}, "chrome_android": {"version_added": "62"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": null}, "firefox_android": {"version_added": null}, "ie": {"version_added": false}, "nodejs": {"version_added": null}, "opera": {"version_added": "49"}, "opera_android": {"version_added": "46"}, "safari": {"version_added": null}, "safari_ios": {"version_added": null}, "samsunginternet_android": {"version_added": "8.0"}, "webview_android": {"version_added": "62"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}