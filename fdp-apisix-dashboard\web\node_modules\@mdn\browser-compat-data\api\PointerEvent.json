{"api": {"PointerEvent": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "prefix": "MS", "partial_implementation": true, "notes": "See <a href='https://msdn.microsoft.com/library/dn304886'>MSDN Pointer events updates</a>."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "PointerEvent": {"__compat": {"description": "<code>PointerEvent()</code> constructor", "mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/PointerEvent", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "prefix": "MS", "partial_implementation": true, "notes": "See <a href='https://msdn.microsoft.com/library/dn304886'>MSDN Pointer events updates</a>."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "getCoalescedEvents": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/getCoalescedEvents", "support": {"chrome": {"version_added": "58"}, "chrome_android": {"version_added": "58"}, "edge": {"version_added": "79"}, "firefox": {"version_added": "59"}, "firefox_android": {"version_added": "59", "partial_implementation": true, "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": "45"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "58"}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "height": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/height", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "partial_implementation": true, "notes": "Returns values in screen pixels instead of CSS document pixels."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "isPrimary": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/isPrimary", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": "10"}, "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "pointerId": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/pointerId", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": "10"}, "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "pointerType": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/pointerType", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "partial_implementation": true, "notes": "Returns an integer enumeration instead of a string."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "fractional_coordinates": {"__compat": {"description": "Fractional coordinates for <code>mouse</code>.", "support": {"chrome": {"version_added": "64", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, "chrome_android": {"version_added": "64", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, "edge": [{"version_added": "79", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, {"version_added": "12", "version_removed": "79", "partial_implementation": true, "notes": "Only <code>clientX</code>, <code>clientY</code>, <code>pageX</code> and <code>pageY</code> are fractional."}], "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": true, "partial_implementation": true, "notes": "Only <code>clientX</code>, <code>clientY</code>, <code>pageX</code> and <code>pageY</code> are fractional."}, "opera": {"version_added": "51", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, "opera_android": {"version_added": "47", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "9.0", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}, "webview_android": {"version_added": "64", "partial_implementation": true, "notes": "<code>movementX</code> and <code>movementY</code> are not fractional, see <a href='https://crbug.com/802067'>Chromium bug 802067</a>."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}, "pressure": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/pressure", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "partial_implementation": true, "notes": "Returns 0 instead of 0.5 on hardware that doesn't support pressure."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "tangentialPressure": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/tangentialPressure", "support": {"chrome": {"version_added": "58"}, "chrome_android": {"version_added": "58"}, "edge": {"version_added": "79"}, "firefox": [{"version_added": "59"}, {"version_added": "54", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "54", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": "45"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "58"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "tiltX": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/tiltX", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": "10"}, "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "tiltY": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/tiltY", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": "10"}, "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "twist": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/twist", "support": {"chrome": {"version_added": "58"}, "chrome_android": {"version_added": "58"}, "edge": {"version_added": "18"}, "firefox": [{"version_added": "59"}, {"version_added": "54", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "54", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": "45"}, "opera_android": {"version_added": "43"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "7.0"}, "webview_android": {"version_added": "58"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "width": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PointerEvent/width", "support": {"chrome": {"version_added": "55"}, "chrome_android": {"version_added": "55"}, "edge": {"version_added": "12"}, "firefox": [{"version_added": "59"}, {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}], "firefox_android": {"version_added": "41", "flags": [{"type": "preference", "name": "dom.w3c_pointer_events.enabled", "value_to_set": "true"}]}, "ie": [{"version_added": "11"}, {"version_added": "10", "partial_implementation": true, "notes": "Returns values in screen pixels instead of CSS document pixels."}], "opera": {"version_added": "42"}, "opera_android": {"version_added": "42"}, "safari": {"version_added": "13"}, "safari_ios": {"version_added": "13"}, "samsunginternet_android": {"version_added": "6.0"}, "webview_android": {"version_added": "55"}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}