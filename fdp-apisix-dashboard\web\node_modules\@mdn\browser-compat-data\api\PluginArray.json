{"api": {"PluginArray": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PluginArray", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}, "item": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PluginArray/item", "support": {"chrome": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "chrome_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true, "notes": "Starting with Samsung Internet 7.0, method parameters are required instead of optional."}, "webview_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "length": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PluginArray/length", "support": {"chrome": {"version_added": true}, "chrome_android": {"version_added": true}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true}, "webview_android": {"version_added": true}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "namedItem": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PluginArray/namedItem", "support": {"chrome": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "chrome_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true, "notes": "Starting with Samsung Internet 7.0, method parameters are required instead of optional."}, "webview_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}, "refresh": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PluginArray/refresh", "support": {"chrome": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "chrome_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}, "edge": {"version_added": "12"}, "firefox": {"version_added": true}, "firefox_android": {"version_added": true}, "ie": {"version_added": null}, "opera": {"version_added": true}, "opera_android": {"version_added": false}, "safari": {"version_added": true}, "safari_ios": {"version_added": true}, "samsunginternet_android": {"version_added": true, "notes": "Starting with Samsung Internet 7.0, method parameters are required instead of optional."}, "webview_android": {"version_added": true, "notes": "Starting with version 59, method parameters are required instead of optional."}}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}