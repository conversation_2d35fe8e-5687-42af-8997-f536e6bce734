{"name": "rc-collapse", "version": "1.9.3", "description": "rc-collapse ui component for react", "keywords": ["react", "react-component", "react-rc-collapse", "rc-collapse", "collapse", "accordion"], "homepage": "http://github.com/react-component/collapse", "author": "<EMAIL>", "repository": {"type": "git", "url": "**************:react-component/collapse.git"}, "bugs": {"url": "http://github.com/react-component/collapse/issues"}, "license": "MIT", "files": ["lib", "es", "assets/*.css"], "main": "./lib/index", "module": "./es/index", "config": {"port": 8002}, "scripts": {"build": "rc-tools run build", "gh-pages": "rc-tools run gh-pages", "start": "rc-tools run server", "compile": "rc-tools run compile --babel-runtime", "pub": "rc-tools run pub", "lint": "rc-tools run lint", "test": "jest", "coverage": "jest --coverage && cat ./coverage/lcov.info | coveralls"}, "devDependencies": {"coveralls": "^2.13.1", "expect.js": "0.3.x", "jest": "^20.0.1", "jquery": "^3.3.1", "pre-commit": "1.x", "rc-tools": "6.x", "react": "^16.0.0", "react-dom": "^16.0.0", "string.prototype.repeat": "^0.2.0"}, "pre-commit": ["lint"], "jest": {"setupFiles": ["./tests/setup.js"], "collectCoverageFrom": ["src/*"], "transform": {"\\.tsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js", "\\.jsx?$": "./node_modules/rc-tools/scripts/jestPreprocessor.js"}}, "dependencies": {"classnames": "2.x", "css-animation": "1.x", "prop-types": "^15.5.6", "rc-animate": "2.x"}}