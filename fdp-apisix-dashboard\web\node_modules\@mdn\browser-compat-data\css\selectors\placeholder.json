{"css": {"selectors": {"placeholder": {"__compat": {"description": "<code>::placeholder</code>", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/::placeholder", "support": {"chrome": [{"version_added": "57"}, {"prefix": "-webkit-input-", "version_added": "6"}], "chrome_android": [{"version_added": "57"}, {"prefix": "-webkit-input-", "version_added": "18"}], "edge": [{"prefix": "-webkit-input-", "version_added": "12"}, {"prefix": "-ms-input-", "version_added": "12"}], "firefox": [{"version_added": "51"}, {"prefix": "-moz-", "version_added": "19"}], "firefox_android": [{"version_added": "51"}, {"prefix": "-moz-", "version_added": "19"}], "ie": {"version_added": false}, "opera": [{"version_added": "44"}, {"prefix": "-webkit-input-", "version_added": "15"}], "opera_android": [{"version_added": "43"}, {"prefix": "-webkit-input-", "version_added": "14"}], "safari": [{"version_added": "10.1"}, {"prefix": "-webkit-input-", "version_added": "5"}], "safari_ios": [{"version_added": "10.3"}, {"prefix": "-webkit-input-", "version_added": "4.3"}], "samsunginternet_android": [{"version_added": "7.0"}, {"prefix": "-webkit-input-", "version_added": "1.0"}], "webview_android": [{"version_added": "57"}, {"prefix": "-webkit-input-", "version_added": "2"}]}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}