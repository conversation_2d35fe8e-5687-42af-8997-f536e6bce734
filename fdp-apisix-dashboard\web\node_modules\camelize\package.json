{"name": "camelize", "version": "1.0.0", "description": "recursively transform key strings to camel-case", "main": "index.js", "devDependencies": {"tape": "~2.3.2"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/camelize.git"}, "homepage": "https://github.com/substack/camelize", "keywords": ["camel-case", "json", "transform"], "testling": {"files": "test/*.js", "browsers": {"iexplore": ["6.0", "7.0", "8.0", "9.0"], "chrome": ["20.0"], "firefox": ["10.0", "15.0"], "safari": ["5.1"], "opera": ["12.0"]}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT"}