{"api": {"PresentationConnectionCloseEvent": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnectionCloseEvent", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}, "PresentationConnectionCloseEvent": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnectionCloseEvent/PresentationConnectionCloseEvent", "description": "<code>PresentationConnectionCloseEvent()</code> constructor", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": null}, "opera_android": {"version_added": null}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "message": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnectionCloseEvent/message", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}, "reason": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PresentationConnectionCloseEvent/reason", "support": {"chrome": {"version_added": "50"}, "chrome_android": {"version_added": "50"}, "edge": {"version_added": "≤79"}, "firefox": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "firefox_android": {"version_added": "51", "flags": [{"type": "preference", "name": "dom.presentation.enabled", "value_to_set": "true"}]}, "ie": {"version_added": false}, "opera": {"version_added": true}, "opera_android": {"version_added": true}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "5.0"}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": true, "deprecated": false}}}}}}