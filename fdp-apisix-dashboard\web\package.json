{"name": "apisix-dashboard", "version": "3.0.1", "private": true, "description": "Dashboard for Apache APISIX", "scripts": {"prepare": "cd .. && husky install web/.husky", "analyze": "cross-env ANALYZE=1 yarn run build", "copy-folder": "node --experimental-modules copy-folder.mjs", "build": "yarn copy-folder monaco-editor ./public/ && NODE_OPTIONS=--max_old_space_size=4096 umi build", "dev": "yarn run start:dev", "fetch:blocks": "pro fetch-blocks --branch antd@4 && yarn run prettier", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && yarn run lint:js && yarn run lint:style && yarn run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && yarn run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\"", "prettier": "prettier -c --write \"**/*\"", "site": "yarn run fetch:blocks && yarn run build", "start": "yarn copy-folder monaco-editor ./public/ && umi dev", "start:e2e": "cross-env SERVE_ENV=test yarn run start", "test:e2e": "start-server-and-test 'cross-env SERVE_ENV=test yarn start' http-get://localhost:8000 cypress:run-ci", "test-plugin:e2e": "start-server-and-test 'cross-env SERVE_ENV=test yarn start' http-get://localhost:8000 cypress:run-plugin-ci", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none yarn run start", "start:no-mock": "cross-env MOCK=none yarn run start", "start:no-ui": "cross-env UMI_UI=none yarn run start", "start:pre": "cross-env REACT_APP_ENV=pre yarn run start", "start:test": "cross-env REACT_APP_ENV=test MOCK=none yarn run start", "test:component": "umi test ./src/components", "tsc": "tsc", "cypress:open": "cross-env CYPRESS_SERVE_ENV=test cypress open", "cypress:open-dev": "cross-env CYPRESS_SERVE_ENV=dev cypress open", "cypress:run-ci": "cross-env CYPRESS_SERVE_ENV=test cypress run", "cypress:run-plugin-ci": "cross-env CYPRESS_SERVE_ENV=test cypress run --spec 'cypress/e2e/plugin/*.cy.js'"}, "license": "Apache-2.0", "lint-staged": {"**/*.less": "stylelint", "**/*.{js,jsx,ts,tsx}": "yarn run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.0.0", "@ant-design/pro-layout": "^6.0.0", "@ant-design/pro-table": "2.30.8", "@antv/x6": "^1.18.5", "@antv/x6-react-components": "^1.1.7", "@monaco-editor/react": "^4.3.1", "@rjsf/antd": "2.2.0", "@rjsf/core": "2.2.0", "@types/js-yaml": "^4.0.0", "ahooks": "^3.7.1", "ajv": "^7.0.3", "ajv-formats": "^1.5.1", "antd": "^4.4.0", "base-64": "^1.0.0", "classnames": "^2.2.6", "dayjs": "1.8.28", "dotenv": "^16.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-simple-import-sort": "^8.0.0", "file-saver": "^2.0.5", "js-beautify": "^1.13.0", "js-yaml": "^4.0.0", "lodash": "^4.17.11", "moment": "^2.29.2", "nzh": "1.0.4", "omit.js": "^2.0.2", "path-to-regexp": "2.4.0", "qs": "^6.9.0", "query-string": "^6.13.7", "react": "^16.8.6", "react-copy-to-clipboard": "^5.0.3", "react-device-detect": "^1.12.1", "react-dom": "^16.8.6", "react-helmet-async": "^1.0.4", "start-server-and-test": "^1.11.5", "styled-components": "^5.2.1", "umi": "^3.1.2", "umi-request": "^1.0.8", "use-merge-value": "^1.0.1", "uuid": "7.0.3", "yaml": "^1.10.0"}, "devDependencies": {"@4tw/cypress-drag-drop": "^1.6.0", "@ant-design/pro-cli": "^2.0.2", "@cypress/code-coverage": "^3.10.0", "@types/base-64": "^0.1.3", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/file-saver": "^2.0.1", "@types/formdata": "^0.10.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/js-beautify": "^1.13.1", "@types/js-yaml": "^4.0.0", "@types/lodash": "^4.14.144", "@types/node-forge": "0.9.3", "@types/qs": "^6.5.3", "@types/react": "^16.9.17", "@types/react-copy-to-clipboard": "^5.0.0", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.13", "@types/styled-components": "^5.1.7", "@types/uuid": "7.0.4", "@umijs/fabric": "^2.2.0", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.0-beta.2", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-react": "^1.7.11", "@umijs/preset-ui": "^2.1.11", "babel-plugin-istanbul": "^6.0.0", "carlo": "^0.9.46", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "cypress": "^10.4.0", "cypress-file-upload": "^5.0.2", "cypress-localstorage-commands": "^2.2.0", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "eslint-plugin-eslint-comments": "^3.2.0", "express": "^4.17.1", "hexo-fs": "^5.0.0", "husky": "^6.0.0", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "monaco-editor": "^0.32.1", "pinst": "^2.1.1", "postcss": "^8.4.16", "postcss-less": "^6.0.0", "prettier": "^2.0.1", "pro-download": "1.0.1", "puppeteer-core": "^4.0.1", "stylelint": "^14.10.0", "stylelint-config-css-modules": "^4.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard-scss": "^5.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.5.0", "stylelint-order": "^5.0.0"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}