{"name": "https-proxy-agent", "version": "2.2.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTPS", "main": "./index.js", "types": "./index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-https-proxy-agent.git"}, "keywords": ["https", "proxy", "endpoint", "agent"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-https-proxy-agent/issues"}, "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "devDependencies": {"mocha": "^6.2.0", "proxy": "1"}, "engines": {"node": ">= 4.5.0"}}