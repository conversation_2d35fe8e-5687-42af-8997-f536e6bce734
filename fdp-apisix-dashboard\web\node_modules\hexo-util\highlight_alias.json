{"languages": ["1c", "abnf", "accesslog", "actionscript", "ada", "angelscript", "apache", "applescript", "arcade", "a<PERSON><PERSON><PERSON>", "armasm", "xml", "asciidoc", "<PERSON>j", "autohotkey", "autoit", "av<PERSON><PERSON>", "awk", "axapta", "bash", "basic", "bnf", "brainfuck", "c", "cal", "capnproto", "ceylon", "clean", "clojure", "clojure-repl", "cmake", "coffeescript", "coq", "cos", "cpp", "crmsh", "crystal", "csharp", "csp", "css", "d", "markdown", "dart", "delphi", "diff", "django", "dns", "dockerfile", "dos", "dsconfig", "dts", "dust", "ebnf", "elixir", "elm", "ruby", "erb", "erlang-repl", "erlang", "excel", "fix", "flix", "fortran", "fsharp", "gams", "gauss", "gcode", "g<PERSON>kin", "glsl", "gml", "go", "golo", "gradle", "groovy", "haml", "handlebars", "haskell", "haxe", "hsp", "http", "hy", "inform7", "ini", "irpf90", "isbl", "java", "javascript", "jboss-cli", "json", "julia", "julia-repl", "kotlin", "lasso", "latex", "ldif", "leaf", "less", "lisp", "livecodeserver", "livescript", "llvm", "lsl", "lua", "makefile", "mathematica", "matlab", "maxima", "mel", "mercury", "mipsasm", "mizar", "perl", "mojolicious", "monkey", "moonscript", "n1ql", "nestedtext", "nginx", "nim", "nix", "node-repl", "nsis", "objectivec", "ocaml", "openscad", "oxygene", "parser3", "pf", "pgsql", "php", "php-template", "plaintext", "pony", "powershell", "processing", "profile", "prolog", "properties", "protobuf", "puppet", "purebasic", "python", "python-repl", "q", "qml", "r", "reasonml", "rib", "roboconf", "routeros", "rsl", "ruleslanguage", "rust", "sas", "scala", "scheme", "scilab", "scss", "shell", "smali", "smalltalk", "sml", "sqf", "sql", "stan", "stata", "step21", "stylus", "subunit", "swift", "taggerscript", "yaml", "tap", "tcl", "thrift", "tp", "twig", "typescript", "vala", "vbnet", "vbscript", "vbscript-html", "verilog", "vhdl", "vim", "wasm", "wren", "x86asm", "xl", "xquery", "zephir"], "aliases": {"1c": "1c", "abnf": "abnf", "accesslog": "accesslog", "actionscript": "actionscript", "as": "actionscript", "ada": "ada", "angelscript": "angelscript", "asc": "angelscript", "apache": "apache", "apacheconf": "apache", "applescript": "applescript", "osascript": "applescript", "arcade": "arcade", "arduino": "a<PERSON><PERSON><PERSON>", "ino": "a<PERSON><PERSON><PERSON>", "armasm": "armasm", "arm": "armasm", "xml": "xml", "html": "xml", "xhtml": "xml", "rss": "xml", "atom": "xml", "xjb": "xml", "xsd": "xml", "xsl": "xml", "plist": "xml", "wsf": "xml", "svg": "xml", "asciidoc": "asciidoc", "adoc": "asciidoc", "aspectj": "<PERSON>j", "autohotkey": "autohotkey", "ahk": "autohotkey", "autoit": "autoit", "avrasm": "av<PERSON><PERSON>", "awk": "awk", "axapta": "axapta", "x++": "axapta", "bash": "bash", "sh": "bash", "basic": "basic", "bnf": "bnf", "brainfuck": "brainfuck", "bf": "brainfuck", "c": "c", "h": "c", "cal": "cal", "capnproto": "capnproto", "capnp": "capnproto", "ceylon": "ceylon", "clean": "clean", "icl": "clean", "dcl": "clean", "clojure": "clojure", "clj": "clojure", "edn": "clojure", "clojure-repl": "clojure-repl", "cmake": "cmake", "cmake.in": "cmake", "coffeescript": "coffeescript", "coffee": "coffeescript", "cson": "coffeescript", "iced": "coffeescript", "coq": "coq", "cos": "cos", "cls": "cos", "cpp": "cpp", "cc": "cpp", "c++": "cpp", "h++": "cpp", "hpp": "cpp", "hh": "cpp", "hxx": "cpp", "cxx": "cpp", "crmsh": "crmsh", "crm": "crmsh", "pcmk": "crmsh", "crystal": "crystal", "cr": "crystal", "csharp": "csharp", "cs": "csharp", "c#": "csharp", "csp": "csp", "css": "css", "d": "d", "markdown": "markdown", "md": "markdown", "mkdown": "markdown", "mkd": "markdown", "dart": "dart", "delphi": "delphi", "dpr": "delphi", "dfm": "delphi", "pas": "delphi", "pascal": "delphi", "diff": "diff", "patch": "diff", "django": "django", "jinja": "django", "dns": "dns", "bind": "dns", "zone": "dns", "dockerfile": "dockerfile", "docker": "dockerfile", "dos": "dos", "bat": "dos", "cmd": "dos", "dsconfig": "dsconfig", "dts": "dts", "dust": "dust", "dst": "dust", "ebnf": "ebnf", "elixir": "elixir", "ex": "elixir", "exs": "elixir", "elm": "elm", "ruby": "ruby", "rb": "ruby", "gemspec": "ruby", "podspec": "ruby", "thor": "ruby", "irb": "ruby", "erb": "erb", "erlang-repl": "erlang-repl", "erlang": "erlang", "erl": "erlang", "excel": "excel", "xlsx": "excel", "xls": "excel", "fix": "fix", "flix": "flix", "fortran": "fortran", "f90": "fortran", "f95": "fortran", "fsharp": "fsharp", "fs": "fsharp", "f#": "fsharp", "gams": "gams", "gms": "gams", "gauss": "gauss", "gss": "gauss", "gcode": "gcode", "nc": "gcode", "gherkin": "g<PERSON>kin", "feature": "g<PERSON>kin", "glsl": "glsl", "gml": "gml", "go": "go", "golang": "go", "golo": "golo", "gradle": "gradle", "groovy": "groovy", "haml": "haml", "handlebars": "handlebars", "hbs": "handlebars", "html.hbs": "handlebars", "html.handlebars": "handlebars", "htmlbars": "handlebars", "haskell": "haskell", "hs": "haskell", "haxe": "haxe", "hx": "haxe", "hsp": "hsp", "http": "http", "https": "http", "hy": "hy", "hylang": "hy", "inform7": "inform7", "i7": "inform7", "ini": "ini", "toml": "ini", "irpf90": "irpf90", "isbl": "isbl", "java": "java", "jsp": "java", "javascript": "javascript", "js": "javascript", "jsx": "javascript", "mjs": "javascript", "cjs": "javascript", "jboss-cli": "jboss-cli", "wildfly-cli": "jboss-cli", "json": "json", "julia": "julia", "julia-repl": "julia-repl", "jldoctest": "julia-repl", "kotlin": "kotlin", "kt": "kotlin", "kts": "kotlin", "lasso": "lasso", "ls": "livescript", "lassoscript": "lasso", "latex": "latex", "tex": "latex", "ldif": "ldif", "leaf": "leaf", "less": "less", "lisp": "lisp", "livecodeserver": "livecodeserver", "livescript": "livescript", "llvm": "llvm", "lsl": "lsl", "lua": "lua", "makefile": "makefile", "mk": "makefile", "mak": "makefile", "make": "makefile", "mathematica": "mathematica", "mma": "mathematica", "wl": "mathematica", "matlab": "matlab", "maxima": "maxima", "mel": "mel", "mercury": "mercury", "m": "mercury", "moo": "mercury", "mipsasm": "mipsasm", "mips": "mipsasm", "mizar": "mizar", "perl": "perl", "pl": "perl", "pm": "perl", "mojolicious": "mojolicious", "monkey": "monkey", "moonscript": "moonscript", "moon": "moonscript", "n1ql": "n1ql", "nestedtext": "nestedtext", "nt": "nestedtext", "nginx": "nginx", "nginxconf": "nginx", "nim": "nim", "nix": "nix", "nixos": "nix", "node-repl": "node-repl", "nsis": "nsis", "objectivec": "objectivec", "mm": "objectivec", "objc": "objectivec", "obj-c": "objectivec", "obj-c++": "objectivec", "objective-c++": "objectivec", "ocaml": "ocaml", "ml": "sml", "openscad": "openscad", "scad": "openscad", "oxygene": "oxygene", "parser3": "parser3", "pf": "pf", "pf.conf": "pf", "pgsql": "pgsql", "postgres": "pgsql", "postgresql": "pgsql", "php": "php", "php-template": "php-template", "plaintext": "plaintext", "text": "plaintext", "txt": "plaintext", "pony": "pony", "powershell": "powershell", "pwsh": "powershell", "ps": "powershell", "ps1": "powershell", "processing": "processing", "pde": "processing", "profile": "profile", "prolog": "prolog", "properties": "properties", "protobuf": "protobuf", "puppet": "puppet", "pp": "puppet", "purebasic": "purebasic", "pb": "purebasic", "pbi": "purebasic", "python": "python", "py": "python", "gyp": "python", "ipython": "python", "python-repl": "python-repl", "pycon": "python-repl", "q": "q", "k": "q", "kdb": "q", "qml": "qml", "qt": "qml", "r": "r", "reasonml": "reasonml", "re": "reasonml", "rib": "rib", "roboconf": "roboconf", "graph": "roboconf", "instances": "roboconf", "routeros": "routeros", "mikrotik": "routeros", "rsl": "rsl", "ruleslanguage": "ruleslanguage", "rust": "rust", "rs": "rust", "sas": "sas", "scala": "scala", "scheme": "scheme", "scilab": "scilab", "sci": "scilab", "scss": "scss", "shell": "shell", "console": "shell", "shellsession": "shell", "smali": "smali", "smalltalk": "smalltalk", "st": "smalltalk", "sml": "sml", "sqf": "sqf", "sql": "sql", "stan": "stan", "stanfuncs": "stan", "stata": "stata", "do": "stata", "ado": "stata", "step21": "step21", "p21": "step21", "step": "step21", "stp": "step21", "stylus": "stylus", "styl": "stylus", "subunit": "subunit", "swift": "swift", "taggerscript": "taggerscript", "yaml": "yaml", "yml": "yaml", "tap": "tap", "tcl": "tcl", "tk": "tcl", "thrift": "thrift", "tp": "tp", "twig": "twig", "craftcms": "twig", "typescript": "typescript", "ts": "typescript", "tsx": "typescript", "vala": "vala", "vbnet": "vbnet", "vb": "vbnet", "vbscript": "vbscript", "vbs": "vbscript", "vbscript-html": "vbscript-html", "verilog": "verilog", "v": "verilog", "sv": "verilog", "svh": "verilog", "vhdl": "vhdl", "vim": "vim", "wasm": "wasm", "wren": "wren", "x86asm": "x86asm", "xl": "xl", "tao": "xl", "xquery": "xquery", "xpath": "xquery", "xq": "xquery", "zephir": "zephir", "zep": "zephir"}}