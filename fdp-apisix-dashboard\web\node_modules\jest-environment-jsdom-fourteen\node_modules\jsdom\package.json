{"name": "jsdom", "version": "14.1.0", "description": "A JavaScript implementation of many web standards", "keywords": ["dom", "html", "whatwg", "w3c"], "maintainers": ["<PERSON> <<EMAIL>> (http://tmpvar.com)", "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "<PERSON> <<EMAIL>> (https://blog.smayr.name/)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://timothygu.me/)", "Zirro <<EMAIL>>"], "license": "MIT", "repository": "jsdom/jsdom", "dependencies": {"abab": "^2.0.0", "acorn": "^6.0.4", "acorn-globals": "^4.3.0", "array-equal": "^1.0.0", "cssom": "^0.3.4", "cssstyle": "^1.1.1", "data-urls": "^1.1.0", "domexception": "^1.0.1", "escodegen": "^1.11.0", "html-encoding-sniffer": "^1.0.2", "nwsapi": "^2.1.3", "parse5": "5.1.0", "pn": "^1.1.0", "request": "^2.88.0", "request-promise-native": "^1.0.5", "saxes": "^3.1.9", "symbol-tree": "^3.2.2", "tough-cookie": "^2.5.0", "w3c-hr-time": "^1.0.1", "w3c-xmlserializer": "^1.1.2", "webidl-conversions": "^4.0.2", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^7.0.0", "ws": "^6.1.2", "xml-name-validator": "^3.0.0"}, "_dependenciesComments": {"parse5": "Pinned to exact version number because we monkeypatch its internals (see htmltodom.js)"}, "devDependencies": {"benchmark": "1.0.0", "browserify": "^16.2.3", "chai": "^4.2.0", "eslint": "^4.19.1", "eslint-find-rules": "^3.3.1", "eslint-plugin-html": "^5.0.0", "eslint-plugin-jsdom-internal": "link:./scripts/eslint-plugin", "js-yaml": "^3.12.0", "karma": "^1.7.1", "karma-browserify": "^5.3.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-webworker": "^1.3.0", "karma-sauce-launcher": "^1.2.0", "minimatch": "^3.0.4", "mocha": "^3.5.2", "mocha-sugar-free": "^1.4.0", "optimist": "0.6.1", "portfinder": "^1.0.20", "q": "^1.5.1", "rimraf": "^2.6.2", "server-destroy": "^1.0.1", "st": "^1.2.2", "watchify": "^3.11.0", "wd": "^1.11.1", "webidl2js": "^9.2.1"}, "browser": {"canvas": false, "vm": "./lib/jsdom/vm-shim.js", "./lib/jsdom/living/websockets/WebSocket-impl.js": "./lib/jsdom/living/websockets/WebSocket-impl-browser.js"}, "scripts": {"prepare": "yarn convert-idl", "pretest": "yarn convert-idl && yarn init-wpt", "test-wpt": "mocha test/web-platform-tests/run-wpts.js", "test-tuwpt": "mocha test/web-platform-tests/run-tuwpts.js", "test-mocha": "mocha", "test-api": "mocha test/api", "test": "mocha test/index.js", "test-browser-iframe": "karma start test/karma.conf.js", "test-browser-worker": "karma start test/karma-webworker.conf.js", "test-browser": "yarn test-browser-iframe && yarn test-browser-worker", "lint": "eslint . --cache --ext .js,.html", "lint-is-complete": "eslint-find-rules --unused .eslintrc.json", "init-wpt": "git submodule update --init --recursive", "reset-wpt": "rimraf ./test/web-platform-tests/tests && yarn init-wpt", "update-wpt": "git submodule update --recursive --remote && cd test/web-platform-tests/tests && python wpt.py manifest --path ../wpt-manifest.json", "update-authors": "git log --format=\"%aN <%aE>\" | sort -f | uniq > AUTHORS.txt", "benchmark": "node ./benchmark/runner", "benchmark-browser": "node ./benchmark/runner --bundle", "convert-idl": "node ./scripts/webidl/convert"}, "main": "./lib/api.js", "engines": {"node": ">=8"}}