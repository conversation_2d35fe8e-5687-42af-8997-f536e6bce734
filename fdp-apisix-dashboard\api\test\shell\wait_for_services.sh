#!/usr/bin/env bash

#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

i=1
timeout=60
while ! curl -s 127.0.0.1:12800 >/dev/null; do
  if [[ "$i" -gt "$timeout" ]]; then
    echo "timeout occurred after waiting $timeout seconds"
    exit 1
  fi
  sleep 1
  echo "waited skywalking for $i seconds.."
  ((i++));
done
