{"name": "os-homedir", "version": "1.0.2", "description": "Node.js 4 `os.homedir()` ponyfill", "license": "MIT", "repository": "sindresorhus/os-homedir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["builtin", "core", "ponyfill", "polyfill", "shim", "os", "homedir", "home", "dir", "directory", "folder", "user", "path"], "devDependencies": {"ava": "*", "path-exists": "^2.0.0", "xo": "^0.16.0"}}