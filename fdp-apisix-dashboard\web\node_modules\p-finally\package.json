{"name": "p-finally", "version": "1.0.0", "description": "`Promise#finally()` ponyfill - Invoked when the promise is settled regardless of outcome", "license": "MIT", "repository": "sindresorhus/p-finally", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "finally", "handler", "function", "async", "await", "promises", "settled", "ponyfill", "polyfill", "shim", "bluebird"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}}