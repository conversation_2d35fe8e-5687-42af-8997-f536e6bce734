{"api": {"PeriodicSyncManager": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicSyncManager", "support": {"chrome": {"version_added": "80"}, "chrome_android": {"version_added": "80"}, "edge": {"version_added": "80"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": "67"}, "opera_android": {"version_added": "57"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "13.0"}, "webview_android": {"version_added": "80"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}, "getTags": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicSyncManager/getTags", "support": {"chrome": {"version_added": "80"}, "chrome_android": {"version_added": "80"}, "edge": {"version_added": "80"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": "67"}, "opera_android": {"version_added": "57"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "13.0"}, "webview_android": {"version_added": "80"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "register": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicSyncManager/register", "support": {"chrome": {"version_added": "80"}, "chrome_android": {"version_added": "80"}, "edge": {"version_added": "80"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": "67"}, "opera_android": {"version_added": "57"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "13.0"}, "webview_android": {"version_added": "80"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}, "unregister": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PeriodicSyncManager/unregister", "support": {"chrome": {"version_added": "80"}, "chrome_android": {"version_added": "80"}, "edge": {"version_added": "80"}, "firefox": {"version_added": false}, "firefox_android": {"version_added": false}, "ie": {"version_added": false}, "opera": {"version_added": "67"}, "opera_android": {"version_added": "57"}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": "13.0"}, "webview_android": {"version_added": "80"}}, "status": {"experimental": true, "standard_track": false, "deprecated": false}}}}}}