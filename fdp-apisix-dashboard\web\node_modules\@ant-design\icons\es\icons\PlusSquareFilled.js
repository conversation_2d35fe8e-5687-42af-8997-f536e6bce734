// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
import * as React from 'react';
import PlusSquareFilledSvg from "@ant-design/icons-svg/es/asn/PlusSquareFilled";
import AntdIcon from '../components/AntdIcon';

var PlusSquareFilled = function PlusSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, Object.assign({}, props, {
    ref: ref,
    icon: PlusSquareFilledSvg
  }));
};

PlusSquareFilled.displayName = 'PlusSquareFilled';
export default /*#__PURE__*/React.forwardRef(PlusSquareFilled);