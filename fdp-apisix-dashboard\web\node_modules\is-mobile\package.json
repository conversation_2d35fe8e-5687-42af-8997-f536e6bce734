{"name": "is-mobile", "description": "Check if mobile browser.", "version": "2.1.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/is-mobile.git"}, "homepage": "https://github.com/juliangruber/is-mobile", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "prettier-standard '**/*.js' && standard && tape test.js"}, "dependencies": {}, "devDependencies": {"prettier-standard": "^8.0.1", "standard": "^12.0.1", "tape": "~1.0.4"}, "keywords": ["mobile", "desktop", "check", "browser"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}