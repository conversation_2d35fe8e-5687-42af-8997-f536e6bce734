/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.32.1(8ad5e3bceab16a4d0856c43a374b511dffb1e795)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/editor/editor.main.nls.fr", {
	"vs/base/browser/ui/actionbar/actionViewItems": [
		"{0} ({1})",
	],
	"vs/base/browser/ui/findinput/findInput": [
		"entrée",
	],
	"vs/base/browser/ui/findinput/findInputCheckboxes": [
		"Re<PERSON><PERSON> la casse",
		"Mo<PERSON> entier",
		"Utiliser une expression régulière",
	],
	"vs/base/browser/ui/findinput/replaceInput": [
		"entrée",
		"Préserver la casse",
	],
	"vs/base/browser/ui/iconLabel/iconLabelHover": [
		"Chargement...",
	],
	"vs/base/browser/ui/inputbox/inputBox": [
		"Erreur : {0}",
		"Avertissement : {0}",
		"Info : {0}",
		"pour l’historique",
	],
	"vs/base/browser/ui/keybindingLabel/keybindingLabel": [
		"Indépendant",
	],
	"vs/base/browser/ui/tree/abstractTree": [
		"Effacer",
		"Désactiver le filtre sur le type",
		"Activer le filtre sur le type",
		"Aucun élément",
		"{0} éléments sur {1} correspondants",
	],
	"vs/base/common/actions": [
		"(vide)",
	],
	"vs/base/common/errorMessage": [
		"{0}: {1}",
		"Une erreur système s\'est produite ({0})",
		"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.",
		"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.",
		"{0} ({1} erreurs au total)",
		"Une erreur inconnue s’est produite. Veuillez consulter le journal pour plus de détails.",
	],
	"vs/base/common/keybindingLabels": [
		"Ctrl",
		"Maj",
		"Alt",
		"Windows",
		"Ctrl",
		"Maj",
		"Alt",
		"Super",
		"Contrôle",
		"Maj",
		"Option",
		"Commande",
		"Contrôle",
		"Maj",
		"Alt",
		"Windows",
		"Contrôle",
		"Maj",
		"Alt",
		"Super",
	],
	"vs/base/parts/quickinput/browser/quickInput": [
		"Précédent",
		"Appuyez sur \'Entrée\' pour confirmer votre saisie, ou sur \'Échap\' pour l\'annuler",
		"{0}/{1}",
		"Taper pour affiner les résultats.",
		"{0} résultats",
		"{0} Sélectionnés",
		"OK",
		"Personnalisé",
		"Précédent ({0})",
		"Précédent",
	],
	"vs/base/parts/quickinput/browser/quickInputList": [
		"Entrée rapide",
	],
	"vs/editor/browser/controller/coreCommands": [
		"Aligner par rapport à la fin même en cas de passage à des lignes plus longues",
		"Aligner par rapport à la fin même en cas de passage à des lignes plus longues",
		"Curseurs secondaires supprimés",
	],
	"vs/editor/browser/controller/textAreaHandler": [
		"éditeur",
		"L\'éditeur n\'est pas accessible pour le moment. Appuyez sur {0} pour voir les options.",
	],
	"vs/editor/browser/editorExtensions": [
		"Ann&&uler",
		"Annuler",
		"&&Rétablir",
		"Rétablir",
		"&&Sélectionner tout",
		"Tout sélectionner",
	],
	"vs/editor/browser/widget/codeEditorWidget": [
		"Le nombre de curseurs a été limité à {0}.",
	],
	"vs/editor/browser/widget/diffEditorWidget": [
		"Élément décoratif de ligne pour les insertions dans l\'éditeur de différences.",
		"Élément décoratif de ligne pour les suppressions dans l\'éditeur de différences.",
		"Impossible de comparer les fichiers car l\'un d\'eux est trop volumineux.",
	],
	"vs/editor/browser/widget/diffReview": [
		"Icône de l\'option Insérer dans la revue des différences.",
		"Icône de l\'option Supprimer dans la revue des différences.",
		"Icône de l\'option Fermer dans la revue des différences.",
		"Fermer",
		"aucune ligne changée",
		"1 ligne changée",
		"{0} lignes changées",
		"Différence {0} sur {1} : ligne d\'origine {2}, {3}, ligne modifiée {4}, {5}",
		"vide",
		"{0} ligne inchangée {1}",
		"{0} ligne d\'origine {1} ligne modifiée {2}",
		"+ {0} ligne modifiée {1}",
		"- {0} ligne d\'origine {1}",
		"Accéder à la différence suivante",
		"Accéder la différence précédente",
	],
	"vs/editor/browser/widget/inlineDiffMargin": [
		"Copier les lignes supprimées",
		"Copier la ligne supprimée",
		"Copier les lignes modifiées",
		"Copier la ligne modifiée",
		"Copier la ligne supprimée ({0})",
		"Copier la ligne modifiée ({0})",
		"Annuler la modification",
		"Copier la ligne supprimée ({0})",
		"Copier la ligne modifiée ({0})",
	],
	"vs/editor/common/config/editorConfigurationSchema": [
		"Éditeur",
		"Le nombre d\'espaces auxquels une tabulation est égale. Ce paramètre est substitué basé sur le contenu du fichier lorsque `#editor.detectIndentation#` est à \'on\'.",
		"Espaces insérés quand vous appuyez sur la touche Tab. Ce paramètre est remplacé en fonction du contenu du fichier quand \'#editor.detectIndentation#\' est activé.",
		"Contrôle si \'#editor.tabSize#\' et \'#editor.insertSpaces#\' sont automatiquement détectés lors de l’ouverture d’un fichier en fonction de son contenu.",
		"Supprimer l\'espace blanc de fin inséré automatiquement.",
		"Traitement spécial des fichiers volumineux pour désactiver certaines fonctionnalités utilisant beaucoup de mémoire.",
		"Contrôle si la saisie semi-automatique doit être calculée en fonction des mots présents dans le document.",
		"Suggère uniquement des mots dans le document actif.",
		"Suggère des mots dans tous les documents ouverts du même langage.",
		"Suggère des mots dans tous les documents ouverts.",
		"Contrôle la façon dont sont calculées les complétions basées sur des mots dans les documents.",
		"Coloration sémantique activée pour tous les thèmes de couleur.",
		"Coloration sémantique désactivée pour tous les thèmes de couleur.",
		"La coloration sémantique est configurée par le paramètre \'semanticHighlighting\' du thème de couleur actuel.",
		"Contrôle si semanticHighlighting est affiché pour les langages qui le prennent en charge.",
		"Garder les éditeurs d\'aperçu ouverts même si l\'utilisateur double-clique sur son contenu ou appuie sur la touche Échap. ",
		"Les lignes plus longues que cette valeur ne sont pas tokenisées pour des raisons de performances",
		"Définit les symboles de type crochet qui augmentent ou diminuent le retrait.",
		"Séquence de chaînes ou de caractères de crochets ouvrants.",
		"Séquence de chaînes ou de caractères de crochets fermants.",
		"Définit les paires de crochets qui sont colorisées par leur niveau d’imbrication si la colorisation des paires de crochets est activée.",
		"Séquence de chaînes ou de caractères de crochets ouvrants.",
		"Séquence de chaînes ou de caractères de crochets fermants.",
		"Délai d\'expiration en millisecondes avant annulation du calcul de diff. Utilisez 0 pour supprimer le délai d\'expiration.",
		"Taille de fichier maximale en Mo pour laquelle calculer les différences. Utilisez 0 pour ne pas avoir de limite.",
		"Contrôle si l\'éditeur de différences affiche les différences en mode côte à côte ou inline.",
		"Quand il est activé, l\'éditeur de différences ignore les changements d\'espace blanc de début ou de fin.",
		"Contrôle si l\'éditeur de différences affiche les indicateurs +/- pour les changements ajoutés/supprimés .",
		"Contrôle si l\'éditeur affiche CodeLens.",
		"Le retour automatique à la ligne n\'est jamais effectué.",
		"Le retour automatique à la ligne s\'effectue en fonction de la largeur de la fenêtre d\'affichage.",
		"Le retour automatique à la ligne dépend du paramètre \'#editor.wordWrap#\'.",
	],
	"vs/editor/common/config/editorOptions": [
		"L\'éditeur utilise les API de la plateforme pour détecter si un lecteur d\'écran est attaché.",
		"L\'éditeur est optimisé en permanence pour les lecteurs d\'écran. Le retour automatique à la ligne est désactivé.",
		"L\'éditeur n\'est jamais optimisé pour une utilisation avec un lecteur d\'écran.",
		"Contrôle si l\'éditeur doit s\'exécuter dans un mode optimisé pour les lecteurs d\'écran. Si la valeur est on, le retour automatique à la ligne est désactivé.",
		"Contrôle si un espace est inséré pour les commentaires.",
		"Contrôle si les lignes vides doivent être ignorées avec des actions d\'activation/de désactivation, d\'ajout ou de suppression des commentaires de ligne.",
		"Contrôle si la copie sans sélection permet de copier la ligne actuelle.",
		"Contrôle si le curseur doit sauter pour rechercher les correspondances lors de la saisie.",
		"Ne lancez jamais la chaîne de recherche dans la sélection de l’éditeur.",
		"Toujours amorcer la chaîne de recherche à partir de la sélection de l’éditeur, y compris le mot à la position du curseur.",
		"Chaîne de recherche initiale uniquement dans la sélection de l’éditeur.",
		"Détermine si la chaîne de recherche dans le Widget Recherche est initialisée avec la sélection de l’éditeur.",
		"Ne jamais activer automatiquement la recherche dans la sélection (par défaut).",
		"Toujours activer automatiquement la recherche dans la sélection.",
		"Activez Rechercher automatiquement dans la sélection quand plusieurs lignes de contenu sont sélectionnées.",
		"Contrôle la condition d\'activation automatique de la recherche dans la sélection.",
		"Détermine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partagé sur macOS.",
		"Contrôle si le widget Recherche doit ajouter des lignes supplémentaires en haut de l\'éditeur. Quand la valeur est true, vous pouvez faire défiler au-delà de la première ligne si le widget Recherche est visible.",
		"Contrôle si la recherche redémarre automatiquement depuis le début (ou la fin) quand il n\'existe aucune autre correspondance.",
		"Active/désactive les ligatures de police (fonctionnalités de police \'calt\' et \'liga\'). Remplacez ceci par une chaîne pour contrôler de manière précise la propriété CSS \'font-feature-settings\'.",
		"Propriété CSS \'font-feature-settings\' explicite. Vous pouvez passer une valeur booléenne à la place si vous devez uniquement activer/désactiver les ligatures.",
		"Configure les ligatures de police ou les fonctionnalités de police. Il peut s\'agir d\'une valeur booléenne permettant d\'activer/de désactiver les ligatures, ou d\'une chaîne correspondant à la valeur de la propriété CSS \'font-feature-settings\'.",
		"Contrôle la taille de police en pixels.",
		"Seuls les mots clés \"normal\" et \"bold\", ou les nombres compris entre 1 et 1 000 sont autorisés.",
		"Contrôle l\'épaisseur de police. Accepte les mots clés \"normal\" et \"bold\", ou les nombres compris entre 1 et 1 000.",
		"Montrer l\'aperçu des résultats (par défaut)",
		"Accéder au résultat principal et montrer un aperçu",
		"Accéder au résultat principal et activer l\'accès sans aperçu pour les autres",
		"Ce paramètre est déprécié, utilisez des paramètres distincts comme \'editor.editor.gotoLocation.multipleDefinitions\' ou \'editor.editor.gotoLocation.multipleImplementations\' à la place.",
		"Contrôle le comportement de la commande \'Atteindre la définition\' quand plusieurs emplacements cibles existent.",
		"Contrôle le comportement de la commande \'Atteindre la définition de type\' quand plusieurs emplacements cibles existent.",
		"Contrôle le comportement de la commande \'Atteindre la déclaration\' quand plusieurs emplacements cibles existent.",
		"Contrôle le comportement de la commande \'Atteindre les implémentations\' quand plusieurs emplacements cibles existent.",
		"Contrôle le comportement de la commande \'Atteindre les références\' quand plusieurs emplacements cibles existent.",
		"ID de commande alternatif exécuté quand le résultat de \'Atteindre la définition\' est l\'emplacement actuel.",
		"ID de commande alternatif exécuté quand le résultat de \'Atteindre la définition de type\' est l\'emplacement actuel.",
		"ID de commande alternatif exécuté quand le résultat de \'Atteindre la déclaration\' est l\'emplacement actuel.",
		"ID de commande alternatif exécuté quand le résultat de \'Atteindre l\'implémentation\' est l\'emplacement actuel.",
		"ID de commande alternatif exécuté quand le résultat de \'Atteindre la référence\' est l\'emplacement actuel.",
		"Contrôle si le pointage est affiché.",
		"Contrôle le délai en millisecondes, après lequel le survol est affiché.",
		"Contrôle si le pointage doit rester visible quand la souris est déplacée au-dessus.",
		"Préférez afficher les points au-dessus de la ligne, s’il y a de l’espace.",
		"Active l’ampoule d’action de code dans l’éditeur.",
		"Active les indicateurs inlay dans l’éditeur.",
		"Contrôle la taille de police des indicateurs d’inlay dans l’éditeur. La valeur par défaut de 90 % de « #editor.fontSize# » est utilisée lorsque la valeur configurée est inférieure à « 5 » ou supérieure à la taille de police de l’éditeur.",
		"Contrôle la famille de polices des indicateurs d’inlay dans l’éditeur. Lorsqu’il est défini sur vide, \'#editor.fontFamily#\' est utilisé.",
		"Contrôle la hauteur de ligne. \r\n - Utilisez 0 pour calculer automatiquement la hauteur de ligne à partir de la taille de police.\r\n : les valeurs comprises entre 0 et 8 sont utilisées comme multiplicateur avec la taille de police.\r\n : les valeurs supérieures ou égales à 8 seront utilisées comme valeurs effectives.",
		"Contrôle si la minimap est affichée.",
		"Le minimap a la même taille que le contenu de l\'éditeur (défilement possible).",
		"Le minimap s\'agrandit ou se réduit selon les besoins pour remplir la hauteur de l\'éditeur (pas de défilement).",
		"Le minimap est réduit si nécessaire pour ne jamais dépasser la taille de l\'éditeur (pas de défilement).",
		"Contrôle la taille du minimap.",
		"Contrôle le côté où afficher la minimap.",
		"Contrôle quand afficher le curseur du minimap.",
		"Échelle du contenu dessiné dans le minimap : 1, 2 ou 3.",
		"Afficher les caractères réels sur une ligne par opposition aux blocs de couleur.",
		"Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.",
		"Contrôle la quantité d’espace entre le bord supérieur de l’éditeur et la première ligne.",
		"Contrôle la quantité d\'espace entre le bord inférieur de l\'éditeur et la dernière ligne.",
		"Active une fenêtre contextuelle qui affiche de la documentation sur les paramètres et des informations sur les types à mesure que vous tapez.",
		"Détermine si le menu de suggestions de paramètres se ferme ou reviens au début lorsque la fin de la liste est atteinte.",
		"Activez les suggestions rapides dans les chaînes.",
		"Activez les suggestions rapides dans les commentaires.",
		"Activez les suggestions rapides en dehors des chaînes et des commentaires.",
		"Contrôle si les suggestions doivent apparaître automatiquement pendant la saisie.",
		"Les numéros de ligne ne sont pas affichés.",
		"Les numéros de ligne sont affichés en nombre absolu.",
		"Les numéros de ligne sont affichés sous la forme de distance en lignes à la position du curseur.",
		"Les numéros de ligne sont affichés toutes les 10 lignes.",
		"Contrôle l\'affichage des numéros de ligne.",
		"Nombre de caractères monospace auxquels cette règle d\'éditeur effectue le rendu.",
		"Couleur de cette règle d\'éditeur.",
		"Rendre les règles verticales après un certain nombre de caractères à espacement fixe. Utiliser plusieurs valeurs pour plusieurs règles. Aucune règle n\'est dessinée si le tableau est vide.",
		"La barre de défilement verticale sera visible uniquement lorsque cela est nécessaire.",
		"La barre de défilement verticale est toujours visible.",
		"La barre de défilement verticale est toujours masquée.",
		"Contrôle la visibilité de la barre de défilement verticale.",
		"La barre de défilement horizontale sera visible uniquement lorsque cela est nécessaire.",
		"La barre de défilement horizontale est toujours visible.",
		"La barre de défilement horizontale est toujours masquée.",
		"Contrôle la visibilité de la barre de défilement horizontale.",
		"Largeur de la barre de défilement verticale.",
		"Hauteur de la barre de défilement horizontale.",
		"Contrôle si les clics permettent de faire défiler par page ou d’accéder à la position de clic.",
		"Contrôle si tous les caractères ASCII non basiques sont mis en surbrillance. Seuls les caractères compris entre U+0020 et U+007E, tabulation, saut de ligne et retour chariot sont considérés comme des ASCII de base.",
		"Contrôle si les caractères qui réservent de l’espace ou qui n’ont pas de largeur sont mis en surbrillance.",
		"Contrôle si les caractères mis en surbrillance peuvent être déconcertés avec des caractères ASCII de base, à l’exception de ceux qui sont courants dans les paramètres régionaux utilisateur actuels.",
		"Contrôle si les caractères des commentaires doivent également faire l’objet d’une mise en surbrillance Unicode.",
		"Contrôle si les caractères des commentaires doivent également faire l’objet d’une mise en surbrillance Unicode.",
		"Définit les caractères autorisés qui ne sont pas mis en surbrillance.",
		"Les caractères Unicode communs aux paramètres régionaux autorisés ne sont pas mis en surbrillance.",
		"Contrôle si les suggestions en ligne doivent être affichées automatiquement dans l’éditeur.",
		"Contrôle si la coloration de la paire de crochets est activée ou non. Utilisez « workbench.colorCustomizations » pour remplacer les couleurs de surbrillance de crochets.",
		"Désactive les repères de paire de crochets.",
		"Active les repères de paire de crochets uniquement pour la paire de crochets actifs.",
		"Désactive les repères de paire de crochets.",
		"Contrôle si les guides de la paire de crochets sont activés ou non.",
		"Active les repères horizontaux en plus des repères de paire de crochets verticaux.",
		"Active les repères horizontaux uniquement pour la paire de crochets actifs.",
		"Désactive les repères de paire de crochets horizontaux.",
		"Contrôle si les guides de la paire de crochets horizontaux sont activés ou non.",
		"Contrôle si les guides de la paire de crochets sont activés ou non.",
		"Contrôle si l’éditeur doit afficher les guides de mise en retrait.",
		"Contrôle si l’éditeur doit mettre en surbrillance le guide de mise en retrait actif.",
		"Insérez une suggestion sans remplacer le texte à droite du curseur.",
		"Insérez une suggestion et remplacez le texte à droite du curseur.",
		"Contrôle si les mots sont remplacés en cas d\'acceptation de la saisie semi-automatique. Notez que cela dépend des extensions adhérant à cette fonctionnalité.",
		"Détermine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.",
		"Contrôle si le tri favorise les mots qui apparaissent à proximité du curseur.",
		"Contrôle si les sélections de suggestion mémorisées sont partagées entre plusieurs espaces de travail et fenêtres (nécessite \'#editor.suggestSelection#\').",
		"Contrôle si un extrait de code actif empêche les suggestions rapides.",
		"Contrôle s\'il faut montrer ou masquer les icônes dans les suggestions.",
		"Contrôle la visibilité de la barre d\'état en bas du widget de suggestion.",
		"Contrôle si la sortie de la suggestion doit être affichée en aperçu dans l’éditeur.",
		"Détermine si les détails du widget de suggestion sont inclus dans l\'étiquette ou uniquement dans le widget de détails",
		"Ce paramètre est déprécié. Le widget de suggestion peut désormais être redimensionné.",
		"Ce paramètre est déprécié, veuillez utiliser des paramètres distincts comme \'editor.suggest.showKeywords\' ou \'editor.suggest.showSnippets\' à la place.",
		"Si activé, IntelliSense montre des suggestions de type \'method\'.",
		"Si activé, IntelliSense montre des suggestions de type \'function\'.",
		"Si activé, IntelliSense montre des suggestions de type \'constructor\'.",
		"Si cette option est activée, IntelliSense montre des suggestions `dépréciées`.",
		"Si activé, IntelliSense montre des suggestions de type \'field\'.",
		"Si activé, IntelliSense montre des suggestions de type \'variable\'.",
		"Si activé, IntelliSense montre des suggestions de type \'class\'.",
		"Si activé, IntelliSense montre des suggestions de type \'struct\'.",
		"Si activé, IntelliSense montre des suggestions de type \'interface\'.",
		"Si activé, IntelliSense montre des suggestions de type \'module\'.",
		"Si activé, IntelliSense montre des suggestions de type \'property\'.",
		"Si activé, IntelliSense montre des suggestions de type \'event\'.",
		"Si activé, IntelliSense montre des suggestions de type \'operator\'.",
		"Si activé, IntelliSense montre des suggestions de type \'unit\'.",
		"Si activé, IntelliSense montre des suggestions de type \'value\'.",
		"Si activé, IntelliSense montre des suggestions de type \'constant\'.",
		"Si activé, IntelliSense montre des suggestions de type \'enum\'.",
		"Si activé, IntelliSense montre des suggestions de type \'enumMember\'.",
		"Si activé, IntelliSense montre des suggestions de type \'keyword\'.",
		"Si activé, IntelliSense montre des suggestions de type \'text\'.",
		"Si activé, IntelliSense montre des suggestions de type \'color\'.",
		"Si activé, IntelliSense montre des suggestions de type \'file\'.",
		"Si activé, IntelliSense montre des suggestions de type \'reference\'.",
		"Si activé, IntelliSense montre des suggestions de type \'customcolor\'.",
		"Si activé, IntelliSense montre des suggestions de type \'folder\'.",
		"Si activé, IntelliSense montre des suggestions de type \'typeParameter\'.",
		"Si activé, IntelliSense montre des suggestions de type \'snippet\'.",
		"Si activé, IntelliSense montre des suggestions de type \'utilisateur\'.",
		"Si activé, IntelliSense montre des suggestions de type \'problèmes\'.",
		"Indique si les espaces blancs de début et de fin doivent toujours être sélectionnés.",
		"Contrôle si les suggestions doivent être acceptées sur les caractères de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut être un caractère de validation qui accepte une suggestion et tape ce caractère.",
		"Accepter uniquement une suggestion avec \'Entrée\' quand elle effectue une modification textuelle.",
		"Contrôle si les suggestions sont acceptées après appui sur \'Entrée\', en plus de \'Tab\'. Permet d’éviter toute ambiguïté entre l’insertion de nouvelles lignes et l\'acceptation de suggestions.",
		"Contrôle le nombre de lignes de l’éditeur qu’un lecteur d’écran peut lire en une seule fois. Quand nous détectons un lecteur d’écran, nous définissons automatiquement la valeur par défaut à 500. Attention : Les valeurs supérieures à la valeur par défaut peuvent avoir un impact important sur les performances.",
		"Contenu de l\'éditeur",
		"Utilisez les configurations de langage pour déterminer quand fermer automatiquement les parenthèses.",
		"Fermer automatiquement les parenthèses uniquement lorsque le curseur est à gauche de l’espace.",
		"Contrôle si l’éditeur doit fermer automatiquement les parenthèses quand l’utilisateur ajoute une parenthèse ouvrante.",
		"Supprimez les guillemets ou crochets fermants adjacents uniquement s\'ils ont été insérés automatiquement.",
		"Contrôle si l\'éditeur doit supprimer les guillemets ou crochets fermants adjacents au moment de la suppression.",
		"Tapez avant les guillemets ou les crochets fermants uniquement s\'ils sont automatiquement insérés.",
		"Contrôle si l\'éditeur doit taper avant les guillemets ou crochets fermants.",
		"Utilisez les configurations de langage pour déterminer quand fermer automatiquement les guillemets.",
		"Fermer automatiquement les guillemets uniquement lorsque le curseur est à gauche de l’espace.",
		"Contrôle si l’éditeur doit fermer automatiquement les guillemets après que l’utilisateur ajoute un guillemet ouvrant.",
		"L\'éditeur n\'insère pas de retrait automatiquement.",
		"L\'éditeur conserve le retrait de la ligne actuelle.",
		"L\'éditeur conserve le retrait de la ligne actuelle et honore les crochets définis par le langage.",
		"L\'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage et appelle des objets onEnterRules spéciaux définis par les langages.",
		"L\'éditeur conserve le retrait de la ligne actuelle, honore les crochets définis par le langage, appelle des objets onEnterRules spéciaux définis par les langages et honore les objets indentationRules définis par les langages.",
		"Contrôle si l\'éditeur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, déplacent ou mettent en retrait des lignes.",
		"Utilisez les configurations de langue pour déterminer quand entourer automatiquement les sélections.",
		"Entourez avec des guillemets et non des crochets.",
		"Entourez avec des crochets et non des guillemets.",
		"Contrôle si l\'éditeur doit automatiquement entourer les sélections quand l\'utilisateur tape des guillemets ou des crochets.",
		"Émule le comportement des tabulations pour la sélection quand des espaces sont utilisés à des fins de mise en retrait. La sélection respecte les taquets de tabulation.",
		"Contrôle si l\'éditeur affiche CodeLens.",
		"Contrôle la famille de polices pour CodeLens.",
		"Contrôle la taille de police en pixels pour CodeLens. Quand la valeur est \'0\', 90 % de \'#editor.fontSize#\' est utilisé.",
		"Contrôle si l\'éditeur doit afficher les éléments décoratifs de couleurs inline et le sélecteur de couleurs.",
		"Autoriser l\'utilisation de la souris et des touches pour sélectionner des colonnes.",
		"Contrôle si la coloration syntaxique doit être copiée dans le presse-papiers.",
		"Contrôler le style d’animation du curseur.",
		"Contrôle si l\'animation du point d\'insertion doit être activée.",
		"Contrôle le style du curseur.",
		"Contrôle le nombre minimal de lignes de début et de fin visibles autour du curseur. Également appelé \'scrollOff\' ou \'scrollOffset\' dans d\'autres éditeurs.",
		"\'cursorSurroundingLines\' est appliqué seulement s\'il est déclenché via le clavier ou une API.",
		"\'cursorSurroundingLines\' est toujours appliqué.",
		"Contrôle quand \'cursorSurroundingLines\' doit être appliqué.",
		"Détermine la largeur du curseur lorsque `#editor.cursorStyle#` est à `line`.",
		"Contrôle si l’éditeur autorise le déplacement de sélections par glisser-déplacer.",
		"Multiplicateur de vitesse de défilement quand vous appuyez sur \'Alt\'.",
		"Contrôle si l\'éditeur a le pliage de code activé.",
		"Utilisez une stratégie de pliage propre à la langue, si disponible, sinon utilisez la stratégie basée sur le retrait.",
		"Utilisez la stratégie de pliage basée sur le retrait.",
		"Contrôle la stratégie de calcul des plages de pliage.",
		"Contrôle si l\'éditeur doit mettre en évidence les plages pliées.",
		"Contrôle si l’éditeur réduit automatiquement les plages d’importation.",
		"Nombre maximal de régions pliables. L’augmentation de cette valeur peut réduire la réactivité de l’éditeur lorsque la source actuelle comprend un grand nombre de régions pliables.",
		"Contrôle si le fait de cliquer sur le contenu vide après une ligne pliée déplie la ligne.",
		"Contrôle la famille de polices.",
		"Détermine si l’éditeur doit automatiquement mettre en forme le contenu collé. Un formateur doit être disponible et être capable de mettre en forme une plage dans un document.",
		"Contrôle si l’éditeur doit mettre automatiquement en forme la ligne après la saisie.",
		"Contrôle si l\'éditeur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au débogage.",
		"Contrôle si le curseur doit être masqué dans la règle de la vue d’ensemble.",
		"Contrôle l\'espacement des lettres en pixels.",
		"Contrôle si la modification liée est activée dans l\'éditeur. En fonction du langage, les symboles associés, par exemple les balises HTML, sont mis à jour durant le processus de modification.",
		"Contrôle si l’éditeur doit détecter les liens et les rendre cliquables.",
		"Mettez en surbrillance les crochets correspondants.",
		"Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.",
		"Faire un zoom sur la police de l\'éditeur quand l\'utilisateur fait tourner la roulette de la souris tout en maintenant la touche \'Ctrl\' enfoncée.",
		"Fusionnez plusieurs curseurs quand ils se chevauchent.",
		"Mappe vers \'Contrôle\' dans Windows et Linux, et vers \'Commande\' dans macOS.",
		"Mappe vers \'Alt\' dans Windows et Linux, et vers \'Option\' dans macOS.",
		"The modifier to be used to add multiple cursors with the mouse. The Go to Definition and Open Link mouse gestures will adapt such that they do not conflict with the multicursor modifier. [Read more](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).",
		"Chaque curseur colle une seule ligne de texte.",
		"Chaque curseur colle le texte en entier.",
		"Contrôle le collage quand le nombre de lignes du texte collé correspond au nombre de curseurs.",
		"Contrôle si l\'éditeur doit mettre en surbrillance les occurrences de symboles sémantiques.",
		"Contrôle si une bordure doit être dessinée autour de la règle de la vue d\'ensemble.",
		"Focus sur l\'arborescence à l\'ouverture de l\'aperçu",
		"Placer le focus sur l\'éditeur à l\'ouverture de l\'aperçu",
		"Contrôle s\'il faut mettre le focus sur l\'éditeur inline ou sur l\'arborescence dans le widget d\'aperçu.",
		"Contrôle si le geste de souris Accéder à la définition ouvre toujours le widget d\'aperçu.",
		"Contrôle le délai en millisecondes après lequel des suggestions rapides sont affichées.",
		"Contrôle si l\'éditeur renomme automatiquement selon le type.",
		"Déprécié. Utilisez \'editor.linkedEditing\' à la place.",
		"Contrôle si l’éditeur doit afficher les caractères de contrôle.",
		"Affichez le dernier numéro de ligne quand le fichier se termine par un saut de ligne.",
		"Met en surbrillance la gouttière et la ligne actuelle.",
		"Contrôle la façon dont l’éditeur doit afficher la mise en surbrillance de la ligne actuelle.",
		"Contrôle si l\'éditeur doit afficher la mise en surbrillance de la ligne actuelle uniquement quand il a le focus.",
		"Affiche les espaces blancs à l\'exception des espaces uniques entre les mots.",
		"Afficher les espaces blancs uniquement sur le texte sélectionné.",
		"Affiche uniquement les caractères correspondant aux espaces blancs de fin.",
		"Contrôle la façon dont l’éditeur doit restituer les caractères espaces.",
		"Contrôle si les sélections doivent avoir des angles arrondis.",
		"Contrôle le nombre de caractères supplémentaires, au-delà duquel l’éditeur défile horizontalement.",
		"Contrôle si l’éditeur défile au-delà de la dernière ligne.",
		"Faites défiler uniquement le long de l\'axe prédominant quand le défilement est à la fois vertical et horizontal. Empêche la dérive horizontale en cas de défilement vertical sur un pavé tactile.",
		"Contrôle si le presse-papiers principal Linux doit être pris en charge.",
		"Contrôle si l\'éditeur doit mettre en surbrillance les correspondances similaires à la sélection.",
		"Affichez toujours les contrôles de pliage.",
		"Affichez uniquement les contrôles de pliage quand la souris est au-dessus de la reliure.",
		"Contrôle quand afficher les contrôles de pliage sur la reliure.",
		"Contrôle la disparition du code inutile.",
		"Contrôle les variables dépréciées barrées.",
		"Afficher des suggestions d’extraits au-dessus d’autres suggestions.",
		"Afficher des suggestions d’extraits en-dessous d’autres suggestions.",
		"Afficher des suggestions d’extraits avec d’autres suggestions.",
		"Ne pas afficher de suggestions d’extrait de code.",
		"Contrôle si les extraits de code s\'affichent en même temps que d\'autres suggestions, ainsi que leur mode de tri.",
		"Contrôle si l\'éditeur défile en utilisant une animation.",
		"Taille de la police pour le widget de suggestion. Lorsque la valeur est à `0`, la valeur de `#editor.fontSize` est utilisée.",
		"Hauteur de ligne du widget de suggestion. Quand la valeur est \'0\', la valeur de \'#editor.lineHeight#\' est utilisée. La valeur minimale est 8.",
		"Contrôle si les suggestions devraient automatiquement s’afficher lorsque vous tapez les caractères de déclencheur.",
		"Sélectionnez toujours la première suggestion.",
		"Sélectionnez les suggestions récentes sauf si une entrée ultérieure en a sélectionné une, par ex., \'console.| -> console.log\', car \'log\' a été effectué récemment.",
		"Sélectionnez des suggestions en fonction des préfixes précédents qui ont complété ces suggestions, par ex., \'co -> console\' et \'con -> const\'.",
		"Contrôle comment les suggestions sont pré-sélectionnés lors de l’affichage de la liste de suggestion.",
		"La complétion par tabulation insérera la meilleure suggestion lorsque vous appuyez sur tab.",
		"Désactiver les complétions par tabulation.",
		"Compléter les extraits de code par tabulation lorsque leur préfixe correspond. Fonctionne mieux quand les \'quickSuggestions\' ne sont pas activées.",
		"Active les complétions par tabulation",
		"Les marques de fin de ligne inhabituelles sont automatiquement supprimées.",
		"Les marques de fin de ligne inhabituelles sont ignorées.",
		"Les marques de fin de ligne inhabituelles demandent à être supprimées.",
		"Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des problèmes.",
		"L\'insertion et la suppression des espaces blancs suit les taquets de tabulation.",
		"Caractères utilisés comme séparateurs de mots durant la navigation ou les opérations basées sur les mots",
		"Le retour automatique à la ligne n\'est jamais effectué.",
		"Le retour automatique à la ligne s\'effectue en fonction de la largeur de la fenêtre d\'affichage.",
		"Les lignes seront terminées à `#editor.wordWrapColumn#`.",
		"Les lignes seront terminées au minimum du viewport et `#editor.wordWrapColumn#`.",
		"Contrôle comment les lignes doivent être limitées.",
		"Contrôle la colonne de terminaison de l’éditeur lorsque `#editor.wordWrap#` est à `wordWrapColumn` ou `bounded`.",
		"Aucune mise en retrait. Les lignes enveloppées commencent à la colonne 1.",
		"Les lignes enveloppées obtiennent la même mise en retrait que le parent.",
		"Les lignes justifiées obtiennent une mise en retrait +1 vers le parent.",
		"Les lignes justifiées obtiennent une mise en retrait +2 vers le parent. ",
		"Contrôle la mise en retrait des lignes justifiées.",
		"Suppose que tous les caractères ont la même largeur. Il s\'agit d\'un algorithme rapide qui fonctionne correctement pour les polices à espacement fixe et certains scripts (comme les caractères latins) où les glyphes ont la même largeur.",
		"Délègue le calcul des points de wrapping au navigateur. Il s\'agit d\'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.",
		"Contrôle l\'algorithme qui calcule les points de wrapping.",
	],
	"vs/editor/common/core/editorColorRegistry": [
		"Couleur d\'arrière-plan de la mise en surbrillance de la ligne à la position du curseur.",
		"Couleur d\'arrière-plan de la bordure autour de la ligne à la position du curseur.",
		"Couleur d\'arrière-plan des plages mises en surbrillance, comme par les fonctionnalités de recherche et Quick Open. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur d\'arrière-plan de la bordure autour des plages mises en surbrillance.",
		"Couleur d\'arrière-plan du symbole mis en surbrillance, comme le symbole Atteindre la définition ou Suivant/Précédent. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.",
		"Couleur d\'arrière-plan de la bordure autour des symboles mis en surbrillance.",
		"Couleur du curseur de l\'éditeur.",
		"La couleur de fond du curseur de l\'éditeur. Permet de personnaliser la couleur d\'un caractère survolé par un curseur de bloc.",
		"Couleur des espaces blancs dans l\'éditeur.",
		"Couleur des repères de retrait de l\'éditeur.",
		"Couleur des guides d\'indentation de l\'éditeur actif",
		"Couleur des numéros de ligne de l\'éditeur.",
		"Couleur des numéros de lignes actives de l\'éditeur",
		"L’ID est déprécié. Utilisez à la place \'editorLineNumber.activeForeground\'.",
		"Couleur des numéros de lignes actives de l\'éditeur",
		"Couleur des règles de l\'éditeur",
		"Couleur pour les indicateurs CodeLens",
		"Couleur d\'arrière-plan pour les accolades associées",
		"Couleur pour le contour des accolades associées",
		"Couleur de la bordure de la règle d\'aperçu.",
		"Couleur d\'arrière-plan de la règle d\'aperçu de l\'éditeur. Utilisée uniquement quand la minimap est activée et placée sur le côté droit de l\'éditeur.",
		"Couleur de fond pour la bordure de l\'éditeur. La bordure contient les marges pour les symboles et les numéros de ligne.",
		"Couleur de bordure du code source inutile (non utilisé) dans l\'éditeur.",
		"Opacité du code source inutile (non utilisé) dans l\'éditeur. Par exemple, \'#000000c0\' affiche le code avec une opacité de 75 %. Pour les thèmes à fort contraste, utilisez la couleur de thème \'editorUnnecessaryCode.border\' pour souligner le code inutile au lieu d\'utiliser la transparence.",
		"Couleur de bordure du texte fantôme dans l’éditeur.",
		"Couleur de premier plan du texte fantôme dans l’éditeur.",
		"Couleur de l’arrière-plan du texte fantôme dans l’éditeur",
		"Couleur de marqueur de la règle d\'aperçu pour la mise en surbrillance des plages. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur du marqueur de la règle d\'aperçu pour les erreurs.",
		"Couleur du marqueur de la règle d\'aperçu pour les avertissements.",
		"Couleur du marqueur de la règle d\'aperçu pour les informations.",
		"Couleur de premier plan des crochets (1). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des crochets (2). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des crochets (3). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des crochets (4). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des crochets (5). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des crochets (6). Nécessite l’activation de la coloration de la paire de crochets.",
		"Couleur de premier plan des parenthèses inattendues",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (1). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (2). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (3). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (4). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (5). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets inactifs (6). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (1). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (2). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (3). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (4). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (5). Nécessite l’activation des repères de paire de crochets.",
		"Couleur d’arrière-plan des repères de paire de crochets actifs (6). Nécessite l’activation des repères de paire de crochets.",
		"Couleur de bordure utilisée pour mettre en surbrillance les caractères Unicode",
	],
	"vs/editor/common/editorContextKeys": [
		"Indique si le texte de l\'éditeur a le focus (le curseur clignote)",
		"Indique si l\'éditeur ou un widget de l\'éditeur a le focus (par exemple, le focus se trouve sur le widget de recherche)",
		"Indique si un éditeur ou une entrée de texte mis en forme a le focus (le curseur clignote)",
		"Indique si l\'éditeur est en lecture seule",
		"Indique si le contexte est celui d\'un éditeur de différences",
		"Indique si \'editor.columnSelection\' est activé",
		"Indique si du texte est sélectionné dans l\'éditeur",
		"Indique si l\'éditeur a plusieurs sélections",
		"Indique si la touche Tab permet de déplacer le focus hors de l\'éditeur",
		"Indique si le pointage de l\'éditeur est visible",
		"Indique si l\'éditeur fait partie d\'un éditeur plus important (par exemple Notebooks)",
		"Identificateur de langage de l\'éditeur",
		"Indique si l\'éditeur a un fournisseur d\'éléments de complétion",
		"Indique si l\'éditeur a un fournisseur d\'actions de code",
		"Indique si l\'éditeur a un fournisseur d\'informations CodeLens",
		"Indique si l\'éditeur a un fournisseur de définitions",
		"Indique si l\'éditeur a un fournisseur de déclarations",
		"Indique si l\'éditeur a un fournisseur d\'implémentation",
		"Indique si l\'éditeur a un fournisseur de définitions de type",
		"Indique si l\'éditeur a un fournisseur de pointage",
		"Indique si l\'éditeur a un fournisseur de mise en surbrillance pour les documents",
		"Indique si l\'éditeur a un fournisseur de symboles pour les documents",
		"Indique si l\'éditeur a un fournisseur de référence",
		"Indique si l\'éditeur a un fournisseur de renommage",
		"Indique si l\'éditeur a un fournisseur d\'aide sur les signatures",
		"Indique si l\'éditeur a un fournisseur d\'indicateurs inline",
		"Indique si l\'éditeur a un fournisseur de mise en forme pour les documents",
		"Indique si l\'éditeur a un fournisseur de mise en forme de sélection pour les documents",
		"Indique si l\'éditeur a plusieurs fournisseurs de mise en forme pour les documents",
		"Indique si l\'éditeur a plusieurs fournisseurs de mise en forme de sélection pour les documents",
	],
	"vs/editor/common/languages/modesRegistry": [
		"Texte brut",
	],
	"vs/editor/common/model/editStack": [
		"Frappe en cours",
	],
	"vs/editor/common/standaloneStrings": [
		"Aucune sélection",
		"Ligne {0}, colonne {1} ({2} sélectionné)",
		"Ligne {0}, colonne {1}",
		"{0} sélections ({1} caractères sélectionnés)",
		"{0} sélections",
		"Remplacement du paramètre \'accessibilitySupport\' par \'on\'.",
		"Ouverture de la page de documentation sur l\'accessibilité de l\'éditeur.",
		"dans un volet en lecture seule d\'un éditeur de différences.",
		"dans un volet d\'un éditeur de différences.",
		" dans un éditeur de code en lecture seule",
		" dans un éditeur de code",
		"Pour configurer l\'éditeur de manière à être optimisé en cas d\'utilisation d\'un lecteur d\'écran, appuyez sur Commande+E maintenant.",
		"Pour configurer l\'éditeur de manière à être optimisé en cas d\'utilisation d\'un lecteur d\'écran, appuyez sur Contrôle+E maintenant.",
		"L\'éditeur est configuré pour être optimisé en cas d\'utilisation avec un lecteur d\'écran.",
		"L\'éditeur est configuré pour ne jamais être optimisé en cas d\'utilisation avec un lecteur d\'écran, ce qui n\'est pas le cas pour le moment.",
		"Appuyez sur Tab dans l\'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. Activez ou désactivez ce comportement en appuyant sur {0}.",
		"Appuyez sur Tab dans l\'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. La commande {0} ne peut pas être déclenchée par une combinaison de touches.",
		"Appuyez sur Tab dans l\'éditeur pour insérer le caractère de tabulation. Activez ou désactivez ce comportement en appuyant sur {0}.",
		"Appuyez sur Tab dans l\'éditeur pour insérer le caractère de tabulation. La commande {0} ne peut pas être déclenchée par une combinaison de touches.",
		"Appuyez sur Commande+H maintenant pour ouvrir une fenêtre de navigateur avec plus d\'informations sur l\'accessibilité de l\'éditeur.",
		"Appuyez sur Contrôle+H maintenant pour ouvrir une fenêtre de navigateur avec plus d\'informations sur l\'accessibilité de l\'éditeur.",
		"Vous pouvez masquer cette info-bulle et revenir à l\'éditeur en appuyant sur Échap ou Maj+Échap.",
		"Afficher l\'aide sur l\'accessibilité",
		"Développeur : Inspecter les jetons",
		"Accéder à la ligne/colonne...",
		"Afficher tous les fournisseurs d\'accès rapide",
		"Palette de commandes",
		"Commandes d\'affichage et d\'exécution",
		"Accéder au symbole...",
		"Accéder au symbole par catégorie...",
		"Contenu de l\'éditeur",
		"Appuyez sur Alt+F1 pour voir les options d\'accessibilité.",
		"Activer/désactiver le thème à contraste élevé",
		"{0} modifications dans {1} fichiers",
	],
	"vs/editor/contrib/anchorSelect/browser/anchorSelect": [
		"Ancre de sélection",
		"Ancre définie sur {0}:{1}",
		"Définir l\'ancre de sélection",
		"Atteindre l\'ancre de sélection",
		"Sélectionner de l\'ancre au curseur",
		"Annuler l\'ancre de sélection",
	],
	"vs/editor/contrib/bracketMatching/browser/bracketMatching": [
		"Couleur du marqueur de la règle d\'aperçu pour rechercher des parenthèses.",
		"Atteindre le crochet",
		"Sélectionner jusqu\'au crochet",
		"Accéder au &&crochet",
	],
	"vs/editor/contrib/caretOperations/browser/caretOperations": [
		"Déplacer le texte sélectionné à gauche",
		"Déplacer le texte sélectionné à droite",
	],
	"vs/editor/contrib/caretOperations/browser/transpose": [
		"Transposer les lettres",
	],
	"vs/editor/contrib/clipboard/browser/clipboard": [
		"Co&&uper",
		"Couper",
		"Couper",
		"Couper",
		"&&Copier",
		"Copier",
		"Copier",
		"Copier",
		"Copier en tant que",
		"Copier en tant que",
		"Co&&ller",
		"Coller",
		"Coller",
		"Coller",
		"Copier avec la coloration syntaxique",
	],
	"vs/editor/contrib/codeAction/browser/codeActionCommands": [
		"Type d\'action de code à exécuter.",
		"Contrôle quand les actions retournées sont appliquées.",
		"Appliquez toujours la première action de code retournée.",
		"Appliquez la première action de code retournée si elle est la seule.",
		"N\'appliquez pas les actions de code retournées.",
		"Contrôle si seules les actions de code par défaut doivent être retournées.",
		"Une erreur inconnue s\'est produite à l\'application de l\'action du code",
		"Correction rapide...",
		"Aucune action de code disponible",
		"Aucune action de code préférée n\'est disponible pour \'{0}\'",
		"Aucune action de code disponible pour \'{0}\'",
		"Aucune action de code par défaut disponible",
		"Aucune action de code disponible",
		"Remanier...",
		"Aucune refactorisation par défaut disponible pour \'{0}\'",
		"Aucune refactorisation disponible pour \'{0}\'",
		"Aucune refactorisation par défaut disponible",
		"Aucune refactorisation disponible",
		"Action de la source",
		"Aucune action source par défaut disponible pour \'{0}\'",
		"Aucune action source disponible pour \'{0}\'",
		"Aucune action source par défaut disponible",
		"Aucune action n\'est disponible",
		"Organiser les importations",
		"Aucune action organiser les imports disponible",
		"Tout corriger",
		"Aucune action Tout corriger disponible",
		"Corriger automatiquement...",
		"Aucun correctif automatique disponible",
	],
	"vs/editor/contrib/codeAction/browser/lightBulbWidget": [
		"Afficher les actions de code. Correctif rapide disponible par défaut ({0})",
		"Afficher les actions de code ({0})",
		"Afficher les actions de code",
	],
	"vs/editor/contrib/codelens/browser/codelensController": [
		"Afficher les commandes Code Lens de la ligne actuelle",
	],
	"vs/editor/contrib/colorPicker/browser/colorPickerWidget": [
		"Cliquez pour activer/désactiver les options de couleur (rgb/hsl/hexadécimal).",
	],
	"vs/editor/contrib/comment/browser/comment": [
		"Activer/désactiver le commentaire de ligne",
		"Afficher/masquer le commen&&taire de ligne",
		"Ajouter le commentaire de ligne",
		"Supprimer le commentaire de ligne",
		"Activer/désactiver le commentaire de bloc",
		"Afficher/masquer le commentaire de &&bloc",
	],
	"vs/editor/contrib/contextmenu/browser/contextmenu": [
		"Afficher le menu contextuel de l\'éditeur",
	],
	"vs/editor/contrib/cursorUndo/browser/cursorUndo": [
		"Annulation du curseur",
		"Restauration du curseur",
	],
	"vs/editor/contrib/editorState/browser/keybindingCancellation": [
		"Indique si l\'éditeur exécute une opération annulable, par exemple \'Avoir un aperçu des références\'",
	],
	"vs/editor/contrib/find/browser/findController": [
		"Rechercher",
		"&&Rechercher",
		"Remplace l’indicateur « Utiliser une expression régulière ».\r\nL’indicateur ne sera pas enregistré à l’avenir.\r\n0 : Ne rien faire\r\n1 : Vrai\r\n2 : Faux",
		"Remplace l’indicateur « Match Whole Word ».\r\nL’indicateur ne sera pas enregistré à l’avenir.\r\n0 : Ne rien faire\r\n1 : Vrai\r\n2 : Faux",
		"Remplace l’indicateur « Cas mathématiques ».\r\nL’indicateur ne sera pas enregistré à l’avenir.\r\n0 : Ne rien faire\r\n1 : Vrai\r\n2 : Faux",
		"Remplace l’indicateur « Preserve Case ».\r\nL’indicateur ne sera pas enregistré à l’avenir.\r\n0 : Ne rien faire\r\n1 : Vrai\r\n2 : Faux",
		"Trouver avec des arguments",
		"Rechercher dans la sélection",
		"Rechercher suivant",
		"Rechercher précédent",
		"Sélection suivante",
		"Sélection précédente",
		"Remplacer",
		"&&Remplacer",
	],
	"vs/editor/contrib/find/browser/findWidget": [
		"Icône de l\'option Rechercher dans la sélection dans le widget de recherche de l\'éditeur.",
		"Icône permettant d\'indiquer que le widget de recherche de l\'éditeur est réduit.",
		"Icône permettant d\'indiquer que le widget de recherche de l\'éditeur est développé.",
		"Icône de l\'option Remplacer dans le widget de recherche de l\'éditeur.",
		"Icône de l\'option Tout remplacer dans le widget de recherche de l\'éditeur.",
		"Icône de l\'option Rechercher précédent dans le widget de recherche de l\'éditeur.",
		"Icône de l\'option Rechercher suivant dans le widget de recherche de l\'éditeur.",
		"Rechercher",
		"Rechercher",
		"Correspondance précédente",
		"Correspondance suivante",
		"Rechercher dans la sélection",
		"Fermer",
		"Remplacer",
		"Remplacer",
		"Remplacer",
		"Tout remplacer",
		"Activer/désactiver le remplacement",
		"Seuls les {0} premiers résultats sont mis en évidence, mais toutes les opérations de recherche fonctionnent sur l’ensemble du texte.",
		"{0} sur {1}",
		"Aucun résultat",
		"{0} trouvé(s)",
		"{0} trouvé pour \'{1}\'",
		"{0} trouvé pour \'{1}\', sur {2}",
		"{0} trouvé pour \'{1}\'",
		"La combinaison Ctrl+Entrée permet désormais d\'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour redéfinir le comportement.",
	],
	"vs/editor/contrib/folding/browser/folding": [
		"Le nombre de régions pliables est limité à un maximum de {0}. Augmentez l’option de configuration [\'Folding Maximum Regions\'](command:workbench.action.openSettings?[\"editor.foldingMaximumRegions\"]) pour en activer d’autres.",
		"Déplier",
		"Déplier de manière récursive",
		"Plier",
		"Activer/désactiver le pliage",
		"Plier de manière récursive",
		"Replier tous les commentaires de bloc",
		"Replier toutes les régions",
		"Déplier toutes les régions",
		"Plier toutes les régions sauf celles sélectionnées",
		"Déplier toutes les régions sauf celles sélectionnées",
		"Plier tout",
		"Déplier tout",
		"Atteindre le pli parent",
		"Accéder à la plage de pliage précédente",
		"Accéder à la plage de pliage suivante",
		"Niveau de pliage {0}",
		"Couleur d\'arrière-plan des gammes pliées. La couleur ne doit pas être opaque pour ne pas cacher les décorations sous-jacentes.",
		"Couleur du contrôle de pliage dans la marge de l\'éditeur.",
	],
	"vs/editor/contrib/folding/browser/foldingDecorations": [
		"Icône des plages développées dans la marge de glyphes de l\'éditeur.",
		"Icône des plages réduites dans la marge de glyphes de l\'éditeur.",
	],
	"vs/editor/contrib/fontZoom/browser/fontZoom": [
		"Agrandissement de l\'éditeur de polices de caractères",
		"Rétrécissement de l\'éditeur de polices de caractères",
		"Remise à niveau du zoom de l\'éditeur de polices de caractères",
	],
	"vs/editor/contrib/format/browser/format": [
		"1 modification de format effectuée à la ligne {0}",
		"{0} modifications de format effectuées à la ligne {1}",
		"1 modification de format effectuée entre les lignes {0} et {1}",
		"{0} modifications de format effectuées entre les lignes {1} et {2}",
	],
	"vs/editor/contrib/format/browser/formatActions": [
		"Mettre le document en forme",
		"Mettre la sélection en forme",
	],
	"vs/editor/contrib/gotoError/browser/gotoError": [
		"Aller au problème suivant (Erreur, Avertissement, Info)",
		"Icône du prochain marqueur goto.",
		"Aller au problème précédent (Erreur, Avertissement, Info)",
		"Icône du précédent marqueur goto.",
		"Aller au problème suivant dans Fichiers (Erreur, Avertissement, Info)",
		"&&Problème suivant",
		"Aller au problème précédent dans Fichiers (Erreur, Avertissement, Info)",
		"&&Problème précédent",
	],
	"vs/editor/contrib/gotoError/browser/gotoErrorWidget": [
		"Erreur",
		"Avertissement",
		"Info",
		"Conseil",
		"{0} à {1}. ",
		"{0} problèmes sur {1}",
		"{0} problème(s) sur {1}",
		"Couleur d\'erreur du widget de navigation dans les marqueurs de l\'éditeur.",
		"Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.",
		"Couleur d\'avertissement du widget de navigation dans les marqueurs de l\'éditeur.",
		"Arrière-plan du titre d’erreur du widget de navigation dans les marqueurs de l’éditeur.",
		"Couleur d’information du widget de navigation du marqueur de l\'éditeur.",
		"Arrière-plan du titre des informations du widget de navigation dans les marqueurs de l’éditeur.",
		"Arrière-plan du widget de navigation dans les marqueurs de l\'éditeur.",
	],
	"vs/editor/contrib/gotoSymbol/browser/goToCommands": [
		"Aperçu",
		"Définitions",
		"Définition introuvable pour \'{0}\'",
		"Définition introuvable",
		"Atteindre la définition",
		"Ouvrir la définition sur le côté",
		"Faire un Peek de la Définition",
		"Déclarations",
		"Aucune déclaration pour \'{0}\'",
		"Aucune déclaration",
		"Accéder à la déclaration",
		"Aucune déclaration pour \'{0}\'",
		"Aucune déclaration",
		"Aperçu de la déclaration",
		"Définitions de type",
		"Définition de type introuvable pour \'{0}\'",
		"Définition de type introuvable",
		"Atteindre la définition de type",
		"Aperçu de la définition du type",
		"Implémentations",
		"Implémentation introuvable pour \'{0}\'",
		"Implémentation introuvable",
		"Atteindre les implémentations",
		"Implémentations d\'aperçu",
		"Aucune référence pour \'{0}\'",
		"Aucune référence",
		"Atteindre les références",
		"Références",
		"Aperçu des références",
		"Références",
		"Atteindre un symbole",
		"Emplacements",
		"Aucun résultat pour « {0} »",
		"Références",
		"Atteindre la &&définition",
		"Atteindre la &&déclaration",
		"Accéder à la définition de &&type",
		"Atteindre les &&implémentations",
		"Atteindre les &&références",
	],
	"vs/editor/contrib/gotoSymbol/browser/link/goToDefinitionAtPosition": [
		"Cliquez pour afficher {0} définitions.",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesController": [
		"Indique si l\'aperçu des références est visible, par exemple via \'Avoir un aperçu des références\' ou \'Faire un peek de la définition\'",
		"Chargement en cours...",
		"{0} ({1})",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesTree": [
		"{0} références",
		"{0} référence",
		"Références",
	],
	"vs/editor/contrib/gotoSymbol/browser/peek/referencesWidget": [
		"aperçu non disponible",
		"Aucun résultat",
		"Références",
	],
	"vs/editor/contrib/gotoSymbol/browser/referencesModel": [
		"symbole dans {0} sur la ligne {1}, colonne {2}",
		"symbole dans {0} à la ligne {1}, colonne {2}, {3}",
		"1 symbole dans {0}, chemin complet {1}",
		"{0} symboles dans {1}, chemin complet {2}",
		"Résultats introuvables",
		"1 symbole dans {0}",
		"{0} symboles dans {1}",
		"{0} symboles dans {1} fichiers",
	],
	"vs/editor/contrib/gotoSymbol/browser/symbolNavigation": [
		"Indique s\'il existe des emplacements de symboles que vous pouvez parcourir à l\'aide du clavier uniquement.",
		"Symbole {0} sur {1}, {2} pour le suivant",
		"Symbole {0} sur {1}",
	],
	"vs/editor/contrib/hover/browser/hover": [
		"Afficher par pointage",
		"Afficher le pointeur de l\'aperçu de définition",
	],
	"vs/editor/contrib/hover/browser/markdownHoverParticipant": [
		"Chargement en cours...",
		"La tokenisation des lignes longues est ignorée pour des raisons de performances. Cela peut être configurée via \'editor.maxTokenizationLineLength\'.",
	],
	"vs/editor/contrib/hover/browser/markerHoverParticipant": [
		"Voir le problème",
		"Aucune solution disponible dans l\'immédiat",
		"Recherche de correctifs rapides...",
		"Aucune solution disponible dans l\'immédiat",
		"Correction rapide...",
	],
	"vs/editor/contrib/inPlaceReplace/browser/inPlaceReplace": [
		"Remplacer par la valeur précédente",
		"Remplacer par la valeur suivante",
	],
	"vs/editor/contrib/indentation/browser/indentation": [
		"Convertir les retraits en espaces",
		"Convertir les retraits en tabulations",
		"Taille des tabulations configurée",
		"Sélectionner la taille des tabulations pour le fichier actuel",
		"Mettre en retrait avec des tabulations",
		"Mettre en retrait avec des espaces",
		"Détecter la mise en retrait à partir du contenu",
		"Remettre en retrait les lignes",
		"Réindenter les lignes sélectionnées",
	],
	"vs/editor/contrib/inlineCompletions/browser/ghostTextController": [
		"Indique si une suggestion en ligne est visible",
		"Indique si la suggestion en ligne commence par un espace blanc",
		"Indique si la suggestion incluse commence par un espace blanc inférieur à ce qui serait inséré par l’onglet.",
		"Afficher la suggestion en ligne suivante",
		"Afficher la suggestion en ligne précédente",
		"Déclencher la suggestion en ligne",
	],
	"vs/editor/contrib/inlineCompletions/browser/inlineCompletionsHoverParticipant": [
		"Suivant",
		"Précédent",
		"Accepter",
		"Suggestion :",
	],
	"vs/editor/contrib/lineSelection/browser/lineSelection": [
		"Développer la sélection de ligne",
	],
	"vs/editor/contrib/linesOperations/browser/linesOperations": [
		"Copier la ligne en haut",
		"&&Copier la ligne en haut",
		"Copier la ligne en bas",
		"Co&&pier la ligne en bas",
		"Dupliquer la sélection",
		"&&Dupliquer la sélection",
		"Déplacer la ligne vers le haut",
		"Déplacer la ligne &&vers le haut",
		"Déplacer la ligne vers le bas",
		"Déplacer la &&ligne vers le bas",
		"Trier les lignes dans l\'ordre croissant",
		"Trier les lignes dans l\'ordre décroissant",
		"Supprimer les lignes dupliquées",
		"Découper l\'espace blanc de fin",
		"Supprimer la ligne",
		"Mettre en retrait la ligne",
		"Ajouter un retrait négatif à la ligne",
		"Insérer une ligne au-dessus",
		"Insérer une ligne sous",
		"Supprimer tout ce qui est à gauche",
		"Supprimer tout ce qui est à droite",
		"Joindre les lignes",
		"Transposer les caractères autour du curseur",
		"Transformer en majuscule",
		"Transformer en minuscule",
		"Appliquer la casse \"1re lettre des mots en majuscule\"",
		"Transformer en snake case",
	],
	"vs/editor/contrib/linkedEditing/browser/linkedEditing": [
		"Démarrer la modification liée",
		"Couleur d\'arrière-plan quand l\'éditeur renomme automatiquement le type.",
	],
	"vs/editor/contrib/links/browser/links": [
		"Exécuter la commande",
		"suivre le lien",
		"cmd + clic",
		"ctrl + clic",
		"option + clic",
		"alt + clic",
		"Exécuter la commande {0}",
		"Échec de l\'ouverture de ce lien, car il n\'est pas bien formé : {0}",
		"Échec de l\'ouverture de ce lien, car sa cible est manquante.",
		"Ouvrir le lien",
	],
	"vs/editor/contrib/message/browser/messageController": [
		"Indique si l\'éditeur affiche un message inline",
		"Impossible de modifier dans l’éditeur en lecture seule",
	],
	"vs/editor/contrib/multicursor/browser/multicursor": [
		"Curseur ajouté : {0}",
		"Curseurs ajoutés : {0}",
		"Ajouter un curseur au-dessus",
		"&&Ajouter un curseur au-dessus",
		"Ajouter un curseur en dessous",
		"Aj&&outer un curseur en dessous",
		"Ajouter des curseurs à la fin des lignes",
		"Ajouter des c&&urseurs à la fin des lignes",
		"Ajouter des curseurs en bas",
		"Ajouter des curseurs en haut",
		"Ajouter la sélection à la correspondance de recherche suivante",
		"Ajouter l\'occurrence suiva&&nte",
		"Ajouter la sélection à la correspondance de recherche précédente",
		"Ajouter l\'occurrence p&&récédente",
		"Déplacer la dernière sélection vers la correspondance de recherche suivante",
		"Déplacer la dernière sélection à la correspondance de recherche précédente",
		"Sélectionner toutes les occurrences des correspondances de la recherche",
		"Sélectionner toutes les &&occurrences",
		"Modifier toutes les occurrences",
	],
	"vs/editor/contrib/parameterHints/browser/parameterHints": [
		"Indicateurs des paramètres Trigger",
	],
	"vs/editor/contrib/parameterHints/browser/parameterHintsWidget": [
		"Icône d\'affichage du prochain conseil de paramètre.",
		"Icône d\'affichage du précédent conseil de paramètre.",
		"{0}, conseil",
		"Couleur de premier plan de l’élément actif dans l’indicateur de paramètre.",
	],
	"vs/editor/contrib/peekView/browser/peekView": [
		"Indique si l\'éditeur de code actuel est intégré à l\'aperçu",
		"Fermer",
		"Couleur d\'arrière-plan de la zone de titre de l\'affichage d\'aperçu.",
		"Couleur du titre de l\'affichage d\'aperçu.",
		"Couleur des informations sur le titre de l\'affichage d\'aperçu.",
		"Couleur des bordures et de la flèche de l\'affichage d\'aperçu.",
		"Couleur d\'arrière-plan de la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur de premier plan des noeuds de lignes dans la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur de premier plan des noeuds de fichiers dans la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur d\'arrière-plan de l\'entrée sélectionnée dans la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur de premier plan de l\'entrée sélectionnée dans la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur d\'arrière-plan de l\'éditeur d\'affichage d\'aperçu.",
		"Couleur d\'arrière-plan de la bordure de l\'éditeur d\'affichage d\'aperçu.",
		"Couleur de mise en surbrillance d\'une correspondance dans la liste des résultats de l\'affichage d\'aperçu.",
		"Couleur de mise en surbrillance d\'une correspondance dans l\'éditeur de l\'affichage d\'aperçu.",
		"Bordure de mise en surbrillance d\'une correspondance dans l\'éditeur de l\'affichage d\'aperçu.",
	],
	"vs/editor/contrib/quickAccess/browser/gotoLineQuickAccess": [
		"Ouvrez d\'abord un éditeur de texte pour accéder à une ligne.",
		"Atteindre la ligne {0} et le caractère {1}.",
		"Accédez à la ligne {0}.",
		"Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne entre 1 et {2} auquel accéder.",
		"Ligne actuelle : {0}, caractère : {1}. Tapez un numéro de ligne auquel accéder.",
	],
	"vs/editor/contrib/quickAccess/browser/gotoSymbolQuickAccess": [
		"Pour accéder à un symbole, ouvrez d\'abord un éditeur de texte avec des informations de symbole.",
		"L\'éditeur de texte actif ne fournit pas les informations de symbole.",
		"Aucun symbole d\'éditeur correspondant",
		"Aucun symbole d\'éditeur",
		"Ouvrir sur le côté",
		"Ouvrir en bas",
		"symboles ({0})",
		"propriétés ({0})",
		"méthodes ({0})",
		"fonctions ({0})",
		"constructeurs ({0})",
		"variables ({0})",
		"classes ({0})",
		"structs ({0})",
		"événements ({0})",
		"opérateurs ({0})",
		"interfaces ({0})",
		"espaces de noms ({0})",
		"packages ({0})",
		"paramètres de type ({0})",
		"modules ({0})",
		"propriétés ({0})",
		"énumérations ({0})",
		"membres d\'énumération ({0})",
		"chaînes ({0})",
		"fichiers ({0})",
		"tableaux ({0})",
		"nombres ({0})",
		"booléens ({0})",
		"objets ({0})",
		"clés ({0})",
		"champs ({0})",
		"constantes ({0})",
	],
	"vs/editor/contrib/rename/browser/rename": [
		"Aucun résultat.",
		"Une erreur inconnue s\'est produite lors de la résolution de l\'emplacement de renommage",
		"Renommage de \'{0}\'",
		"Changement du nom de {0}",
		"\'{0}\' renommé en \'{1}\'. Récapitulatif : {2}",
		"Le renommage n\'a pas pu appliquer les modifications",
		"Le renommage n\'a pas pu calculer les modifications",
		"Renommer le symbole",
		"Activer/désactiver la possibilité d\'afficher un aperçu des changements avant le renommage",
	],
	"vs/editor/contrib/rename/browser/renameInputField": [
		"Indique si le widget de renommage d\'entrée est visible",
		"Renommez l\'entrée. Tapez le nouveau nom et appuyez sur Entrée pour valider.",
		"{0} pour renommer, {1} pour afficher un aperçu",
	],
	"vs/editor/contrib/smartSelect/browser/smartSelect": [
		"Étendre la sélection",
		"Dév&&elopper la sélection",
		"Réduire la sélection",
		"&&Réduire la sélection",
	],
	"vs/editor/contrib/snippet/browser/snippetController2": [
		"Indique si l\'éditeur est actualisé en mode extrait",
		"Indique s\'il existe un taquet de tabulation suivant en mode extrait",
		"Indique s\'il existe un taquet de tabulation précédent en mode extrait",
	],
	"vs/editor/contrib/snippet/browser/snippetVariables": [
		"Dimanche",
		"Lundi",
		"Mardi",
		"Mercredi",
		"Jeudi",
		"Vendredi",
		"Samedi",
		"Dim",
		"Lun",
		"Mar",
		"Mer",
		"Jeu",
		"Ven",
		"Sam",
		"Janvier",
		"Février",
		"Mars",
		"Avril",
		"Mai",
		"Juin",
		"Juillet",
		"Août",
		"Septembre",
		"Octobre",
		"Novembre",
		"Décembre",
		"Jan",
		"Fév",
		"Mar",
		"Avr",
		"Mai",
		"Juin",
		"Jul",
		"Aoû",
		"Sept",
		"Oct",
		"Nov",
		"Déc",
	],
	"vs/editor/contrib/suggest/browser/suggest": [
		"Indique si les suggestions sont visibles",
		"Indique si les détails des suggestions sont visibles",
		"Indique s\'il existe plusieurs suggestions au choix",
		"Indique si l\'insertion de la suggestion actuelle entraîne un changement ou si tout a déjà été tapé",
		"Indique si les suggestions sont insérées quand vous appuyez sur Entrée",
		"Indique si la suggestion actuelle a un comportement d\'insertion et de remplacement",
		"Indique si le comportement par défaut consiste à insérer ou à remplacer",
		"Indique si la suggestion actuelle prend en charge la résolution des détails supplémentaires",
	],
	"vs/editor/contrib/suggest/browser/suggestController": [
		"L\'acceptation de \'{0}\' a entraîné {1} modifications supplémentaires",
		"Suggestions pour Trigger",
		"Insérer",
		"Insérer",
		"Remplacer",
		"Remplacer",
		"Insérer",
		"afficher moins",
		"afficher plus",
		"Réinitialiser la taille du widget de suggestion",
	],
	"vs/editor/contrib/suggest/browser/suggestWidget": [
		"Couleur d\'arrière-plan du widget de suggestion.",
		"Couleur de bordure du widget de suggestion.",
		"Couleur de premier plan du widget de suggestion.",
		"Couleur de premier plan de l’entrée sélectionnée dans le widget de suggestion.",
		"Couleur de premier plan de l’icône de l’entrée sélectionnée dans le widget de suggestion.",
		"Couleur d\'arrière-plan de l\'entrée sélectionnée dans le widget de suggestion.",
		"Couleur de la surbrillance des correspondances dans le widget de suggestion.",
		"Couleur des mises en surbrillance dans le widget de suggestion lorsqu’un élément a le focus.",
		"Couleur de premier plan du statut du widget de suggestion.",
		"Chargement en cours...",
		"Pas de suggestions.",
		"Suggérer",
		"({0}, {1}) {2}",
		"{0}{1}",
		"{0}, {1}",
		"{0}, documents : {1}",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetDetails": [
		"Fermer",
		"Chargement en cours...",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetRenderer": [
		"Icône d\'affichage d\'informations supplémentaires dans le widget de suggestion.",
		"Lire la suite",
	],
	"vs/editor/contrib/suggest/browser/suggestWidgetStatus": [
		"{0} ({1})",
	],
	"vs/editor/contrib/symbolIcons/browser/symbolIcons": [
		"Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles booléens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de membre d\'énumérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'événement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de mot clé. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de méthode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'opérateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de propriété. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de référence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de chaîne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de paramètre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles d\'unité. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
		"Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.",
	],
	"vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode": [
		"Activer/désactiver l\'utilisation de la touche Tab pour déplacer le focus",
		"Appuyer sur Tab déplacera le focus vers le prochain élément pouvant être désigné comme élément actif",
		"Appuyer sur Tab insérera le caractère de tabulation",
	],
	"vs/editor/contrib/tokenization/browser/tokenization": [
		"Développeur : forcer la retokenisation",
	],
	"vs/editor/contrib/unicodeHighlighter/browser/unicodeHighlighter": [
		"Icône affichée avec un message d\'avertissement dans l\'éditeur d\'extensions.",
		"Ce document contient de nombreux caractères Unicode ASCII non basiques.",
		"Ce document contient de nombreux caractères Unicode ambigus.",
		"Ce document contient de nombreux caractères Unicode invisibles.",
		"Le caractère {0} peut être confus avec le caractère {1}, ce qui est plus courant dans le code source.",
		"Le caractère {0} est invisible.",
		"Le caractère {0} n’est pas un caractère ASCII de base.",
		"Ajuster les paramètres",
		"Désactiver la mise en surbrillance dans les commentaires",
		"Désactiver la mise en surbrillance des caractères dans les commentaires",
		"Désactiver la mise en surbrillance dans les chaînes",
		"Désactiver la mise en surbrillance des caractères dans les chaînes",
		"Désactiver la mise en surbrillance ambiguë",
		"Désactiver la mise en surbrillance des caractères ambigus",
		"Désactiver le surlignage invisible",
		"Désactiver la mise en surbrillance des caractères invisibles",
		"Désactiver la mise en surbrillance non ASCII",
		"Désactiver la mise en surbrillance des caractères ASCII non de base",
		"Afficher les options d’exclusion",
		"Exclure la mise en surbrillance des {0} (caractère invisible)",
		"Exclure {0} de la mise en surbrillance",
		"Autoriser les caractères Unicode plus courants dans le langage \"{0}\"",
		"Configurer les options de surlignage Unicode",
	],
	"vs/editor/contrib/unusualLineTerminators/browser/unusualLineTerminators": [
		"Marques de fin de ligne inhabituelles",
		"Marques de fin de ligne inhabituelles détectées",
		"Le fichier « {0} »contient un ou plusieurs caractères de fin de ligne inhabituels, par exemple le séparateur de ligne (LS) ou le séparateur de paragraphe (PS).\r\n\r\nIl est recommandé de les supprimer du fichier. Vous pouvez configurer ce comportement par le biais de `editor.unusualLineTerminators`.",
		"Supprimer les marques de fin de ligne inhabituelles",
		"Ignorer",
	],
	"vs/editor/contrib/wordHighlighter/browser/wordHighlighter": [
		"Couleur d\'arrière-plan d\'un symbole pendant l\'accès en lecture, comme la lecture d\'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur d\'arrière-plan d\'un symbole pendant l\'accès en écriture, comme l\'écriture d\'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de bordure d\'un symbole durant l\'accès en lecture, par exemple la lecture d\'une variable.",
		"Couleur de bordure d\'un symbole durant l\'accès en écriture, par exemple l\'écriture dans une variable.",
		"Couleur de marqueur de la règle d\'aperçu pour la mise en surbrillance des symboles. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de marqueur de la règle d\'aperçu pour la mise en surbrillance des symboles d\'accès en écriture. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Aller à la prochaine mise en évidence de symbole",
		"Aller à la mise en évidence de symbole précédente",
		"Déclencher la mise en évidence de symbole",
	],
	"vs/editor/contrib/wordOperations/browser/wordOperations": [
		"Supprimer le mot",
	],
	"vs/platform/actions/browser/menuEntryActionViewItem": [
		"{0} ({1})",
		"{0} ({1})",
	],
	"vs/platform/configuration/common/configurationRegistry": [
		"Substitutions de configuration du langage par défaut",
		"Configurez les paramètres à remplacer pour le langage {0}.",
		"Configurez les paramètres d\'éditeur à remplacer pour un langage.",
		"Ce paramètre ne prend pas en charge la configuration par langage.",
		"Configurez les paramètres d\'éditeur à remplacer pour un langage.",
		"Ce paramètre ne prend pas en charge la configuration par langage.",
		"Impossible d\'inscrire une propriété vide",
		"Impossible d\'inscrire \'{0}\'. Ceci correspond au modèle de propriété \'\\\\[.*\\\\]$\' permettant de décrire les paramètres d\'éditeur spécifiques à un langage. Utilisez la contribution \'configurationDefaults\'.",
		"Impossible d\'inscrire \'{0}\'. Cette propriété est déjà inscrite.",
	],
	"vs/platform/contextkey/browser/contextKeyService": [
		"Commande qui retourne des informations sur les clés de contexte",
	],
	"vs/platform/contextkey/common/contextkeys": [
		"Indique si le système d\'exploitation est macOS",
		"Indique si le système d\'exploitation est Linux",
		"Indique si le système d\'exploitation est Windows",
		"Indique si la plateforme est un navigateur web",
		"Indique si le système d\'exploitation est macOS sur une plateforme qui n\'est pas un navigateur",
		"Indique si le système d’exploitation est Linux",
		"Indique si le focus clavier se trouve dans une zone d\'entrée",
	],
	"vs/platform/keybinding/common/abstractKeybindingService": [
		"Touche ({0}) utilisée. En attente d\'une seconde touche...",
		"La combinaison de touches ({0}, {1}) n’est pas une commande.",
	],
	"vs/platform/list/browser/listService": [
		"Banc d\'essai",
		"Mappe vers \'Contrôle\' dans Windows et Linux, et vers \'Commande\' dans macOS.",
		"Mappe vers \'Alt\' dans Windows et Linux, et vers \'Option\' dans macOS.",
		"Le modificateur à utiliser pour ajouter un élément dans les arbres et listes pour une sélection multiple avec la souris (par exemple dans l’Explorateur, les éditeurs ouverts et la vue scm). Les mouvements de la souris \'Ouvrir à côté\' (si pris en charge) s\'adapteront tels qu’ils n\'entrent pas en conflit avec le modificateur multiselect.",
		"Contrôle l\'ouverture des éléments dans les arborescences et les listes à l\'aide de la souris (si cela est pris en charge). Notez que certaines arborescences et listes peuvent choisir d\'ignorer ce paramètre, s\'il est non applicable.",
		"Contrôle si les listes et les arborescences prennent en charge le défilement horizontal dans le banc d\'essai. Avertissement : L\'activation de ce paramètre a un impact sur les performances.",
		"Contrôle la mise en retrait de l\'arborescence, en pixels.",
		"Contrôle si l\'arborescence doit afficher les repères de mise en retrait.",
		"Détermine si les listes et les arborescences ont un défilement fluide.",
		"Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.",
		"Multiplicateur de vitesse de défilement quand vous appuyez sur \'Alt\'.",
		"La navigation au clavier Simple place le focus sur les éléments qui correspondent à l\'entrée de clavier. La mise en correspondance est effectuée sur les préfixes uniquement.",
		"La navigation de mise en surbrillance au clavier met en surbrillance les éléments qui correspondent à l\'entrée de clavier. La navigation ultérieure vers le haut ou vers le bas parcourt uniquement les éléments mis en surbrillance.",
		"La navigation au clavier Filtrer filtre et masque tous les éléments qui ne correspondent pas à l\'entrée de clavier.",
		"Contrôle le style de navigation au clavier pour les listes et les arborescences dans le banc d\'essai. Les options sont Simple, Mise en surbrillance et Filtrer.",
		"Contrôle si la navigation au clavier dans les listes et les arborescences est automatiquement déclenchée simplement par la frappe. Si défini sur \'false\', la navigation au clavier est seulement déclenchée avec l\'exécution de la commande \'list.toggleKeyboardNavigation\', à laquelle vous pouvez attribuer un raccourci clavier.",
		"Contrôle la façon dont les dossiers de l\'arborescence sont développés quand vous cliquez sur les noms de dossiers. Notez que certaines arborescences et listes peuvent choisir d\'ignorer ce paramètre, s\'il est non applicable.",
	],
	"vs/platform/markers/common/markers": [
		"Erreur",
		"Avertissement",
		"Info",
	],
	"vs/platform/quickinput/browser/commandsQuickAccess": [
		"{0}, {1}",
		"récemment utilisées",
		"autres commandes",
		"La commande \'{0}\' a entraîné une erreur ({1})",
	],
	"vs/platform/quickinput/browser/helpQuickAccess": [
		"commandes globales",
		"commandes de l\'éditeur",
		"{0}, {1}",
	],
	"vs/platform/theme/common/colorRegistry": [
		"Couleur de premier plan globale. Cette couleur est utilisée si elle n\'est pas remplacée par un composant.",
		"Couleur principale de premier plan pour les messages d\'erreur. Cette couleur est utilisée uniquement si elle n\'est pas redéfinie par un composant.",
		"Couleur de premier plan du texte descriptif fournissant des informations supplémentaires, par exemple pour un label.",
		"Couleur par défaut des icônes du banc d\'essai.",
		"Couleur de bordure globale des éléments ayant le focus. Cette couleur est utilisée si elle n\'est pas remplacée par un composant.",
		"Bordure supplémentaire autour des éléments pour les séparer des autres et obtenir un meilleur contraste.",
		"Bordure supplémentaire autour des éléments actifs pour les séparer des autres et obtenir un meilleur contraste.",
		"La couleur d\'arrière-plan des sélections de texte dans le banc d\'essai (par ex., pour les champs d\'entrée ou les zones de texte). Notez que cette couleur ne s\'applique pas aux sélections dans l\'éditeur et le terminal.",
		"Couleur pour les séparateurs de texte.",
		"Couleur des liens dans le texte.",
		"Couleur de premier plan pour les liens dans le texte lorsqu\'ils sont cliqués ou survolés.",
		"Couleur des segments de texte préformatés.",
		"Couleur d\'arrière-plan des citations dans le texte.",
		"Couleur de bordure des citations dans le texte.",
		"Couleur d\'arrière-plan des blocs de code dans le texte.",
		"Couleur de l\'ombre des widgets, comme rechercher/remplacer, au sein de l\'éditeur.",
		"Arrière-plan de la zone d\'entrée.",
		"Premier plan de la zone d\'entrée.",
		"Bordure de la zone d\'entrée.",
		"Couleur de la bordure des options activées dans les champs d\'entrée.",
		"Couleur d\'arrière-plan des options activées dans les champs d\'entrée.",
		"Couleur de pointage d’arrière-plan des options dans les champs d’entrée.",
		"Couleur de premier plan des options activées dans les champs d\'entrée.",
		"Couleur de premier plan de la zone d\'entrée pour le texte d\'espace réservé.",
		"Couleur d\'arrière-plan de la validation d\'entrée pour la gravité des informations.",
		"Couleur de premier plan de validation de saisie pour la sévérité Information.",
		"Couleur de bordure de la validation d\'entrée pour la gravité des informations.",
		"Couleur d\'arrière-plan de la validation d\'entrée pour la gravité de l\'avertissement.",
		"Couleur de premier plan de la validation de la saisie pour la sévérité Avertissement.",
		"Couleur de bordure de la validation d\'entrée pour la gravité de l\'avertissement.",
		"Couleur d\'arrière-plan de la validation d\'entrée pour la gravité de l\'erreur.",
		"Couleur de premier plan de la validation de saisie pour la sévérité Erreur.",
		"Couleur de bordure de la validation d\'entrée pour la gravité de l\'erreur. ",
		"Arrière-plan de la liste déroulante.",
		"Arrière-plan de la liste déroulante.",
		"Premier plan de la liste déroulante.",
		"Bordure de la liste déroulante.",
		"Couleur de fond du widget Case à cocher.",
		"Couleur de premier plan du widget Case à cocher.",
		"Couleur de bordure du widget Case à cocher.",
		"Couleur de premier plan du bouton.",
		"Couleur d\'arrière-plan du bouton.",
		"Couleur d\'arrière-plan du bouton pendant le pointage.",
		"Couleur de bordure du bouton.",
		"Couleur de premier plan du bouton secondaire.",
		"Couleur d\'arrière-plan du bouton secondaire.",
		"Couleur d\'arrière-plan du bouton secondaire au moment du pointage.",
		"Couleur de fond des badges. Les badges sont de courts libellés d\'information, ex. le nombre de résultats de recherche.",
		"Couleur des badges. Les badges sont de courts libellés d\'information, ex. le nombre de résultats de recherche.",
		"Ombre de la barre de défilement pour indiquer que la vue défile.",
		"Couleur de fond du curseur de la barre de défilement.",
		"Couleur de fond du curseur de la barre de défilement lors du survol.",
		"Couleur d’arrière-plan de la barre de défilement lorsqu\'on clique dessus.",
		"Couleur de fond pour la barre de progression qui peut s\'afficher lors d\'opérations longues.",
		"Couleur d\'arrière-plan du texte d\'erreur dans l\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.",
		"Couleur de premier plan de la ligne ondulée marquant les erreurs dans l\'éditeur.",
		"Couleur de bordure des zones d\'erreur dans l\'éditeur.",
		"Couleur d\'arrière-plan du texte d\'avertissement dans l\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.",
		"Couleur de premier plan de la ligne ondulée marquant les avertissements dans l\'éditeur.",
		"Couleur de bordure des zones d\'avertissement dans l\'éditeur.",
		"Couleur d\'arrière-plan du texte d\'information dans l\'éditeur. La couleur ne doit pas être opaque pour ne pas masquer les décorations sous-jacentes.",
		"Couleur de premier plan de la ligne ondulée marquant les informations dans l\'éditeur.",
		"Couleur de bordure des zones d\'informations dans l\'éditeur.",
		"Couleur de premier plan de la ligne ondulée d\'indication dans l\'éditeur.",
		"Couleur de bordure des zones d\'indication dans l\'éditeur.",
		"Couleur de bordure des fenêtres coulissantes.",
		"Couleur d\'arrière-plan de l\'éditeur.",
		"Couleur de premier plan par défaut de l\'éditeur.",
		"Couleur d\'arrière-plan des gadgets de l\'éditeur tels que rechercher/remplacer.",
		"Couleur de premier plan des widgets de l\'éditeur, notamment Rechercher/remplacer.",
		"Couleur de bordure des widgets de l\'éditeur. La couleur est utilisée uniquement si le widget choisit d\'avoir une bordure et si la couleur n\'est pas remplacée par un widget.",
		"Couleur de bordure de la barre de redimensionnement des widgets de l\'éditeur. La couleur est utilisée uniquement si le widget choisit une bordure de redimensionnement et si la couleur n\'est pas remplacée par un widget.",
		"Couleur d\'arrière-plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.",
		"Couleur de premier plan du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.",
		"Couleur d\'arrière-plan du titre du sélecteur rapide. Le widget de sélecteur rapide est le conteneur de sélecteurs comme la palette de commandes.",
		"Couleur du sélecteur rapide pour les étiquettes de regroupement.",
		"Couleur du sélecteur rapide pour les bordures de regroupement.",
		"Couleur d’arrière-plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.",
		"Couleur de premier plan d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.",
		"Couleur de bordure de la combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.",
		"Couleur de bordure du bas d’étiquette de combinaison de touches. L’étiquette est utilisée pour représenter un raccourci clavier.",
		"Couleur de la sélection de l\'éditeur.",
		"Couleur du texte sélectionné pour le contraste élevé.",
		"Couleur de la sélection dans un éditeur inactif. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur des régions dont le contenu est le même que celui de la sélection. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de bordure des régions dont le contenu est identique à la sélection.",
		"Couleur du résultat de recherche actif.",
		"Couleur des autres correspondances de recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de bordure du résultat de recherche actif.",
		"Couleur de bordure des autres résultats de recherche.",
		"Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur des correspondances de requête de l\'éditeur de recherche.",
		"Couleur de bordure des correspondances de requête de l\'éditeur de recherche.",
		"Surlignage sous le mot sélectionné par pointage. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur d\'arrière-plan du pointage de l\'éditeur.",
		"Couleur de premier plan du pointage de l\'éditeur.",
		"Couleur de bordure du pointage de l\'éditeur.",
		"Couleur d\'arrière-plan de la barre d\'état du pointage de l\'éditeur.",
		"Couleur des liens actifs.",
		"Couleur de premier plan des indicateurs inline",
		"Couleur d\'arrière-plan des indicateurs inline",
		"Couleur de premier plan des indicateurs inline pour les types",
		"Couleur d\'arrière-plan des indicateurs inline pour les types",
		"Couleur de premier plan des indicateurs inline pour les paramètres",
		"Couleur d\'arrière-plan des indicateurs inline pour les paramètres",
		"Couleur utilisée pour l\'icône d\'ampoule suggérant des actions.",
		"Couleur utilisée pour l\'icône d\'ampoule suggérant des actions de correction automatique.",
		"Couleur d\'arrière-plan du texte inséré. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur d\'arrière-plan du texte supprimé. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de contour du texte inséré.",
		"Couleur de contour du texte supprimé.",
		"Couleur de bordure entre les deux éditeurs de texte.",
		"Couleur du remplissage diagonal de l\'éditeur de différences. Le remplissage diagonal est utilisé dans les vues de différences côte à côte.",
		"Couleur d\'arrière-plan de la liste/l\'arborescence pour l\'élément ayant le focus quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de premier plan de la liste/l\'arborescence pour l\'élément ayant le focus quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de contour de la liste/l\'arborescence pour l\'élément ayant le focus quand la liste/l\'arborescence est active. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.",
		"Couleur d\'arrière-plan de la liste/l\'arborescence de l\'élément sélectionné quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de premier plan de la liste/l\'arborescence pour l\'élément sélectionné quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de premier plan de l’icône Liste/l\'arborescence pour l\'élément sélectionné quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur d\'arrière-plan de la liste/l\'arborescence pour l\'élément sélectionné quand la liste/l\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de premier plan de la liste/l\'arborescence pour l\'élément sélectionné quand la liste/l\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur de premier plan de l’icône Liste/l\'arborescence pour l\'élément sélectionné quand la liste/l\'arborescence est inactive. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l\'est pas quand elle est inactive.",
		"Couleur d\'arrière-plan de la liste/l\'arborescence pour l\'élément ayant le focus quand la liste/l\'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier (elle ne l\'est pas quand elle est inactive).",
		"Couleur de contour de la liste/l\'arborescence pour l\'élément ayant le focus quand la liste/l\'arborescence est inactive. Une liste/arborescence active a le focus clavier, contrairement à une liste/arborescence inactive.",
		"Arrière-plan de la liste/l\'arborescence pendant le pointage sur des éléments avec la souris.",
		"Premier plan de la liste/l\'arborescence pendant le pointage sur des éléments avec la souris.",
		"Arrière-plan de l\'opération de glisser-déplacer dans une liste/arborescence pendant le déplacement d\'éléments avec la souris.",
		"Couleur de premier plan dans la liste/l\'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.",
		"Couleur de premier plan de la liste ou l’arborescence pour la surbrillance des correspondances sur les éléments ayant le focus pendant la recherche dans une liste/arborescence.",
		"Couleur de premier plan de liste/arbre pour les éléments non valides, par exemple une racine non résolue dans l’Explorateur.",
		"Couleur de premier plan des éléments de la liste contenant des erreurs.",
		"Couleur de premier plan des éléments de liste contenant des avertissements.",
		"Couleur d\'arrière-plan du widget de filtre de type dans les listes et les arborescences.",
		"Couleur de contour du widget de filtre de type dans les listes et les arborescences.",
		"Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l\'absence de correspondance.",
		"Couleur d\'arrière-plan de la correspondance filtrée.",
		"Couleur de bordure de la correspondance filtrée.",
		"Couleur de trait de l\'arborescence pour les repères de mise en retrait.",
		"Couleur de la bordure du tableau entre les colonnes.",
		"Couleur d\'arrière-plan pour les lignes de tableau impaires.",
		"Couleur de premier plan de la liste/l\'arborescence des éléments atténués.",
		"Utilisez quickInputList.focusBackground à la place",
		"Couleur de premier plan du sélecteur rapide pour l’élément ayant le focus.",
		"Couleur de premier plan de l’icône du sélecteur rapide pour l’élément ayant le focus.",
		"Couleur d\'arrière-plan du sélecteur rapide pour l\'élément ayant le focus.",
		"Couleur de bordure des menus.",
		"Couleur de premier plan des éléments de menu.",
		"Couleur d\'arrière-plan des éléments de menu.",
		"Couleur de premier plan de l\'élément de menu sélectionné dans les menus.",
		"Couleur d\'arrière-plan de l\'élément de menu sélectionné dans les menus.",
		"Couleur de bordure de l\'élément de menu sélectionné dans les menus.",
		"Couleur d\'un élément de menu séparateur dans les menus.",
		"Arrière-plan de la barre d’outils lors du survol des actions à l’aide de la souris",
		"Contour de la barre d’outils lors du survol des actions à l’aide de la souris",
		"Arrière-plan de la barre d’outils quand la souris est maintenue sur des actions",
		"Couleur d’arrière-plan de mise en surbrillance d’un extrait tabstop.",
		"Couleur de bordure de mise en surbrillance d’un extrait tabstop.",
		"Couleur d’arrière-plan de mise en surbrillance du tabstop final d’un extrait.",
		"Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d\'un extrait de code.",
		"Couleur des éléments de navigation avec le focus.",
		"Couleur de fond des éléments de navigation.",
		"Couleur des éléments de navigation avec le focus.",
		"Couleur des éléments de navigation sélectionnés.",
		"Couleur de fond du sélecteur d’élément de navigation.",
		"Arrière-plan d\'en-tête actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Arrière-plan de contenu actuel dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Arrière-plan d\'en-tête entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Arrière-plan de contenu entrant dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Arrière-plan d\'en-tête de l\'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Arrière-plan de contenu de l\'ancêtre commun dans les conflits de fusion inline. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de bordure des en-têtes et du séparateur dans les conflits de fusion inline.",
		"Premier plan de la règle d\'aperçu actuelle pour les conflits de fusion inline.",
		"Premier plan de la règle d\'aperçu entrante pour les conflits de fusion inline.",
		"Arrière-plan de la règle d\'aperçu de l\'ancêtre commun dans les conflits de fusion inline.",
		"Couleur de marqueur de la règle d\'aperçu pour rechercher les correspondances. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de marqueur de la règle d\'aperçu pour la mise en surbrillance des sélections. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.",
		"Couleur de marqueur de la minimap pour les correspondances.",
		"Couleur de marqueur minimap pour les sélections répétées de l’éditeur.",
		"Couleur de marqueur du minimap pour la sélection de l\'éditeur.",
		"Couleur de marqueur de minimap pour les erreurs.",
		"Couleur de marqueur de minimap pour les avertissements.",
		"Couleur d\'arrière-plan du minimap.",
		"Opacité des éléments de premier plan rendus dans la minimap. Par exemple, « #000000c0 » affiche les éléments avec une opacité de 75 %.",
		"Couleur d\'arrière-plan du curseur de minimap.",
		"Couleur d\'arrière-plan du curseur de minimap pendant le survol.",
		"Couleur d\'arrière-plan du curseur de minimap pendant un clic.",
		"Couleur utilisée pour l\'icône d\'erreur des problèmes.",
		"Couleur utilisée pour l\'icône d\'avertissement des problèmes.",
		"Couleur utilisée pour l\'icône d\'informations des problèmes.",
		"Couleur de premier plan utilisée dans les graphiques.",
		"Couleur utilisée pour les lignes horizontales dans les graphiques.",
		"Couleur rouge utilisée dans les visualisations de graphiques.",
		"Couleur bleue utilisée dans les visualisations de graphiques.",
		"Couleur jaune utilisée dans les visualisations de graphiques.",
		"Couleur orange utilisée dans les visualisations de graphiques.",
		"Couleur verte utilisée dans les visualisations de graphiques.",
		"Couleur violette utilisée dans les visualisations de graphiques.",
	],
	"vs/platform/theme/common/iconRegistry": [
		"ID de la police à utiliser. Si aucune valeur n\'est définie, la police définie en premier est utilisée.",
		"Caractère de police associé à la définition d\'icône.",
		"Icône de l\'action de fermeture dans les widgets.",
		"Icône d\'accès à l\'emplacement précédent de l\'éditeur.",
		"Icône d\'accès à l\'emplacement suivant de l\'éditeur.",
	],
	"vs/platform/undoRedo/common/undoRedoService": [
		"Les fichiers suivants ont été fermés et modifiés sur le disque : {0}.",
		"Les fichiers suivants ont été modifiés de manière incompatible : {0}.",
		"Impossible d\'annuler \'{0}\' dans tous les fichiers. {1}",
		"Impossible d\'annuler \'{0}\' dans tous les fichiers. {1}",
		"Impossible d\'annuler \'{0}\' dans tous les fichiers, car des modifications ont été apportées à {1}",
		"Impossible d\'annuler \'{0}\' dans tous les fichiers, car une opération d\'annulation ou de rétablissement est déjà en cours d\'exécution sur {1}",
		"Impossible d\'annuler \'{0}\' dans tous les fichiers, car une opération d\'annulation ou de rétablissement s\'est produite dans l\'intervalle",
		"Souhaitez-vous annuler \'{0}\' dans tous les fichiers ?",
		"Annuler dans {0} fichiers",
		"Annuler ce fichier",
		"Annuler",
		"Impossible d\'annuler \'{0}\', car une opération d\'annulation ou de rétablissement est déjà en cours d\'exécution.",
		"Voulez-vous annuler \'{0}\' ?",
		"Oui",
		"Non",
		"Impossible de répéter \'{0}\' dans tous les fichiers. {1}",
		"Impossible de répéter \'{0}\' dans tous les fichiers. {1}",
		"Impossible de répéter \'{0}\' dans tous les fichiers, car des modifications ont été apportées à {1}",
		"Impossible de rétablir \'{0}\' dans tous les fichiers, car une opération d\'annulation ou de rétablissement est déjà en cours d\'exécution pour {1}",
		"Impossible de rétablir \'{0}\' dans tous les fichiers, car une opération d\'annulation ou de rétablissement s\'est produite dans l\'intervalle",
		"Impossible de rétablir \'{0}\', car une opération d\'annulation ou de rétablissement est déjà en cours d\'exécution.",
	],
	"vs/platform/workspaces/common/workspaces": [
		"Espace de travail de code",
	]
});