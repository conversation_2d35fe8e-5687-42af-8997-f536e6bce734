{"name": "route-with-plugin-orchestration", "uri": "/hello", "upstream": {"type": "roundrobin", "nodes": [{"host": "*************", "port": 1980, "weight": 1}]}, "script": {"rule": {"11-22-33-44": [["code == 503", "yy-uu-ii-oo"], ["", "vv-cc-xx-zz"]]}, "conf": {"11-22-33-44": {"name": "limit-count", "conf": {"count": 2, "time_window": 60, "rejected_code": 503, "key": "remote_addr", "policy": "local"}}, "yy-uu-ii-oo": {"name": "response-rewrite", "conf": {"body": "request has been limited", "headers": {"X-limit-status": "limited"}}}, "vv-cc-xx-zz": {"name": "response-rewrite", "conf": {"body": "normal request", "headers": {"X-limit-status": "normal"}}}}}}