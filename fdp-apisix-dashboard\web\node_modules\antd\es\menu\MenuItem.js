import _extends from "@babel/runtime/helpers/esm/extends";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _createClass from "@babel/runtime/helpers/esm/createClass";
import _inherits from "@babel/runtime/helpers/esm/inherits";
import _createSuper from "@babel/runtime/helpers/esm/createSuper";

var __rest = this && this.__rest || function (s, e) {
  var t = {};

  for (var p in s) {
    if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  }

  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};

import * as React from 'react';
import { Item } from 'rc-menu';
import toArray from "rc-util/es/Children/toArray";
import classNames from 'classnames';
import MenuContext from './MenuContext';
import Tooltip from '../tooltip';
import { SiderContext } from '../layout/Sider';
import { isValidElement, cloneElement } from '../_util/reactNode';

var MenuItem = /*#__PURE__*/function (_React$Component) {
  _inherits(MenuItem, _React$Component);

  var _super = _createSuper(MenuItem);

  function MenuItem() {
    var _this;

    _classCallCheck(this, MenuItem);

    _this = _super.apply(this, arguments);

    _this.renderItem = function (_ref) {
      var siderCollapsed = _ref.siderCollapsed;
      var _this$props = _this.props,
          level = _this$props.level,
          className = _this$props.className,
          children = _this$props.children,
          rootPrefixCls = _this$props.rootPrefixCls;

      var _a = _this.props,
          title = _a.title,
          icon = _a.icon,
          danger = _a.danger,
          rest = __rest(_a, ["title", "icon", "danger"]);

      return /*#__PURE__*/React.createElement(MenuContext.Consumer, null, function (_ref2) {
        var _classNames;

        var inlineCollapsed = _ref2.inlineCollapsed,
            direction = _ref2.direction;

        var _a;

        var tooltipTitle = title;

        if (typeof title === 'undefined') {
          tooltipTitle = level === 1 ? children : '';
        } else if (title === false) {
          tooltipTitle = '';
        }

        var tooltipProps = {
          title: tooltipTitle
        };

        if (!siderCollapsed && !inlineCollapsed) {
          tooltipProps.title = null; // Reset `visible` to fix control mode tooltip display not correct
          // ref: https://github.com/ant-design/ant-design/issues/16742

          tooltipProps.visible = false;
        }

        var childrenLength = toArray(children).length;
        return /*#__PURE__*/React.createElement(Tooltip, _extends({}, tooltipProps, {
          placement: direction === 'rtl' ? 'left' : 'right',
          overlayClassName: "".concat(rootPrefixCls, "-inline-collapsed-tooltip")
        }), /*#__PURE__*/React.createElement(Item, _extends({}, rest, {
          className: classNames((_classNames = {}, _defineProperty(_classNames, "".concat(rootPrefixCls, "-item-danger"), danger), _defineProperty(_classNames, "".concat(rootPrefixCls, "-item-only-child"), (icon ? childrenLength + 1 : childrenLength) === 1), _classNames), className),
          title: title
        }), cloneElement(icon, {
          className: classNames(isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', "".concat(rootPrefixCls, "-item-icon"))
        }), _this.renderItemChildren(inlineCollapsed)));
      });
    };

    return _this;
  }

  _createClass(MenuItem, [{
    key: "renderItemChildren",
    value: function renderItemChildren(inlineCollapsed) {
      var _this$props2 = this.props,
          icon = _this$props2.icon,
          children = _this$props2.children,
          level = _this$props2.level,
          rootPrefixCls = _this$props2.rootPrefixCls; // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span
      // ref: https://github.com/ant-design/ant-design/pull/23456

      if (!icon || isValidElement(children) && children.type === 'span') {
        if (children && inlineCollapsed && level === 1 && typeof children === 'string') {
          return /*#__PURE__*/React.createElement("div", {
            className: "".concat(rootPrefixCls, "-inline-collapsed-noicon")
          }, children.charAt(0));
        }

        return children;
      }

      return /*#__PURE__*/React.createElement("span", null, children);
    }
  }, {
    key: "render",
    value: function render() {
      return /*#__PURE__*/React.createElement(SiderContext.Consumer, null, this.renderItem);
    }
  }]);

  return MenuItem;
}(React.Component);

export { MenuItem as default };
MenuItem.isMenuItem = true;