{"name": "node-import-ts", "version": "1.0.5", "description": "", "main": "lib/index.js", "scripts": {"build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "prepublishOnly": "npm run build"}, "files": ["lib/index.js", "lib/compile.js"], "repository": {"type": "git", "url": "git+https://github.com/chenshuai2144/node-require-ts.git"}, "author": "chenshuai2144", "license": "ISC", "bugs": {"url": "https://github.com/chenshuai2144/node-require-ts/issues"}, "homepage": "https://github.com/chenshuai2144/node-require-ts#readme", "dependencies": {"@types/node": "^12.6.2", "import-fresh": "^3.1.0", "typescript": "^3.5.3"}}