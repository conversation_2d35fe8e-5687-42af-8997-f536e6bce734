{"css": {"properties": {"perspective": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/CSS/perspective", "support": {"chrome": [{"version_added": "36"}, {"prefix": "-webkit-", "version_added": "12"}], "chrome_android": [{"version_added": "36"}, {"prefix": "-webkit-", "version_added": "18"}], "edge": [{"version_added": "12"}, {"prefix": "-webkit-", "version_added": "12"}], "firefox": [{"version_added": "16"}, {"prefix": "-moz-", "version_added": "10"}, {"version_added": "49", "prefix": "-webkit-"}, {"prefix": "-webkit-", "version_added": "45", "flags": [{"type": "preference", "name": "layout.css.prefixes.webkit", "value_to_set": "true"}]}], "firefox_android": [{"version_added": "16"}, {"prefix": "-moz-", "version_added": "10"}, {"version_added": "49", "prefix": "-webkit-"}, {"prefix": "-webkit-", "version_added": "45", "flags": [{"type": "preference", "name": "layout.css.prefixes.webkit", "value_to_set": "true"}]}], "ie": {"version_added": "10"}, "opera": [{"version_added": "23"}, {"prefix": "-webkit-", "version_added": "15"}], "opera_android": [{"version_added": "24"}, {"prefix": "-webkit-", "version_added": "14"}], "safari": [{"version_added": "9"}, {"prefix": "-webkit-", "version_added": "4"}], "safari_ios": [{"version_added": "9"}, {"prefix": "-webkit-", "version_added": "2"}], "samsunginternet_android": [{"version_added": "3.0"}, {"prefix": "-webkit-", "version_added": "1.0"}], "webview_android": [{"version_added": "37"}, {"prefix": "-webkit-", "version_added": "3"}]}, "status": {"experimental": false, "standard_track": true, "deprecated": false}}}}}}