{"webextensions": {"api": {"pkcs11": {"getModuleSlots": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/pkcs11/getModuleSlots", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}, "status": {"experimental": false, "standard_track": false, "deprecated": false}}}, "installModule": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/pkcs11/installModule", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}, "status": {"experimental": false, "standard_track": false, "deprecated": false}}}, "isModuleInstalled": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/pkcs11/isModuleInstalled", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}, "status": {"experimental": false, "standard_track": false, "deprecated": false}}}, "uninstallModule": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions/API/pkcs11/uninstallModule", "support": {"chrome": {"version_added": false}, "edge": {"version_added": false}, "firefox": {"version_added": "58"}, "firefox_android": {"version_added": false}, "opera": {"version_added": false}, "safari": {"version_added": false}}, "status": {"experimental": false, "standard_track": false, "deprecated": false}}}}}}}