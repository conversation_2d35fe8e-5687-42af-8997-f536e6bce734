{"version": 3, "file": "selection.js", "sourceRoot": "", "sources": ["../../src/graph/selection.ts"], "names": [], "mappings": ";;;;;;AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAA;AAKtC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAA;AAE7B,MAAM,OAAO,gBAAiB,SAAQ,IAAI;IAA1C;;QAEU,aAAQ,GAAG,IAAI,OAAO,EAAiB,CAAA;QACvC,gBAAW,GAAG,IAAI,OAAO,EAAiB,CAAA;IA6OpD,CAAC;IA3OC,IAAc,aAAa;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;IAC/B,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,CACL,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI;YACnC,IAAI,CAAC,aAAa,CAAC,UAAU,KAAK,IAAI,CACvC,CAAA;IACH,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI,CAAA;IAC5C,CAAC;IAED,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;IAC3B,CAAC;IAED,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;IAC1B,CAAC;IAES,IAAI;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,CAAA;QAC/C,IAAI,CAAC,cAAc,EAAE,CAAA;IACvB,CAAC;IAES,cAAc;QACtB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QAC7D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;QACvD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAES,aAAa;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAA;QAC9D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;QAC5D,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;QACxD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;IAC7D,CAAC;IAES,gBAAgB,CAAC,EAAE,CAAC,EAAgC;QAC5D,IACE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC;YAC7B,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;gBACtB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC;gBAC1C,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAC5C;YACA,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAA;SACxB;aAAM;YACL,IAAI,CAAC,KAAK,EAAE,CAAA;SACb;IACH,CAAC;IAED,eAAe,CAAC,CAAwB,EAAE,MAAgB;QACxD,OAAO,CACL,CAAC,IAAI,CAAC,kBAAkB;YACxB,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,MAAM,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CACnC,CAAA;IACH,CAAC;IAES,eAAe,CAAC,EAAE,IAAI,EAA+B;QAC7D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAES,aAAa,CAAC,EAAE,CAAC,EAAE,IAAI,EAA6B;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAA;QAClC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACxC,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAA;YAE9C,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAA;aAChE;YAED,IAAI,CAAC,QAAQ,EAAE;gBACb,QAAQ,GAAG,OAAO,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAA;aAChE;SACF;QAED,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;gBAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;aACjB;iBAAM,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACrC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aAC9B;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;aACpB;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;aAClB;SACF;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAES,cAAc,CAAC,EAAE,CAAC,EAAE,IAAI,EAAwC;QACxE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE;gBACrE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;gBACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;aACjC;SACF;IACH,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA;IACzB,CAAC;IAED,UAAU,CAAC,IAAmB;QAC5B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAES,QAAQ,CAAC,KAAwC;QACzD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aAC5C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACZ,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAC/D;aACA,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,MAAM,CACJ,KAAwC,EACxC,UAAiC,EAAE;QAEnC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;aACtC;iBAAM;gBACL,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;aAC1C;SACF;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,QAAQ,CACN,KAAwC,EACxC,UAAoC,EAAE;QAEtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;QACnD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CACH,KAAyC,EACzC,UAAiC,EAAE;QAEnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAC7D,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM;QACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAA;SAClC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAA;SACnC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,eAAe,CAAC,CAAwB;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;SAC9B;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,gBAAgB;QACd,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,IAAI,CAAA;YACpC,OAAO;YACP,wBAAwB;YACxB,6CAA6C;YAC7C,8CAA8C;YAC9C,MAAM;YACN,MAAM;YACN,yCAAyC;YACzC,IAAI;SACL;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,iBAAiB;QACf,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC5B,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,KAAK,CAAA;SACtC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,KAAK,CAAA;IAC9C,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAA;QAClC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,eAAe;QACb,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAA;QACnC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,YAAY,CAAC,SAAyC;QACpD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,UAAU,CAAC,OAA2B;QACpC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QAC/B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,SAAS,CAAC,MAAyB;QACjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,OAAO,IAAI,CAAA;IACb,CAAC;IAGD,OAAO;QACL,IAAI,CAAC,aAAa,EAAE,CAAA;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;CACF;AAJC;IADC,IAAI,CAAC,OAAO,EAAE;+CAId"}