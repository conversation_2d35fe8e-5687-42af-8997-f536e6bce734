{"api": {"PositionSensorVRDevice": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionSensorVRDevice", "support": {"chrome": {"version_added": true, "notes": "The support in Chrome is currently experimental. To find information on Chrome's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Chrome</a> by <PERSON>."}, "chrome_android": {"version_added": false}, "edge": {"version_added": "79", "notes": "The support in Edge is currently experimental. To find information on Edge's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Edge</a> by <PERSON>."}, "firefox": {"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>", "flags": [{"type": "preference", "name": "dom.vr*"}]}, "firefox_android": [{"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>.", "flags": [{"type": "preference", "name": "dom.vr*"}]}, {"version_added": "44", "notes": "The <code>dom.vr*</code> prefs are enabled by default at this point, in Nightly/Aurora editions."}], "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": true}}, "getImmediateState": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionSensorVRDevice/getImmediateState", "support": {"chrome": {"version_added": true, "notes": "The support in Chrome is currently experimental. To find information on Chrome's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Chrome</a> by <PERSON>."}, "chrome_android": {"version_added": false}, "edge": {"version_added": "79", "notes": "The support in Edge is currently experimental. To find information on Edge's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Edge</a> by <PERSON>."}, "firefox": {"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>", "flags": [{"type": "preference", "name": "dom.vr*"}]}, "firefox_android": [{"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>.", "flags": [{"type": "preference", "name": "dom.vr*"}]}, {"version_added": "44", "notes": "The <code>dom.vr*</code> prefs are enabled by default at this point, in Nightly/Aurora editions."}], "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": true}}}, "getState": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionSensorVRDevice/getState", "support": {"chrome": {"version_added": true, "notes": "The support in Chrome is currently experimental. To find information on Chrome's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Chrome</a> by <PERSON>."}, "chrome_android": {"version_added": false}, "edge": {"version_added": "79", "notes": "The support in Edge is currently experimental. To find information on Edge's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Edge</a> by <PERSON>."}, "firefox": {"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>", "flags": [{"type": "preference", "name": "dom.vr*"}]}, "firefox_android": [{"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>.", "flags": [{"type": "preference", "name": "dom.vr*"}]}, {"version_added": "44", "notes": "The <code>dom.vr*</code> prefs are enabled by default at this point, in Nightly/Aurora editions."}], "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": true}}}, "resetSensor": {"__compat": {"mdn_url": "https://developer.mozilla.org/docs/Web/API/PositionSensorVRDevice/resetSensor", "support": {"chrome": {"version_added": true, "notes": "The support in Chrome is currently experimental. To find information on Chrome's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Chrome</a> by <PERSON>."}, "chrome_android": {"version_added": false}, "edge": {"version_added": "79", "notes": "The support in Edge is currently experimental. To find information on Edge's WebVR implementation status including supporting builds, check out <a href='http://blog.tojicode.com/2014/07/bringing-vr-to-chrome.html'>Bringing VR to Edge</a> by <PERSON>."}, "firefox": {"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>", "flags": [{"type": "preference", "name": "dom.vr*"}]}, "firefox_android": [{"version_added": "39", "notes": "The support for this feature is currently disabled by default in Firefox. To enable WebVR support in Firefox Nightly/Developer Edition, you can go to <code>about:config</code> and enable the <code>dom.vr*</code> prefs. A better option however is to install the <a href='http://www.mozvr.com/downloads/webvr-addon-0.1.0.xpi'>WebVR Enabler Add-on</a>, which does this for you and sets up other necessary parts of the <a href='https://developer.mozilla.org/docs/Web/API/WebVR_API/WebVR_environment_setup'>environment</a>.", "flags": [{"type": "preference", "name": "dom.vr*"}]}, {"version_added": "44", "notes": "The <code>dom.vr*</code> prefs are enabled by default at this point, in Nightly/Aurora editions."}], "ie": {"version_added": false}, "opera": {"version_added": false}, "opera_android": {"version_added": false}, "safari": {"version_added": false}, "safari_ios": {"version_added": false}, "samsunginternet_android": {"version_added": false}, "webview_android": {"version_added": false}}, "status": {"experimental": true, "standard_track": false, "deprecated": true}}}}}}