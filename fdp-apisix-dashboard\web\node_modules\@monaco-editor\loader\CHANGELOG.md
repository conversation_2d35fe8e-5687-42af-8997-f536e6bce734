## 1.2.0
###### *Oct 3, 2021*

- monaco-editor: update to the latest version (0.28.1)
- types: fix CancelablePromise type

## 1.1.1
###### *Jun 21, 2021*

- monaco-editor: update to the latest version (0.25.2)

## 1.1.0
###### *Jun 12, 2021*

- monaco-editor: update to the latest version (0.25.0)

## 1.0.1
###### *Mar 18, 2021*

- monaco-editor: update to the latest version (0.23.0)

## 1.0.0
###### *Jan 15, 2021*

🎉 First stable release

- utility: rename the main utility: monaco -> loader
- helpers: create (+ named export) `__getMonacoInstance` internal helper

## 0.1.3
###### *Jan 8, 2021*

- build: in `cjs` and `es` bundles `state-local` is marked as externam lib
- build: in `cjs` and `es` modules structure is preserved - `output.preserveModules = true`

## 0.1.2
###### *Jan 7, 2021*

- package: add jsdelivr source path

## 0.1.1
###### *Jan 7, 2021*

- lib: rename scripts name (from 'core' to 'loader')

## 0.1.0
###### *Jan 6, 2021*

🎉 First release
