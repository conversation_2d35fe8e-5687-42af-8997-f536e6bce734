{"name": "memoize-one", "version": "5.1.1", "description": "A memoization library which only remembers the latest invocation", "main": "dist/memoize-one.cjs.js", "types": "dist/memoize-one.d.ts", "module": "dist/memoize-one.esm.js", "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/alexreardon/memoize-one.git"}, "files": ["/dist", "/src"], "keywords": ["memoize", "memoization", "cache", "performance"], "dependencies": {}, "devDependencies": {"@types/jest": "^24.0.18", "@types/lodash.isequal": "^4.5.5", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "^2.0.0", "benchmark": "^2.1.4", "cross-env": "^5.2.0", "eslint": "6.2.0", "eslint-config-prettier": "^6.1.0", "eslint-plugin-jest": "^22.15.1", "eslint-plugin-prettier": "^3.1.0", "jest": "^24.9.0", "lodash.isequal": "^4.5.0", "prettier": "1.18.2", "rimraf": "3.0.0", "rollup": "^1.19.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript": "^1.0.1", "ts-jest": "^24.0.2", "ts-node": "^8.3.0", "tslib": "^1.10.0", "typescript": "^3.5.3"}, "config": {"prettier_target": "src/**/*.{ts,js,jsx,md,json} test/**/*.{ts,js,jsx,md,json}"}, "scripts": {"validate": "yarn lint && yarn typecheck", "test": "yarn jest", "typecheck": "yarn tsc --noEmit", "prettier:check": "yarn prettier --debug-check $npm_package_config_prettier_target", "prettier:write": "yarn prettier --write $npm_package_config_prettier_target", "lint:eslint": "eslint $npm_package_config_prettier_target", "lint": "yarn lint:eslint && yarn prettier:check", "build": "yarn build:clean && yarn build:dist && yarn build:typescript && yarn build:flow", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:dist": "rollup -c", "build:typescript": "tsc ./src/memoize-one.ts --emitDeclarationOnly --declaration --outDir ./dist", "build:flow": "cp src/memoize-one.js.flow dist/memoize-one.cjs.js.flow", "perf": "ts-node ./benchmarks/shallow-equal.ts", "prepublishOnly": "yarn build"}}